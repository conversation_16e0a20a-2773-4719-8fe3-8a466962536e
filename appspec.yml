version: 0.0

os: linux 

files:
  - source: /
    destination: /home/<USER>/apps/domino/admin/
    overwrite: true
file_exists_behavior: OVERWRITE
permissions:
  - object: /
    pattern: "**"
    owner: ubuntu
    group: ubuntu

hooks:
    AfterInstall:
      - location: scripts/after_install.sh
        timeout: 1000
        runas: ubuntu

  #ApplicationStart:
  #  - location: scripts/start_server.sh     
  #    timeout: 300
  #    runas: root