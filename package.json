{"name": "yum-admin", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "@types/bootstrap": "^4.5.0", "@types/chart.js": "^2.9.22", "@types/google-map-react": "^1.1.8", "@types/jest": "^24.9.1", "@types/jsonwebtoken": "^8.5.0", "@types/react": "^16.9.41", "@types/react-bootstrap-table": "^4.3.8", "@types/react-dom": "^16.9.8", "@types/react-redux": "^7.1.9", "@types/react-router-dom": "^5.1.5", "@types/react-select": "^3.0.14", "@types/react-tabs": "^2.3.2", "antd": "^5.8.4", "axios": "^0.19.2", "beauty-stars": "^1.1.0", "bootstrap": "^4.5.0", "chart.js": "^2.9.3", "chartjs-plugin-datalabels": "^1.0.0", "datatables": "^1.10.18", "datatables-buttons": "^1.0.3", "datatables-responsive": "^1.0.7", "datatables.net": "^1.10.25", "datatables.net-autofill-dt": "^2.3.5", "datatables.net-buttons": "^1.6.5", "datatables.net-buttons-dt": "^1.6.5", "datatables.net-dt": "^1.10.23", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "font-awesome": "^4.7.0", "google-map-react": "^1.1.7", "google-maps-react": "^2.0.6", "html-to-draftjs": "^1.5.0", "jquery": "^3.5.1", "jsonwebtoken": "^8.5.1", "jspdf": "^2.2.0", "jspdf-autotable": "^3.5.13", "jszip": "github:<PERSON><PERSON>/jszip", "jwt-decode": "^3.1.2", "moment": "^2.29.1", "pdfmake": "^0.1.70", "qrcode.react": "^1.0.1", "react": "^16.13.1", "react-bootstrap-table": "^4.3.1", "react-compound-timer": "^1.2.0", "react-content-loader": "^6.2.0", "react-data-table-component": "^6.9.3", "react-dom": "^16.13.1", "react-draft-wysiwyg": "^1.15.0", "react-excel-renderer": "^1.1.0", "react-geocode": "^0.2.1", "react-google-autocomplete": "^1.2.6", "react-google-maps": "^9.4.5", "react-loader-spinner": "^4.0.0", "react-paginate": "^7.1.5", "react-places-autocomplete": "^7.3.0", "react-redux": "^7.2.0", "react-render-html": "^0.6.0", "react-router-dom": "^5.2.0", "react-scripts": "3.4.1", "react-select": "^3.1.0", "react-tabs": "^3.1.1", "react-to-print": "^2.12.1", "react-toastify": "^6.2.0", "react-toggle": "^4.1.2", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "styled-components": "^5.1.1", "typescript": "^4.1.3", "voucher-code-generator": "^1.1.1", "xlsx": "^0.16.9", "xml-beautifier": "^0.5.0"}, "scripts": {"start": "react-scripts start", "build": "set \"GENERATE_SOURCEMAP=false\" && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/draft-js": "^0.11.9", "@types/draftjs-to-html": "^0.8.1", "@types/html-to-draftjs": "^1.4.0", "@types/lodash": "^4.14.165", "@types/node": "^12.19.6", "@types/qrcode.react": "^1.0.2", "@types/react-draft-wysiwyg": "^1.13.4", "@types/react-geocode": "^0.2.0", "@types/react-paginate": "^6.2.1", "@types/react-places-autocomplete": "^7.2.6", "@types/react-toggle": "^4.0.2"}}