{"version": 3, "sources": ["alliances.css", "menuStyle.css", "coupon.css", "fbrInvoice.css", "orders.css", "reports.css", "marketing.css", "style.css"], "names": [], "mappings": "AAAA,qBACI,wBAAyB,CACzB,YAAa,CACb,UAAW,CACX,eACF,CACA,+DAEE,WACF,CCNA,8HACE,wBACF,CAEA,YACE,UAAW,CACX,YAAa,CACb,wBAAyB,CACzB,kBAAmB,CACnB,WACF,CACF,yBACE,YACE,0BACF,CACF,CClBA,aACI,eAAgB,CAChB,qBAAsB,CACtB,UAAY,CACZ,kBAAmB,CACnB,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,iBAAkB,CAClB,WAAY,CACZ,gBACJ,CCXA,gBACI,UAAW,CACX,aAAc,CACd,UACF,CACA,UAEE,kBACF,CAKA,MACE,iBAAkB,CAClB,kBACF,CAEA,UACE,8BAA+B,CAC/B,sBACF,CAEA,SAEE,eAAgB,CAChB,kBACF,CACA,cACE,iBAAkB,CAClB,UAAY,CAEZ,UAAW,CAEX,gBAAiB,CACjB,oBAAqB,CACrB,cAAe,CACf,eAAgB,CAChB,iBACF,CAEA,aACE,kBACF,CAEA,cACE,UAAW,CACX,wBAAyB,CACzB,gBAAiB,CACjB,kBACF,CAEA,oCACE,kBACF,CAEA,kCAEE,cAAe,CACf,UAAW,CACX,eAAgB,CAChB,6BACF,CAEA,iBACE,gBAAiB,CACjB,+BAAgC,CAChC,kBAAmB,CACnB,eACF,CAEA,iBACE,4BAA8B,CAC9B,eACF,CACA,uBACE,iBACF,CAEA,+CAGE,kBACF,CAYA,kGAHE,cAMF,CAHA,SAEE,eACF,CAMA,gCAJE,UAAY,CACZ,cAeF,CAZA,eAEE,UAAW,CACX,WAAY,CACZ,QAAS,CACT,eAAgB,CAChB,kBAAmB,CAEnB,4BAA6B,CAC7B,aAAc,CACd,iBAAkB,CAClB,kBACF,CAEA,YACE,UACF,CACA,cACI,cAAe,CACf,kBACJ,CACA,cACE,UACF,CC/HF,iCACE,eAAiB,CACjB,WACF,CAEA,mBACE,qBACF,CAEA,gCACE,eAAiB,CACjB,SACF,CAEA,uCACE,eAAiB,CACjB,aACF,CAEA,sBACE,uBACF,CAGA,oCACE,YAAa,CACb,wBAAyB,CACzB,eAAgB,CAChB,cAAe,CACf,eAAgB,CAChB,oBACF,CAQA,yCACE,YACF,CAEA,4CACE,eAAgB,CAChB,eACF,CAEA,8CACE,UAAW,CACX,wBAAyB,CACzB,oBACF,CAEA,yCACE,oBAAuB,CAEvB,gBAAiB,CACjB,aAAc,CACd,qBAAsB,CACtB,qBACF,CAEA,8FAEE,oBACF,CAEA,yCACE,oCACE,YAAa,CACb,eAAgB,CAChB,cAAe,CACf,eAAgB,CAChB,oBAAqB,CACrB,iBAAkB,CAClB,0BAA2B,CAC3B,cAAe,CACf,kBAAmB,CACnB,eAAgB,CAChB,cACF,CACF,CAEA,8DAEE,YACF,CAEA,sBACE,WAAY,CACZ,SAAU,CACV,WAAY,CACZ,qBACF,CAEA,kBACE,YAAa,CACb,wBAAyB,CACzB,eACF,CACA,eACC,iBAED,CCxGA,uBACI,gCAKJ,CACA,2CALI,oBAAsB,CACtB,0BAA4B,CAC5B,kBAAmB,CACnB,UASJ,CAPA,oBACI,kCAA6C,CAI7C,iBAEJ,CAEA,cACI,SACJ,CCjBA,yCACI,YAAa,CACb,wBAAyB,CACzB,eAAgB,CAChB,cAAe,CACf,eAAgB,CAChB,oBACF,CACA,2DACE,cAAe,CACf,oBACF,CACA,8CACE,YACF,CACA,iDACE,eAAgB,CAChB,eACF,CACA,mDACE,UAAW,CACX,wBAAyB,CACzB,oBACF,CACA,8CACE,oBAAuB,CAEvB,gBAAiB,CACjB,aAAc,CACd,qBAAsB,CACtB,qBACF,CACA,wGACE,oBACF,CACA,yCACE,yCACE,YAAa,CACb,eAAgB,CAChB,cAAe,CACf,eAAgB,CAChB,oBAAqB,CACrB,iBAAkB,CAClB,0BAA2B,CAC3B,cAAe,CACf,kBAAmB,CACnB,eAAgB,CAChB,cACF,CACF,CACA,wEACE,YACF,CACA,+CACE,cACF,CCxDF,aACI,UAAW,CACX,WAAY,CACZ,eACJ", "file": "main.a25aacd6.chunk.css", "sourcesContent": ["div.DraftEditor-root {\n    border: 1px solid #eaeaea;\n    height: 150px;\n    padding: 1%;\n    overflow-y: auto;\n  }\n  div.DraftEditor-editor<PERSON><PERSON><PERSON>,\n  div.public-DraftEditor-content {\n    height: 100%;\n  }", ".custom-classname.react-toggle--checked .react-toggle-track {\n    background-color: #20A5D6;\n  }\n  .custom-classname.react-toggle--checked .react-toggle-track:hover {\n    background-color: #20A5D6;\n  }\n\n  .search-box{\n    padding: 2%;\n    display: flex;\n    border: 1px solid #EAEAEA;\n    align-items: center;\n    height: 40px;\n  }\n@media (min-width: 576px){\n  .nut-dialog {\n    max-width: 1200px !important;\n  }\n}", ".tooltip-btn {\n    font-weight: 700;\n    font-family: monospace;\n    color: white;\n    background: #20A5d6;\n    width: 20px;\n    height: 20px;\n    text-align: center;\n    border-radius: 50%;\n    float: right;\n    margin-left: 15px;\n}", ".clearfix:after {\n    content: \"\";\n    display: table;\n    clear: both;\n  }\n  .clearfix{\n    /* padding: 15px; */\n    margin-bottom: 10px;\n  }\n  /* a {\n    color: #5D6975;\n    text-decoration: underline;\n  } */\n  #logo {\n    text-align: center;\n    margin-bottom: 15px;\n  }\n  \n  #logo img {\n    -webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */\n    filter: grayscale(100%);\n  }\n  \n  #project {\n    /* float: left; */\n    margin-top: 10px;\n    margin-bottom: 10px;\n  }\n  #project span {\n    font-family: Arial;\n    color: black;\n    /* text-align: right; */\n    width: 52px;\n    /* margin-right: 10px; */\n    margin-left: 10px;\n    display: inline-block;\n    font-size: 14px;\n    text-align: left;\n    margin-right: 30px;\n  }\n  \n  #project div {\n    white-space: nowrap;        \n  }\n  \n  .invoicetable {\n    width: 100%;\n    border-collapse: collapse;\n    border-spacing: 0;\n    margin-bottom: 20px;\n  }\n  \n  .invoicetable tr:nth-child(2n-1) td {\n    background: #F5F5F5;\n  }\n  \n  .invoicetable th,\n  .invoicetable td {\n    font-size: 12px;\n    color:black;\n    text-align: left;\n    text-overflow: unset !important;\n  }\n  \n  .invoicetable th {\n    padding: 5px 10px;\n    border-bottom: 1px solid #C1CED9;\n    white-space: nowrap;        \n    font-weight: normal;\n  }\n  \n  .invoicetable td {\n    white-space: normal !important;\n    text-align: left;\n  }\n  .invoicetable td ul li{\n    margin-left: -15px;\n  }\n  \n  .invoicetable td.service,\n  .invoicetable td.desc {\n    \n    vertical-align: top;\n  }\n  \n  .invoicetable td.unit,\n  .invoicetable td.qty,\n  .invoicetable td.total {\n    font-size: 13px;\n  }\n  \n  .invoicetable td.grand {\n    font-size: 13px;\n    /* border-top: 1px solid #5D6975;; */\n  }\n  #notices{\n    font-size: 13px;\n    margin-top: 10px;\n  }\n  #notices .notice {\n    color: black;\n    font-size: 13px;\n  }\n  \n  .invoicefooter {\n    color: black;\n    width: 100%;\n    height: 30px;\n    bottom: 0;\n    margin-top: 15px;\n    margin-bottom: 15px;\n    font-size: 13px;\n    border-top: 1px solid #C1CED9;\n    padding: 8px 0;\n    text-align: center;\n    white-space: normal;\n  }\n  \n  td.discount{\n    color: black\n  }\n  .heading_text{\n      font-size: 12px;\n      white-space: normal;\n  }\n  .change_color{\n    color: black;\n  }", ".td-column-function-even-example {\n  font-weight: bold;\n  color: green;\n}\n\n.tr-pending-orders {\n  background-color: yellow;\n}\n\n.td-column-function-odd-example {\n  font-weight: bold;\n  color: red;\n}\n\n.td-column-function-aggregator-example {\n  font-weight: bold;\n  color: rgb(236, 109, 130);\n}\n\n.tr-transfered-orders {\n  background-color: orange;\n}\n\n/* pagination container for the coupons */\n.total-orders .pagination-container {\n  display: flex;\n  justify-content: flex-end;\n  list-style: none;\n  cursor: pointer;\n  margin-top: 20px;\n  text-decoration: none;\n}\n\n.pagination-container li,\n.pagination-container li a :hover {\n  cursor: pointer;\n  text-decoration: none;\n}\n\n.total-orders .react-bs-table-pagination {\n  display: none;\n}\n\n.total-orders .dashboard-counts .title span {\n  font-size: 1.1em;\n  font-weight: 700;\n}\n\n.total-orders .pagination-container .active a {\n  color: #fff;\n  background-color: #20A5D6;\n  border-color: #20A5D6;\n}\n\n.total-orders .pagination-container li a {\n  padding: 0.5rem 0.75rem;\n  /* margin-left: -1px; */\n  line-height: 1.25;\n  color: #20A5D6;\n  background-color: #fff;\n  border: 1px solid #ddd;\n}\n\n.total-orders .pagination-container li a:hover,\n.total-orders .pagination-container li a:focus {\n  text-decoration: none;\n}\n\n@media only screen and (max-width: 767px) {\n  .total-orders .pagination-container {\n    display: flex;\n    list-style: none;\n    cursor: pointer;\n    margin-top: 20px;\n    text-decoration: none;\n    position: relative;\n    justify-content: flex-start;\n    flex-wrap: wrap;\n    align-items: center;\n    overflow: hidden;\n    padding-left: 0;\n  }\n}\n\n.total-orders .previous.disabled,\n.total-orders .next.disabled {\n  display: none;\n}\n\n.input-dominos-search {\n  float: right;\n  width: 31%;\n  padding: 3px;\n  border: 1px solid #ddd;\n}\n\n.bottomPagination{\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 12px;\n}\n.topPagination{\n position: absolute;\n  /* z-index: 990; */\n}", ".reports.badge-success{\n    background-color: green !important;\n    color: #FFF !important;\n    padding: 3px 10px !important;\n    border-radius: 15px;\n    width: 80px;\n}\n.reports.badge-fail{\n    background-color: rgb(160, 32, 32) !important;\n    color: #FFF !important;\n    padding: 3px 10px !important;\n    border-radius: 15px;\n    text-align: center;\n    width: 80px;\n}\n\n.nps-question {\n    width: 20%;\n}", "/* pagination container for the coupons */\n.marketing-coupons .pagination-container{\n    display: flex;\n    justify-content: flex-end;\n    list-style: none;\n    cursor: pointer;\n    margin-top: 20px;\n    text-decoration: none;\n  }\n  .pagination-container li, .pagination-container li a :hover{\n    cursor: pointer;\n    text-decoration: none;\n  }\n  .marketing-coupons .react-bs-table-pagination{\n    display: none;\n  }\n  .marketing-coupons .dashboard-counts .title span{\n    font-size: 1.1em;\n    font-weight: 700;\n  }\n  .marketing-coupons .pagination-container .active a{\n    color: #fff;\n    background-color: #20A5D6;\n    border-color: #20A5D6;\n  }\n  .marketing-coupons .pagination-container li a {\n    padding: 0.5rem 0.75rem;\n    /* margin-left: -1px; */\n    line-height: 1.25;\n    color: #20A5D6;\n    background-color: #fff;\n    border: 1px solid #ddd;\n  }\n  .marketing-coupons .pagination-container li a:hover, .marketing-coupons .pagination-container li a:focus{\n    text-decoration: none;\n  }\n  @media only screen and (max-width: 767px) {\n    .marketing-coupons .pagination-container {\n      display: flex;\n      list-style: none;\n      cursor: pointer;\n      margin-top: 20px;\n      text-decoration: none;\n      position: relative;\n      justify-content: flex-start;\n      flex-wrap: wrap;\n      align-items: center;\n      overflow: hidden;\n      padding-left: 0;\n    }\n  }\n  .marketing-coupons .previous.disabled, .marketing-coupons .next.disabled {\n    display: none;\n  }\n  .marketing-coupons .card-body .form-group .btn{\n    padding: 4% 20%;\n  }\n", ".color-input {\n    width: 100%;\n    border: none;\n    margin: 15px 0px 0px 0px\n}"]}