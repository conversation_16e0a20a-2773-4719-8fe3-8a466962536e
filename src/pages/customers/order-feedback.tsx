import React, { Component } from 'react';
import { Redirect } from 'react-router-dom'
import { BootstrapTable, TableHeaderColumn } from 'react-bootstrap-table';
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import { connect } from 'react-redux'
import { logoutUser } from '../../redux'
import { CustomerProps } from '../../interfaces/customers';
import { orderReviewList } from '../../redux/actions/customerAction';
import ReactPaginate from 'react-paginate';

function dateFormatter(cell: any, row: any) {
    return (
        <span> { new Date(cell).toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' }) }</span>
    )
}
function emojiFormatter(cell: any, row: any) {

    const emoji = cell === 1 ? '😡' : cell === 2 ? '😐' : cell === 3 ? '😊' : cell === 4 ? '😍' : '';

    return (
        <span>{emoji}</span>
    )
}

function feedbackFormatter(cell: any, row: any, props: any) {
    return <FeedbackFormatter row={row} />;
}

class FeedbackFormatter extends Component<{ row: any }, { }> {
    constructor(readonly props: any) {
        super(props);
        
    }
    render() {
      const { row } = this.props;
      return (
        <div>
            {
                row.comment.length > 30 ?
                    <button title="View Feedback" className="btn btn-outline-primary" data-toggle="modal" data-target={`#ViewFeedbackMessage${row.id}`}>
                        View Feedback
                    </button>
                :
                    <span>
                        {row.comment}
                    </span>
            }
            <div id={`ViewFeedbackMessage${row.id}`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                <div role="document" className="modal-dialog">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h4 id="exampleModalLabel" className="modal-title">
                                Customer Feedback
                            </h4>
                            <button type="button" data-dismiss="modal" aria-label="Close" className="close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div className="modal-body">
                            <div className="form-row">
                                <div className="form-group col-md-12">
                                    <h5 id="txt">Feedback</h5>
                                    <textarea name="feedbackMessage" id="round2" disabled style={{ width: '100%', height: '100px' }}>
                                    {row.comment}
                                    </textarea>
                                </div>
                            </div>
                        </div>
                        <div className="modal-footer">
                            <button type="button" data-dismiss="modal" className="btn btn-danger">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
      );
    }
}

class CustomerOrderFeedback extends Component<CustomerProps, { currentPage: number, sizePerPage: number, limit: number }> {
    constructor(readonly props: any) {
        super(props);
        this.state = {
            currentPage: 1,
            sizePerPage: 10,
            limit: 10
        }
    }
    componentDidMount() {
        this.props.OrderReviewList(1, this.state.limit);
        document.title = "DominosCMS | Customer Order Feedback"
    }
    handlePageClick = (e: any) => {
        const selectedPage = e.selected;
        this.props.OrderReviewList(selectedPage + 1, this.state.limit);
    };
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const options: any = {
            sizePerPage: 10,
            page: 1,
            pageStartIndex: 1, 
            paginationSize: 3,  
            hideSizePerPage: true, 
            noDataText: 'Order Feedback Not Found'
        };
        return (
            <div className="page">
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Customer Order Feedback</h4>
                                </div>
                            </div>
                        </header>
                        <section className="tables">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col">
                                        <div className="card">
                                            <div className="card-body text-capitalize">
                                                <BootstrapTable version="4" data={this.props.data} search={true} options={options} exportCSV={true} csvFileName='feedbacks.csv' hover>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='id' csvHeader='#' width='100' dataSort={true} isKey>#</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='login_name' csvHeader='Name' width='100' columnTitle>Name</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='phone_number' csvHeader='Contact' width='150' columnTitle>Contact</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='order_id' csvHeader='Order' width='100' columnTitle>order ID</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='store_name' csvHeader='Store' width='150' columnTitle>Store Name</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='order_channel' csvHeader='Platform' width='100' columnTitle>Platform</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='delivery_status' csvHeader='Status' width='100' columnTitle>Status</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='emoji' dataFormat={emojiFormatter} csvHeader='Emoji' width='100' columnTitle>Emoji</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='comment' dataFormat={feedbackFormatter} csvHeader='Comment' width='200' columnTitle>Comment</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='date_created' dataFormat={dateFormatter} csvHeader='Date' width='150' columnTitle>Date</TableHeaderColumn>
                                                </BootstrapTable>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {
                                    (this.props.count > 0) ?
                                    <div className="row" >
                                        <div className="col-2">
                                            <div className="d-flex d-flex justify-content-start align-items-center">
                                                <select className="form-control" onChange={(e) => { this.setState({ limit: Number(e.target.value) }); this.props.OrderReviewList(1, e.target.value) }}>
                                                    <option value="10">10</option>
                                                    <option value="20">20</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                    <option value="100">500</option>
                                                    <option value="100">1000</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div className="col">
                                            <div className="d-flex d-flex justify-content-end align-items-center">
                                                <p className='mr-3'><b className="text-primary">Total Data</b> &nbsp; | &nbsp; <span>{this.props.count}</span></p>
                                                <ReactPaginate
                                                    initialPage={Number(this.state.currentPage)-1}
                                                    breakLinkClassName={'page-link'}
                                                    pageClassName={'page-item'}
                                                    pageLinkClassName={'page-link'}
                                                    previousClassName={'page-item'}
                                                    previousLinkClassName={'page-link'}
                                                    nextClassName={'page-item'}
                                                    nextLinkClassName={'page-link'}
                                                    previousLabel={"prev"}
                                                    nextLabel={"next"}
                                                    breakLabel={"...."}
                                                    breakClassName={"page-item"}
                                                    pageCount={this.props.count/this.state.limit}
                                                    marginPagesDisplayed={1}
                                                    pageRangeDisplayed={2}
                                                    disableInitialCallback={true}
                                                    onPageChange={this.handlePageClick}
                                                    containerClassName={"pagination"}
                                                    activeClassName={"active"} 
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    :
                                    null
                                }
                            </div>
                        </section >
                        <Footer />
                    </div>
                </div>
            </div>
        );
    }
}
;
const mapStateToProps = (state: any) => {
    return {
        data: state.customer.orderReviewList,
        count: state.customer.reviewsCount
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        OrderReviewList: function (page: number, limit: number) {
            dispatch(orderReviewList(page, limit))
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(CustomerOrderFeedback);