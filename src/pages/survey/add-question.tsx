import React, { Component } from 'react';
import { AddQuestionsProps, AddQuestionState } from '../../interfaces/survey'
import { connect } from 'react-redux'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import jwt from 'jsonwebtoken'
import CheckChanges from '../../components/confirmOnLeave'
import { addQuestion, logoutUser } from '../../redux'
import { Link, Redirect } from 'react-router-dom';
import { secretKey } from '../../secret';
import "./style.css"
class AddQuestion extends Component<AddQuestionsProps, AddQuestionState> {
    constructor(props: any) {
        super(props);
        this.state = {
            question: "",
            first_rating_label: "",
            last_rating_label: "",
            options: [
                {
                    option: "",
                    priority: "",
                    color:""
                }
            ]
        }
        this.handleSaveBtnClick = this.handleSaveBtnClick.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
    }
    componentDidMount(): void {
        document.title = "DominosCMS | NPS"
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        this.setState({
            [event.target.name]: event.target.value
        });
    }
    isQuestionReady = () => {
        const { question, options } = this.state;
        if(question == "") {
            return false;
        }
        for (let i = 0; i < options.length; i++) {
            if (options[i]["option"] == "" || options[i]["priority"] == "") {
                return false;
            }
        }
        return true;
    }
    handleOptionInputChange = (e: any, index: any) => {
        const { name, value } = e.target;
        const list: any = this.state.options;
        list[index][name] = value;
        this.setState({ options: list });
    };
    // handle click event of the Remove button
    handleRemoveClick = (index: any) => {
        const list = this.state.options;
        list.splice(index, 1);
        this.setState({ options: list });
    };
    handleAddClick = (i: any) => {
        const list: any = this.state.options;
        if (list[i]["option"] == "" || list[i]["priority"] == "") {
            alert("Please fill all mandatory option fields")
        } else {
            let obj: any = {
                option: "",
                priority: "",
                color:""
            }
            list.push(obj)
            this.setState({ options: list })
        }
    };
    handleSaveBtnClick = (event: any) => {
        let { question, options, first_rating_label, last_rating_label } = this.state;
        let data: any = {
            question: question,
            first_rating_label,
            last_rating_label,
            options: JSON.stringify(options)
        }
        if (options[options.length - 1]["option"] !== "" && options[options.length - 1]["priority"] !== "") {
            this.props.addQuestion(data);
        } else {
            alert("Please fill all mandatory option fields")
        }
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        let { options } = this.state;
        return (
            <div className="page">
                <CheckChanges path="/add-nps" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/* <!-- Page Header--> */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">NPS Management</h4>
                                </div>
                            </div>
                        </header>
                        {/*  Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/nps" className="text-primary">NPS</Link></li>
                                <li className="breadcrumb-item active">Add Question</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Question <span className="text-danger">*</span></label>
                                                            <input id="question" type="text" name="question" required data-msg="Please enter question" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">First Rating Label </label>
                                                            <input id="first_rating_label" type="text" name="first_rating_label" required data-msg="Enter first rating label" className="input-material" value={this.state.first_rating_label} onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Last Rating Label </label>
                                                            <input id="last_rating_label" type="text" name="last_rating_label" required data-msg="Enter last rating label" className="input-material" value={this.state.last_rating_label} onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            {options.map((x: any, ind: any) => {
                                                                return (
                                                                    <div key={ind}>
                                                                        <div className="row">
                                                                            <div className="col-12 col-md-4">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Option {ind + 1}<span className="text-danger">*</span></label>
                                                                                    <input id="option" type="text" name="option" value={this.state.options[ind].option} required className="input-material" onChange={e => this.handleOptionInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-12 col-md-4">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Priority<span className="text-danger">*</span></label>
                                                                                    <input id="priority" type="text" name="priority" value={this.state.options[ind].priority} required className="input-material" onChange={e => this.handleOptionInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-12 col-md-4">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Color (Hex code)</label>
                                                                                    <input id="color" type="color" name="color" value={this.state.options[ind].color} required className="color-input" onChange={e => this.handleOptionInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div className='row'>
                                                                            <div className="col d-flex justify-content-end">
                                                                                <div className="form-group">
                                                                                    {options.length !== 1 &&
                                                                                        <button className="btn btn-sm btn-outline-danger" onClick={() => this.handleRemoveClick(ind)}><i className="fa fa-trash"></i></button>}
                                                                                    {options.length - 1 === ind && <button className="btn btn-sm btn-primary ml-2 float-right" onClick={() => this.handleAddClick(ind)}><i className="fa fa-plus"></i></button>}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="form-group float-right mt-3">
                                                    <button onClick={() => { this.props.history.push("/nps") }} className="btn btn-danger mr-2">Cancel</button>
                                                    <button className='btn btn-primary' disabled={!this.isQuestionReady()} onClick={this.handleSaveBtnClick}>Save</button>
                                                </div>
                                            </div>
                                        </div >
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div>
        );
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        addQuestion: (data: any) => {
            dispatch(addQuestion(data));
        }
    }
}
export default connect(null, mapDispatchToProps)(AddQuestion);