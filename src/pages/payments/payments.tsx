import React, { Component } from 'react';
import { Redirect } from 'react-router-dom';
import { BootstrapTable, TableHeaderColumn } from 'react-bootstrap-table';
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import { connect } from 'react-redux'
import { paymentsList, logoutUser } from '../../redux'
import { PaymentProps } from '../../interfaces/payment';
import moment from 'moment';
import {currency} from '../../client-config'
import ReactPaginate from 'react-paginate';
import { paymentByOrderId, paymentPageReset } from '../../redux/actions/paymentAction';

function priceFormatter(cell: any) {
    return `${currency} ${cell}`;
}
class Payments extends Component<PaymentProps, { currentPage: number, sizePerPage: number, limit: number, searchOrder: number, isOrderSearch: boolean }> {
    constructor(readonly props: any) {
        super(props);
        this.state = {
            currentPage: 1,
            sizePerPage: 10,
            limit: 10,
            searchOrder: 0,
            isOrderSearch: false
        }
    }
    componentDidMount() {
        this.props.paymentsList(1, this.state.limit);
        document.title = "DominosCMS | Payments"
    }
    handlePageClick = (e: any) => {
        const selectedPage = e.selected;
        this.props.paymentsList(selectedPage + 1, this.state.limit);
    };

    handleOrderSearch = (e: any) => {
        this.setState({ searchOrder: e.target.value });
        if (e.target.value === "") {
            this.props.paymentsList(1, this.state.limit);
        }
    }

    resetPage = () => {
        this.setState({ searchOrder: 0, currentPage: 1, limit: 10 });
        this.props.paymentsList(1, 10)
        this.props.resetPaymentPage();
    }

    searchByOrderId = () => {
        this.setState({ isOrderSearch: true })
        this.props.paymentByOrderId(this.state.searchOrder);
    }
    
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const options: any = {
            sizePerPage: 10, // which size per page you want to locate as default
            page: 1,
            pageStartIndex: 1, // where to start counting the pages
            paginationSize: 3,  // the pagination bar size.
            hideSizePerPage: true, //You can hide the dropdown for sizePerPage
            noDataText: 'Payments Not Found'
        };
        return (
            <div className="page">
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/* <!-- Page Header--> */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Payments Management</h4>
                                </div>
                            </div>
                        </header>
                        <section className="tables">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col">
                                        <div className="card">
                                            <div className="card-body">                                            
                                                <div className="row">
                                                    {/* <input type="text" className="form-control" placeholder="Search By Order Id" onChange={(e) => this.props.paymentsList(1, this.state.limit, e.target.value)} /> */}
                                                    <div className='col'>
                                                        <div className="form-group">
                                                            <input id="orderId" type="number" onWheel={(e: any) => e.target.blur()} value={this.state.searchOrder == 0 ? '' : this.state.searchOrder} name="orderId" required placeholder="Search by order id" className="input-material" onChange={this.handleOrderSearch} />
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-md-3 col-12 float-right mt-1">
                                                        <div className="form-group">
                                                            <button onClick={this.searchByOrderId} disabled={this.state.searchOrder <= 0} className="btn btn-primary btn-block">Search Order</button>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-md-3 col-12 float-right mt-1">
                                                        <div className="form-group">
                                                            <button onClick={this.resetPage} className="btn btn-reset btn-block">Reset</button>
                                                        </div>
                                                    </div>
                                                    {/* <button className="btn btn-primary" onClick={() => this.props.paymentsList(1, this.state.limit)}>Search By Order Id</button>
                                                    <button className="btn btn-reset" onClick={() => this.props.paymentsList(1, this.state.limit)}>Refresh</button> */}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col">
                                        <div className="card">
                                            <div className="card-body">   
                                            {
                                                (this.props.paymentOrder.length > 0 || this.props.data.length > 0) ?
                                                    <BootstrapTable data={this.props.paymentOrder.length > 0 ? this.props.paymentOrder : this.props.data} search={true} options={options} exportCSV={true} csvFileName='payments.csv' hover>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10 }} dataField='payment_id' csvHeader='#' dataSort={true} isKey>#</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10 }} dataField='order_id' csvHeader='OrderId' columnTitle>OrderId</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='cardOrderId' csvHeader='Transaction Id' columnTitle>Transaction Id</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='payment_method' csvHeader='Payment Method' columnTitle>Payment Method</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='store_name' csvHeader='Store Name' columnTitle>Store Name</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='branch_code' csvHeader='Branch Code' columnTitle>Branch Code</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='first_name' csvHeader='Name' columnTitle>Customer</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='phone_number' csvHeader='Phone' columnTitle>Phone</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='payment_amount' dataFormat={priceFormatter} csvHeader='Amount' columnTitle>Amount</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='payment_status' dataFormat={statusFormatter} csvHeader='Status' columnTitle>Payment Status</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={dateFormatter} dataField='date_modified' csvHeader='Date' columnTitle>DateTime</TableHeaderColumn>
                                                    </BootstrapTable>
                                                :
                                                <div className="alert alert-danger text-center" role="alert">
                                                    <strong>No Payments Found</strong>
                                                </div>
                                            }                                         
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {
                                    (this.props.count > 0 && this.props.paymentOrder.length == 0) ?
                                    <div className="row" >
                                        <div className="col-2">
                                            <div className="d-flex d-flex justify-content-start align-items-center">
                                                <select className="form-control" onChange={(e) => { this.setState({ limit: Number(e.target.value) }); this.props.paymentsList(1, e.target.value) }}>
                                                    <option value="10">10</option>
                                                    <option value="20">20</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                    <option value="100">500</option>
                                                    <option value="100">1000</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div className="col">
                                            <div className="d-flex d-flex justify-content-end align-items-center">
                                                <p className='mr-3'><b className="text-primary">Total Data</b> &nbsp; | &nbsp; <span>{this.props.count}</span></p>
                                                <ReactPaginate
                                                    initialPage={Number(this.state.currentPage)-1}
                                                    breakLinkClassName={'page-link'}
                                                    pageClassName={'page-item'}
                                                    pageLinkClassName={'page-link'}
                                                    previousClassName={'page-item'}
                                                    previousLinkClassName={'page-link'}
                                                    nextClassName={'page-item'}
                                                    nextLinkClassName={'page-link'}
                                                    previousLabel={"prev"}
                                                    nextLabel={"next"}
                                                    breakLabel={"...."}
                                                    breakClassName={"page-item"}
                                                    pageCount={this.props.count/this.state.limit}
                                                    marginPagesDisplayed={1}
                                                    pageRangeDisplayed={2}
                                                    disableInitialCallback={true}
                                                    onPageChange={this.handlePageClick}
                                                    containerClassName={"pagination"}
                                                    activeClassName={"active"} 
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    :
                                    null
                                }
                            </div>
                        </section >
                        <Footer />
                    </div >
                </div>
            </div>
        );
    }
}
;
function dateFormatter(cell: any) {
    var DateModified: any = moment(cell).local().format('YYYY-MM-DD HH:mm').split(' ');
    DateModified = DateModified[1] + " , " + DateModified[0];
    return (
        <div>
            <span {...((cell !== "" && cell !== null) && { title: DateModified })}> {(cell !== "" && cell !== null) && DateModified} </span>
        </div>
    )
}
function statusFormatter(cell: any) {
    return (
        <div className="text-capitalize">
            <span {...(cell === "succeeded" && { className: "badge badge-success p-2" }) || (cell === 'pending' && { className: "badge badge-info p-2" }) || (cell === 'cancelled' && { className: "badge badge-danger p-2" })}>{cell}</span>
        </div>
    )
}
const mapStateToProps = (state: any) => {
    return {
        data: state.payment.data,
        count: state.payment.count,
        paymentOrder: state.payment.paymentByOrder
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        paymentsList: function (page:any, limit:any) {
            dispatch(paymentsList(page, limit))
        },
        paymentByOrderId: function (orderId: number) {
            dispatch(paymentByOrderId(orderId))
        },
        resetPaymentPage: function () {
            dispatch(paymentPageReset())
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(Payments);