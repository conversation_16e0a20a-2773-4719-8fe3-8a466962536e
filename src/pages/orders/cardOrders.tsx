import React, { Component } from 'react';
import { Redirect } from 'react-router-dom'
import { BootstrapTable, TableHeaderColumn } from 'react-bootstrap-table';
import Topbar from '../../components/topbar'
import ReactPaginate from 'react-paginate';
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import Skelton from '../../pages/skeltonLoader'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import { connect } from 'react-redux'
import Invoice from './FbrInvoice/fbrInvoice';
import { logoutUser, cardOrdersList, statesList, storesList } from '../../redux'
import { OrderProps } from '../../interfaces/order';
import moment from 'moment';
import { filterCardOrdersList, searchCardOrder, searchCardOrderByCardOrderId, searchCardOrderByPhonenumber, showCardOrderItems } from '../../redux/actions/orderAction';
import { toast } from "react-toastify";
import { currency } from '../../client-config'
import "react-loader-spinner/dist/loader/css/react-spinner-loader.css";
import "react-toastify/dist/ReactToastify.css";
import './orders.css'
toast.configure();

class ActionFormatter extends Component<{ row: any }, { selectedOrderId: any, checkTimer: any }> {
    constructor(readonly props: any) {
        super(props);
        this.state = {
            selectedOrderId: "",
            checkTimer: false
        }
    }
    showCardOrderItems = (id: any) => {
        let storeId: any = "";
        let stateId: any = "";
        this.props.data.showCardOrderItems(id, this.props.data.orders);
        storeId = this.props.data.orders.find((obj: any) => {
            return obj.order_id == id;
        }).store_id;
        if (storeId && storeId !== "") {
            stateId = this.props.data.stores && this.props.data.stores.find((obj: any) => {
                return obj.store_id == storeId;
            }).state_id;
        }
        // if (stateId !== "") {
        //     tax = this.props.data.states.find((obj: any) => {
        //         return obj.state_id == stateId;
        //     }).tax_percent;
        // }
        this.setState({ selectedOrderId: id });
    }
    render() {
        const { row, data } = this.props;
        return (
            <div>
                <button title="View Invoice" data-toggle="modal" data-target={`#viewinvoice${row.order_id}`} className="btn btn-outline-info ml-2" onClick={() => this.showCardOrderItems(row.order_id)}><i className="fa fa-file-pdf-o"></i></button>
                {/*Invoice Modal */}
                <div id={`viewinvoice${row.order_id}`} data-backdrop='static' role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog modal-lg" style={{ width: '500px' }}>
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">Invoice</h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                <Invoice data={data} orderId={this.state.selectedOrderId} />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
function actionFormatter(cell: any, row: any, props: any) {
    return (
        <ActionFormatter row={row} data={props} />
    );
}
function skeltonFormatter() {
    return (
        <Skelton></Skelton>
    );
}
class Orders extends Component<OrderProps, { listType: any, cardOrderId: any, phonenumber: any, searchType: any, startDate: any, endDate: any, validTimeFlag: any, [x: number]: any, days: any, orderId: any, loader: any }> {
    closeModal: any;
    constructor(readonly props: any) {
        super(props);
        this.state = {
            days: 0,
            startDate: "",
            endDate: "",
            validTimeFlag: "",
            orderId: "",
            loader: true,
            searchType: "orderId",
            cardOrderId: "",
            phonenumber:"",
            listType: "current"
        }
        this.rowClassNameFormat = this.rowClassNameFormat.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleValidTime = this.handleValidTime.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
    }
    rowClassNameFormat(row: any, rowIdx: any) {
        // row is whole row object
        // rowIdx is index of row
        if (row.is_verify == 0) {
            return 'tr-pending-orders'
        } else {
            return ''
        }
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        this.setState({
            [event.target.name]: event.target.value
        });
    }
    handleSearchOrder = (event: any) => { //here
        event.preventDefault();
        let { orderId, cardOrderId, searchType, phonenumber } = this.state;
        if (searchType == "orderId") this.props.searchOrder(orderId, this.updateLoader)
        else if (searchType == "cardOrderId") this.props.searchOrderByCardId(cardOrderId, this.updateLoader)
        else if (searchType == "phonenumber") this.props.searchOrderByPhonenumber(phonenumber, this.updateLoader)
    }
    handleDisableSearch = () => {
        let { orderId, cardOrderId, phonenumber, searchType } = this.state;
        if (searchType == "orderId") return orderId == ""
        else if (searchType == "cardOrderId") return cardOrderId == ""
        else if (searchType == "phonenumber") return phonenumber == ""

    }
    componentDidMount() {
        let obj = { days: this.state.days };
        this.props.ordersList(obj, false, this.updateLoader,0);
        this.props.statesList();
        this.props.storesList();
        document.title = "DominosCMS | Orders"
    }
    updateLoader = () => {
        this.setState({ loader: false })
    }
    FilterbyStatus(verify_status: any) {
        let { orders } = this.props;
        let filteredArr: any = [];
        filteredArr = orders.filter((order: any) => order.is_verify == verify_status)
        this.props.filterCardOrdersList(filteredArr)
    }
    handleValidTime(event: { target: { name: any; value: any; }; }) {
        let futureDatetime = new Date(event.target.value)
        let validMinDateTime = new Date();
        if (futureDatetime <= validMinDateTime) {
            this.setState({ [event.target.name]: event.target.value, validTimeFlag: true });
        } else {
            this.setState({ validTimeFlag: false })
            toast.error("Invalid date and time selected");
        }
    }
    isOrderDateReady = () => {
        let { startDate, endDate, validTimeFlag } = this.state;
        return (startDate !== "" && endDate !== "" && validTimeFlag == true);
    }

     fetchPaginatedOrders = (obj: any, page: any) => {
            let { startDate, endDate } = this.state;
            let startDateUtc = moment(startDate).utc(false).format('YYYY-MM-DD HH:mm')
            let endDateUtc = moment(endDate).utc(false).format('YYYY-MM-DD HH:mm')
            let data:any
            if(startDate !==""&& endDate !== ""){
                 data = {
                    start: startDateUtc,
                    end: endDateUtc
                }
            }
            else{
                data ={
                    days:0
                }
            }
            if (this.state.listType == "current") {
                this.props.ordersList(data, false, this.updateLoader,page);

            } else {
                this.props.ordersList(data, false, this.updateLoader,page);

            }
        };
        handlePageClick = (e: any) => {
            this.setState({ loader: true })
            let obj = { days: this.state.days };
            const selectedPage = e.selected;
            this.fetchPaginatedOrders(obj, selectedPage);
        };
    handleSubmit() {
        this.setState({ loader: true })
        let { startDate, endDate } = this.state;
        let startDateUtc = moment(startDate).utc(false).format('YYYY-MM-DD HH:mm')
        let endDateUtc = moment(endDate).utc(false).format('YYYY-MM-DD HH:mm')
        let data: any = {
            start: startDateUtc,
            end: endDateUtc
        }
        // this.setState({ startDate: "", endDate: "" })
        this.props.ordersList(data, true, this.updateLoader,0);
    }
    render() {
        const dataa = [
            { id: 1, name: 'John Smith', age: 32, email: '<EMAIL>' },
            { id: 2, name: 'Jane Doe', age: 27, email: '<EMAIL>' },
            { id: 3, name: 'Bob Johnson', age: 41, email: '<EMAIL>' },
            { id: 4, name: 'Alice Williams', age: 25, email: '<EMAIL>' },
            { id: 5, name: 'Jack Brown', age: 38, email: '<EMAIL>' },
            { id: 6, name: 'Linda Davis', age: 29, email: '<EMAIL>' },
            { id: 7, name: 'Mike Garcia', age: 45, email: '<EMAIL>' },
            { id: 8, name: 'Samantha Lee', age: 31, email: '<EMAIL>' },
            { id: 9, name: 'David Nguyen', age: 36, email: '<EMAIL>' },
            { id: 10, name: 'Emily Taylor', age: 24, email: '<EMAIL>' }
        ];
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        let maxDate = moment().format('YYYY-MM-DD[T]HH:mm:ss.SSS')
        let { orders, filteredOrders, emptyFilteredOrder, unverifyCount, verifyCount } = this.props;
        const options: any = {
            sizePerPage: 10, // which size per page you want to locate as default
            page: 1,
            pageStartIndex: 1, // wher to start counting the pages
            paginationSize: 3,  // the pagination bar size.
            hideSizePerPage: true, //You can hide the dropdown for sizePerPage
            noDataText: 'Orders Not Found',
        };
        return (
            <div className="page">
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/* <!-- Page Header--> */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="row py-2">
                                    <div className="col-lg-6 col-md-6 col-12" style={{ display: "flex", flexDirection: "row", alignItems: 'center' }}>
                                        {/* <h4 className="mt-2">Orders Management<button title={this.props.timerStatus == false ? "Enable Refresh" : "Disable Refresh"} data-toggle="modal" data-target={`#enableTimer`} className={this.props.timerStatus == false ? "btn btn-outline-success ml-2" : "btn btn-outline-danger ml-2"}><i className={"fa fa-refresh"}></i></button></h4>
                                        <div className="ml-4 ">
                                            {
                                                this.props.timerStatus == true &&
                                                !this.props.updateTimer == true &&
                                                <ReactTimer />
                                            }
                                        </div> */}
                                    </div>
                                    <div className="col-lg-6 col-md-6 col-12">
                                        <div className="row">
                                            <div className="col-lg-6 col-md-6 col-12">
                                                <div className="form-group">
                                                    <input
                                                        id="start"
                                                        type="datetime-local"
                                                        name="startDate"
                                                        max={maxDate}
                                                        value={this.state.startDate}
                                                        className="input-material"
                                                        onChange={this.handleValidTime}
                                                    />
                                                </div>
                                            </div>
                                            <div className="col-lg-6 col-md-6 col-12">
                                                <div className="form-group mb-1">
                                                    <input
                                                        id="end"
                                                        type="datetime-local"
                                                        name="endDate"
                                                        max={maxDate}
                                                        value={this.state.endDate}
                                                        className="input-material"
                                                        onChange={this.handleValidTime}
                                                    />
                                                </div>
                                                <button disabled={!this.isOrderDateReady()} className="badge badge-pill btn-info text-capitalize" style={{ cursor: 'pointer', float: 'right' }} onClick={() => { this.handleSubmit() }}>get</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </header>
                        {/* Counters Section */}
                        <section className="dashboard-counts py-2">
                            <div className="container-fluid">
                                <div className="row bg-white has-shadow p-0">
                                    {/* <!-- Item --> */}
                                    <div className="col-xl-6 col-sm-6">
                                        <div onClick={() => { this.FilterbyStatus(1) }} className="item d-flex align-items-center p-0" style={{ border: "none", cursor: 'pointer' }}>
                                            <div className="icon bg-green"><i className="fa fa-money"></i></div>
                                            <div className="title"><span>Verified</span><br />
                                                <strong className="text-bold text-dark">{verifyCount}</strong>
                                                <div className="progress">
                                                    <div role="progressbar" style={{ width: `${verifyCount / 100}%`, height: "4px" }} className="progress-bar bg-green"></div>  {/*ariaValuenow="40" ariaValuemin="0" ariaValuemax="100"*/}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* <!-- Item --> */}
                                    <div className="col-xl-6 col-sm-6">
                                        <div onClick={() => { this.FilterbyStatus(0) }} className="item d-flex align-items-center p-0" style={{ border: "none", cursor: 'pointer' }}>
                                            <div className="icon bg-blue"><i className="fa fa-cutlery"></i></div>
                                            <div className="title"><span>Unverified</span> <br />
                                                <strong className="text-bold text-dark">{unverifyCount}</strong>
                                                <div className="progress">
                                                    <div role="progressbar" style={{ width: `${unverifyCount / 100}%`, height: "4px" }} className="progress-bar bg-blue"></div>  {/*ariaValuenow="25" ariaValuemin="0" ariaValuemax="100"*/}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        {/* End Counters Section */}
                        <section className="tables py-2">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col">
                                        <div className="card">
                                            <div className="card-body">
                                            <div className="row">
                                                    <div className="col-lg-8 col-md-6 col-12">
                                                        <div className='row'>
                                                            <div className="col-5">
                                                                <div className="form-group">
                                                                    <select className="form-control mt-2" required data-msg="Select Day" onChange={this.handleInputChange} name="searchType">
                                                                        <option key={0} value={"orderId"}>Search by order Id</option>
                                                                        <option key={1} value={"cardOrderId"}>Search by Card Order Id</option>
                                                                        <option key={3} value={"phonenumber"}>Search by Phone Number</option>

                                                                    </select>
                                                                </div>
                                                            </div>
                                                            {this.state.searchType == "orderId" &&
                                                                <div className="form-group col-6">
                                                                    <input id="orderId" type="number" onWheel={(e: any) => e.target.blur()} value={this.state.orderId} name="orderId" required placeholder="Search by order Id" className="input-material" onChange={this.handleInputChange} />
                                                                </div>
                                                            }
                                                            {this.state.searchType == "cardOrderId" &&
                                                                <div className="form-group col-6">
                                                                    <input id="cardOrderId" type="text" onWheel={(e: any) => e.target.blur()} value={this.state.cardOrderId} name="cardOrderId" required placeholder="Search by Card Order Id" className="input-material" onChange={this.handleInputChange} />
                                                                </div>
                                                            }
                                                            {this.state.searchType == "phonenumber" &&
                                                                <div className="form-group col-6">
                                                                    <input id="phonenumber" type="number" onWheel={(e: any) => e.target.blur()} value={this.state.phonenumber} name="phonenumber" required placeholder="Search by Phone Number" className="input-material" onChange={this.handleInputChange} />
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-md-3 col-12 float-right mt-1">
                                                        <div className="form-group">
                                                            <button onClick={this.handleSearchOrder} disabled={this.handleDisableSearch()} className="btn btn-primary btn-block">Search Order</button>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-md-3 col-12 float-right mt-1">
                                                        <div className="form-group">
                                                            <button onClick={() => { this.FilterbyStatus('failedIntegration') }} className="btn btn-danger btn-block">Sync Failed </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                {(this.props.applyLoaderonDate || (orders && orders.length == 0 && this.state.loader)) ?
                                                    <div >
                                                        {/* <Loader
                                                            type="TailSpin"
                                                            color="#007bff"
                                                            height={50}
                                                            width={50}
                                                        /> */}
                                                        {/* here */}
                                                        <BootstrapTable version='4' data={dataa} search={true} options={options} csvFileName='orders.csv'>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold' }} width='80' tdStyle={{ fontSize: 10 }} dataField='1' dataFormat={skeltonFormatter} csvHeader='Order Id' dataSort={true} isKey>Order Id</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold' }} width='80' tdStyle={{ fontSize: 10 }} dataFormat={skeltonFormatter} >Details</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Parmanent Order Id</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Card Order Id</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Verify</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Order Recieved</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Store</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Order Type</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal', textTransform: 'capitalize' }} dataFormat={skeltonFormatter} columnTitle>Order Channel</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='150' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} csvHeader='Pulse Status' columnTitle>Pulse Status</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} csvHeader='Customer Name' columnTitle>Name</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} csvHeader='Phone' columnTitle>Phone</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='150' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} csvHeader='Address' columnTitle>Address</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} csvHeader='Price'>Order Price</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} csvHeader='Discount'>Discount</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Payment Method</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Cancel Reason</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} csvHeader='Cancel Reason Description' columnTitle>Cancel Reason Description</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Caller For Order Transfer</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Order Transfer</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Order Transfer Reason</TableHeaderColumn>
                                                            <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataFormat={skeltonFormatter} columnTitle>Unverify Reason</TableHeaderColumn>
                                                        </BootstrapTable>
                                                    </div> :
                                                    // here
                                                    <BootstrapTable version='4' data={filteredOrders.length > 0 || emptyFilteredOrder ? filteredOrders : orders} search={true} trClassName={this.rowClassNameFormat} options={options} exportCSV={true} csvFileName='orders.csv'>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold' }} width='80' tdStyle={{ fontSize: 10 }} dataField='order_id' csvHeader='Order Id' dataSort={true} isKey>Order Id</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold' }} width='80' tdStyle={{ fontSize: 10 }} dataField='action' dataFormat={actionFormatter} formatExtraData={this.props} export={false}>Details</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='simplex_order_id' csvHeader='Parmanent Order Id' columnTitle>Parmanent Order Id</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='cardOrderId' csvHeader='Card Order Id' columnTitle>Card Order Id</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='is_verify' dataFormat={verifyStatus} csvHeader='Verify' columnTitle>Verify</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='date_created' csvHeader='Order Recieved' columnTitle>Order Recieved</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='store_name' csvHeader='Store' columnTitle>Store</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='delivery_status' csvHeader='Order Type' columnTitle>Order Type</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal', textTransform: 'capitalize' }} dataField='order_channel' columnTitle>Order Channel</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='150' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='failure_reason' csvHeader='Pulse Status' columnTitle>Pulse Status</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='login_name' csvHeader='Customer Name' columnTitle>Name</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='phone_number' csvHeader='Phone' columnTitle>Phone</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='150' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='delivery_address' csvHeader='Address' columnTitle>Address</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='order_grossprice' dataFormat={priceFormatter} csvHeader='Price'>Order Price</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='discount' dataFormat={priceFormatter} csvHeader='Discount'>Discount</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='80' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='payment_method' csvHeader='Payment Method' columnTitle>Payment Method</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='reason' csvHeader='Cancel Reason' columnTitle>Cancel Reason</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='cancel_reason_description' csvHeader='Cancel Reason Description' columnTitle>Cancel Reason Description</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='order_transfer_caller' csvHeader='Caller For Order Transfer' columnTitle>Caller For Order Transfer</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='is_order_transfer' csvHeader='Order Transfer' dataFormat={isOrderTransferFormatter} columnTitle>Order Transfer</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='120' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField="order_transfer_reason" csvHeader="Order Transfer Reason" columnTitle>Order Transfer Reason</TableHeaderColumn>
                                                        <TableHeaderColumn thStyle={{ fontSize: 10, fontWeight: 'bold', whiteSpace: 'normal' }} width='100' tdStyle={{ fontSize: 10, whiteSpace: 'normal' }} dataField='unverified_reason' csvHeader='Unverify Reason' columnTitle>Unverify Reason</TableHeaderColumn>
                                                    </BootstrapTable>
                                                }

                                                { this.props.orderPageCount > 0 &&
                                                    <div className="row" >
                                                        <div className="col d-flex d-flex justify-content-end align-items-center">
                                                            <ReactPaginate
                                                                initialPage={parseInt(this.props.orderCurrentPage)}
                                                                breakLinkClassName={'page-link'}
                                                                pageClassName={'page-item'}
                                                                pageLinkClassName={'page-link'}
                                                                previousClassName={'page-item'}
                                                                previousLinkClassName={'page-link'}
                                                                nextClassName={'page-item'}
                                                                nextLinkClassName={'page-link'}
                                                                previousLabel={"prev"}
                                                                nextLabel={"next"}
                                                                breakLabel={"...."}
                                                                breakClassName={"page-item"}
                                                                pageCount={this.props.orderPageCount}
                                                                marginPagesDisplayed={1}
                                                                pageRangeDisplayed={2}
                                                                disableInitialCallback={true}
                                                                onPageChange={this.handlePageClick}
                                                                containerClassName={"pagination"}
                                                                activeClassName={"active"} />
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div >
            </div >
        );
    }
};
function isOrderTransferFormatter(cell: any, row: any) {
    if (row.is_order_transfer == 0) {
        return <p title="No">No</p>;
    } else {
        return <p title="Yes">Yes</p>;
    }
}
function priceFormatter(cell: any, row: any) {
    if (row.aggregator_orderId) {
        return `${currency} ${cell}`;
    } else {
        return `${currency} ${Math.round(cell)}`;
    }
}

function verifyStatus(cell: any, row: any) {
    let obj1 = {
        backgroundColor: "green",
        color: "white",
        padding: "4px"
    }
    let obj2 = {
        backgroundColor: "red",
        color: "white",
        padding: "4px"
    }
    return (
        <div>
            <span style={row.is_verify ? obj1 : obj2}>{row.is_verify ? "Verified" : "Unverified"}</span>
        </div>
    )
}

const mapStateToProps = (state: any) => {
    return {
        orders: state.order.cardOrders,
        filteredOrders: state.order.filteredCardOrders,
        emptyFilteredOrder: state.order.emptyFilterCardOrder,
        Items: state.order.cardOrderItems,
        orderDetail: state.order.order,
        states: state.setting.states,
        stores: state.store.data,
        verifyCount: state.order.verifyCount,
        unverifyCount: state.order.unverifyCount,
        applyLoaderonDate: state.order.isLoading,
        orderCurrentPage: state.order.currentPage,
        orderPageCount: state.order.pageCount,
        orderNumOfRows: state.order.numOfRows,
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        ordersList: (obj: any, datetime: any, updateLoader?: any,page?: any) => {
            dispatch(cardOrdersList(obj, datetime,updateLoader,page))
        },
        showCardOrderItems: function (id: any, orders: any) {
            dispatch(showCardOrderItems(id, orders))
        },
        statesList: function () {
            dispatch(statesList())
        },
        storesList: function () {
            dispatch(storesList())
        },
        filterCardOrdersList: (filteredArr: any) => {
            dispatch(filterCardOrdersList(filteredArr))
        },
        searchOrder: (orderId: any, updateLoader?: any) => {
            dispatch(searchCardOrder(orderId, updateLoader))
        },
        searchOrderByPhonenumber: (phone: any, updateLoader?: any) => {
            dispatch(searchCardOrderByPhonenumber(phone, updateLoader))
        },
        searchOrderByCardId: (cardId: any, updateLoader?: any) => {
            dispatch(searchCardOrderByCardOrderId(cardId, updateLoader))
        },
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(Orders);