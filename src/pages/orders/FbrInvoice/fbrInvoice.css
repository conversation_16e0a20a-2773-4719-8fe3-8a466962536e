.clearfix:after {
    content: "";
    display: table;
    clear: both;
  }
  .clearfix{
    /* padding: 15px; */
    margin-bottom: 10px;
  }
  /* a {
    color: #5D6975;
    text-decoration: underline;
  } */
  #logo {
    text-align: center;
    margin-bottom: 15px;
  }
  
  #logo img {
    -webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
    filter: grayscale(100%);
  }
  
  #project {
    /* float: left; */
    margin-top: 10px;
    margin-bottom: 10px;
  }
  #project span {
    font-family: Arial;
    color: black;
    /* text-align: right; */
    width: 52px;
    /* margin-right: 10px; */
    margin-left: 10px;
    display: inline-block;
    font-size: 14px;
    text-align: left;
    margin-right: 30px;
  }
  
  #project div {
    white-space: nowrap;        
  }
  
  .invoicetable {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin-bottom: 20px;
  }
  
  .invoicetable tr:nth-child(2n-1) td {
    background: #F5F5F5;
  }
  
  .invoicetable th,
  .invoicetable td {
    font-size: 12px;
    color:black;
    text-align: left;
    text-overflow: unset !important;
  }
  
  .invoicetable th {
    padding: 5px 10px;
    border-bottom: 1px solid #C1CED9;
    white-space: nowrap;        
    font-weight: normal;
  }
  
  .invoicetable td {
    white-space: normal !important;
    text-align: left;
  }
  .invoicetable td ul li{
    margin-left: -15px;
  }
  
  .invoicetable td.service,
  .invoicetable td.desc {
    
    vertical-align: top;
  }
  
  .invoicetable td.unit,
  .invoicetable td.qty,
  .invoicetable td.total {
    font-size: 13px;
  }
  
  .invoicetable td.grand {
    font-size: 13px;
    /* border-top: 1px solid #5D6975;; */
  }
  #notices{
    font-size: 13px;
    margin-top: 10px;
  }
  #notices .notice {
    color: black;
    font-size: 13px;
  }
  
  .invoicefooter {
    color: black;
    width: 100%;
    height: 30px;
    bottom: 0;
    margin-top: 15px;
    margin-bottom: 15px;
    font-size: 13px;
    border-top: 1px solid #C1CED9;
    padding: 8px 0;
    text-align: center;
    white-space: normal;
  }
  
  td.discount{
    color: black
  }
  .heading_text{
      font-size: 12px;
      white-space: normal;
  }
  .change_color{
    color: black;
  }