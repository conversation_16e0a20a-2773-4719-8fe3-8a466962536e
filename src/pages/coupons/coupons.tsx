import React, { Component } from 'react';
import { Redirect, Link } from 'react-router-dom'
import { BootstrapTable, TableHeaderColumn } from 'react-bootstrap-table';
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import { connect } from 'react-redux'
import { couponsList, logoutUser, deleteCoupon, activeInactiveCoupon } from '../../redux'
import { CouponProps } from '../../interfaces/coupon';
class ActionFormatter extends Component<{ row: any }, {}> {
    constructor(readonly props: any) {
        super(props);
    }
    handleDelete = (id: any) => {
        this.props.data.deleteCoupon(id,this.props.data.history)
    };
    handleActiveInActive = (id: any, is_archive: any) => {
        this.props.data.activeInactiveCoupon(id, is_archive,this.props.data.history);
    };
    render() {
        const { row } = this.props
        return (
            <div>
                <button title={row.is_archive === 0 ? "Inactive" : "Active"} data-toggle="modal" data-target={`#activeInactive${row.coupon_id}`} className={row.is_archive === 0 ? "btn btn-outline-danger mr-2" : "btn btn-outline-success mr-2"}><i className={row.is_archive === 0 ? "fa fa-lock" : "fa fa-unlock"}></i></button>
                <button title="Delete Coupon" className="btn btn-outline-danger" data-toggle="modal" data-target={`#DelCoupon${row.coupon_id}`}><i className="fa fa-trash"></i></button>
                <Link title="Edit Coupon" className="btn btn-outline-primary ml-2" to={`/edit-coupon/${row.coupon_id}`}><i className="fa fa-edit"></i></Link>
                {/* <!-- Modal--> */}
                <div id={`DelCoupon${row.coupon_id}`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">Delete Coupon</h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                <p>Are you sure,you want to delete this Coupon?</p>
                            </div>
                            <div className="modal-footer">
                                <button type="button" data-dismiss="modal" className="btn btn-danger">Close</button>
                                <button onClick={() => this.handleDelete(row.coupon_id)} className="btn btn-primary">Confirm</button>
                            </div>
                        </div>
                    </div>
                </div>
                {/* <!-- Block/Unblock Modal--> */}
                <div id={`activeInactive${row.coupon_id}`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">{row.is_archive === 0 ? "Inactive" : "Active"} Coupon</h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                <p>Are you sure,you want to {row.is_archive === 0 ? "Inactive" : "Active"} this coupon?</p>
                            </div>
                            <div className="modal-footer">
                                <button type="button" data-dismiss="modal" className="btn btn-secondary">Close</button>
                                <button onClick={() => this.handleActiveInActive(row.coupon_id, row.is_archive)} className="btn btn-primary">Confirm</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
function actionFormatter(cell: any, row: any, props: any) {
    return (
        <ActionFormatter row={row} data={props} />
    );
}

class Coupon extends Component<CouponProps, {}> {
    constructor(readonly props: any) {
        super(props);
    }
    componentDidMount() {
        this.props.couponsList()
        document.title = "DominosCMS | Coupons"
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const options: any = {
            sizePerPage: 10, // which size per page you want to locate as default
            page: 1,
            pageStartIndex: 1, // where to start counting the pages
            paginationSize: 3,  // the pagination bar size.
            hideSizePerPage: true, //You can hide the dropdown for sizePerPage
            insertModal: () => { return <Redirect to="/add-coupon" /> },
            noDataText: 'Coupons Not Found'
        };
        return (
            <div className="page">
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/* <!-- Page Header--> */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Coupons Management</h4>
                                </div>
                            </div>
                        </header>
                        <section className="tables">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col">
                                        <div className="card">
                                            <div className="card-body">
                                                <BootstrapTable version="4" data={this.props.data} search={true} pagination={this.props.data.length > 10 && true} options={options} exportCSV={true} insertRow csvFileName='combos.csv' hover>
                                                    <TableHeaderColumn dataField='coupon_id' csvHeader='#' width='50' dataSort={true} isKey>#</TableHeaderColumn>
                                                    <TableHeaderColumn dataField='coupon_name' csvHeader='Coupon Name' width='100' columnTitle>Coupon name</TableHeaderColumn>
                                                    <TableHeaderColumn dataField='coupon_code' csvHeader='Coupon Code' width='100' columnTitle>Coupon Code</TableHeaderColumn>
                                                    <TableHeaderColumn dataField='coupon_description' csvHeader='Description' width='100' columnTitle>Description</TableHeaderColumn>
                                                    <TableHeaderColumn dataField='coupon_value' dataFormat={couponFormatter} csvHeader='Coupon value' width='100' columnTitle>Coupon value</TableHeaderColumn>
                                                    <TableHeaderColumn dataField='expire_date' csvHeader='Expiry' width='100' columnTitle>Valid Till</TableHeaderColumn>
                                                    <TableHeaderColumn dataField='is_archive' width='100' dataFormat={statusFormatter} export={false}>Status</TableHeaderColumn>
                                                    <TableHeaderColumn dataField='action' width='180' dataFormat={actionFormatter} formatExtraData={this.props} export={false}>Action</TableHeaderColumn>
                                                </BootstrapTable>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div>
        );
    }
}
;
function couponFormatter(cell: any) {
    return ` ${cell} %`;
}
function statusFormatter(cell: any) {
    return (
        <div className="text-capitalize">
            <span {...(cell == 0 && { className: "badge badge-success p-2" }) || (cell == 1 && { className: "badge badge-danger p-2" })}>{cell == 0 ? "active" : "Inactive"}</span>
        </div>
    )
}
const mapStateToProps = (state: any) => {
    return {
        data: state.coupon.data
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        couponsList: function () {
            dispatch(couponsList())
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(Coupon);