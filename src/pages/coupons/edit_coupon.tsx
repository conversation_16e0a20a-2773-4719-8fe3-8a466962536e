import React, { Component } from 'react';
import { EditCouponProps, EditCouponState } from '../../interfaces/coupon'
import { connect } from 'react-redux'
import { editCoupon, getCoupon, menusList, logoutUser, itemsListForMultiSelect, searchCustomerByPhone, groupsListForMultiSelect } from '../../redux'
import { storesList } from '../../redux/actions/reportAction';
import { Link, Redirect } from 'react-router-dom'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import CheckChanges from '../../components/confirmOnLeave'
import Select from 'react-select';
import moment from 'moment';
import './coupon.css'
var voucher_codes = require('voucher-code-generator');
class EditCoupon extends Component<EditCouponProps, EditCouponState> {
    constructor(props: any) {
        super(props);
        this.state = {
            couponname: "",
            coupondesc: "",
            couponvalue: "",
            couponcode: "",
            pos_code: "",
            expiryDate: "",
            startDate: "",
            percent: "",
            mode: "",
            channel: "",
            type: "",
            discountType: "",
            limit: "",
            type_id: "",
            multiJson: [],
            multiGroupJson: [],
            multiStoreJson: [],
            freeDelivery: false,
            minamount: "",
            totalusagebycus: "",
            usageDuration: null,
            minorders: "",
            specificCustomer: false,
            vipCustomer: false,
            is_nps: false,
            phone: null,
            multiCustomerJson: [],
            setType: ""
        }
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
        this.handleCouponType = this.handleCouponType.bind(this);
        this.handleDiscountType = this.handleDiscountType.bind(this);
        this.validatePhone = this.validatePhone.bind(this);
    }
    componentDidMount() {
        document.title = "DominosCMS | Coupons"
        let id = this.props.match.params.id;
        this.props.getCoupon(id);
        this.props.storesList();
        this.props.itemsListForMultiSelect();
        this.props.menusList();
        this.props.groupsList();
    }
    UNSAFE_componentWillReceiveProps(nextProps: any, nextState: any) {
        if (!this.state.phone) {
            this.setState({
                couponname: nextProps.couponData.coupon_name,
                coupondesc: nextProps.couponData.coupon_description,
                couponcode: nextProps.couponData.coupon_code,
                pos_code: nextProps.couponData.pos_code,
                startDate: nextProps.couponData.start_date !== "0000-00-00 00:00:00.000000" ? moment(nextProps.couponData.start_date).local().format('YYYY-MM-DDTHH:mm') : "",
                expiryDate: moment(nextProps.couponData.expire_date).local().format('YYYY-MM-DDTHH:mm'),
                type: nextProps.couponData.type ? nextProps.couponData.type : 'menu',
                channel: (nextProps.couponData.channel && nextProps.couponData.channel !== "") ? nextProps.couponData.channel : "delivery",
                type_id: nextProps.couponData.type_id,
                limit: nextProps.couponData.limit,
                mode: nextProps.couponData.mode,
                freeDelivery: nextProps.couponData.free_delivery === 0 ? false : true,
                specificCustomer: nextProps.couponData.for_customer == 0 ? false : true,
                is_nps: nextProps.couponData.is_nps == 0 ? false : true,
                setType: nextProps.couponData.is_emp == 1 ? "is_emp" : nextProps.couponData.is_partner == 1 ? "is_partner" : nextProps.couponData.for_customer == 1 ? "specificCustomer" : "",
                // vipCustomer: nextProps.couponData.is_vip == 0 ? false : true,
                multiJson: nextProps.couponData.items_json && JSON.parse(nextProps.couponData.items_json),
                multiGroupJson: nextProps.couponData.multiGroupJson && JSON.parse(nextProps.couponData.multiGroupJson),
                multiStoreJson: nextProps.couponData.stores_json ? JSON.parse(nextProps.couponData.stores_json) : [],
                multiCustomerJson: nextProps.couponData.customers_json ? JSON.parse(nextProps.couponData.customers_json) : [],
                discountType: nextProps.couponData.discount_type,
                minamount: nextProps.couponData.min_amount,
                minorders: nextProps.couponData.min_orders,
                totalusagebycus: nextProps.couponData.total_usage_customer,
                usageDuration: nextProps.couponData.usage_duration
            })
            if (nextProps.couponData.coupon_value > 0) {
                this.setState({ couponvalue: nextProps.couponData.coupon_value })
            } else if (nextProps.couponData.percent && nextProps.couponData.percent > 0) {
                this.setState({ percent: nextProps.couponData.percent })
            }
        }
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        if (event.target.value == ' ') {
            event.target.value = event.target.value.replace(/\s/g, "");
        }
        this.setState({
            [event.target.name]: event.target.value,
        });
    }
    handleCouponType(event: { target: { name: any; value: any; }; }) {
        this.setState({
            type_id: "",
            [event.target.name]: event.target.value,
        });
    }
    handleGroupsInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ multiGroupJson: e });
        } else {
            this.setState({ multiGroupJson: [] });
        }
    }
    handleItemsInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ multiJson: e });
        } else {
            this.setState({ multiJson: [] });
        }
    }
    handleStoresInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ multiStoreJson: e });
        } else {
            this.setState({ multiStoreJson: [] });
        }
    }
    handleCustomersInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ multiCustomerJson: e });
        } else {
            this.setState({ multiCustomerJson: [] });
        }
    }
    handleDiscountType(event: { target: { name: any; value: any; }; }) {
        this.setState({
            percent: "",
            couponvalue: "",
            [event.target.name]: event.target.value,
        });
    }

    blockInvalidChar = (e: any) =>
        ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

    isCouponReady = () => {
        const { specificCustomer, pos_code, multiCustomerJson, freeDelivery, couponname, couponvalue, couponcode, startDate, expiryDate, multiJson, multiStoreJson, multiGroupJson, mode, type, discountType, limit, type_id, percent } = this.state
        if (type == "menu") {
            return (couponname !== "" && pos_code !== "" && startDate !== "" && expiryDate !== "" && mode !== "" && type !== "" && discountType !== ""
                && limit !== "" && type_id !== "" && (discountType !== "combo" ? (percent !== "" || couponvalue !== "") : true) && couponcode !== "" && (specificCustomer ? multiCustomerJson.length > 0 : true));
        } else if (type == "group") {
            return (couponname !== "" && pos_code !== "" && startDate !== "" && expiryDate !== "" && mode !== "" && type !== "" && discountType !== ""
                && limit !== "" && (multiGroupJson && multiGroupJson.length > 0) && (discountType !== "combo" ? (percent !== "" || couponvalue !== "") : true) && couponcode !== "" && (specificCustomer ? multiCustomerJson.length > 0 : true));
        } else if (type == "item") {
            return (couponname !== "" && pos_code !== "" && startDate !== "" && expiryDate !== "" && mode !== "" && type !== "" && discountType !== ""
                && limit !== "" && (multiJson && multiJson.length > 0) && (discountType !== "combo" ? (percent !== "" || couponvalue !== "") : true) && couponcode !== "" && (specificCustomer ? multiCustomerJson.length > 0 : true));
        } else if (type == "store") {
            return (couponname !== "" && pos_code !== "" && startDate !== "" && expiryDate !== "" && mode !== "" && type !== "" && discountType !== ""
                && limit !== "" && (multiStoreJson && multiStoreJson.length > 0) && (discountType !== "combo" ? (percent !== "" || couponvalue !== "") : true) && couponcode !== "" && (specificCustomer ? multiCustomerJson.length > 0 : true));
        } else if (freeDelivery) {
            return (couponname !== "" && pos_code !== "" && startDate !== "" && expiryDate !== "" && mode !== "" && discountType !== ""
                && limit !== "" && (discountType !== "combo" ? (percent !== "" || couponvalue !== "") : true) && couponcode !== "" && (specificCustomer ? multiCustomerJson.length > 0 : true));
        }
    }
    generateCouponCode = () => {
        let code: any[] = voucher_codes.generate({
            length: 8,
            count: 1,
            prefix: "promo-",
            postfix: `-${moment().format('YYYY')}`,
            charset: voucher_codes.charset("alphanumeric")
        });
        this.setState({ couponcode: code[0] });
    }
    handleFreeDelivery = (event: { target: { name: any; value: any; }; }) => {
        const { freeDelivery } = this.state;
        if (freeDelivery) {
            this.setState({ type: "menu", })
        } else {
            this.setState({ type: "" })
        }
        this.setState({ freeDelivery: !freeDelivery, type_id: "" })
    }
    handleVip = (event: { target: { name: any; value: any; }; }) => {
        const { vipCustomer } = this.state;
        this.setState({ vipCustomer: !vipCustomer })
    }
    validatePhone(event: { target: { name: any; value: any; }; }) {
        if (event.target.value.length < 11) {
            this.setState({ phone: event.target.value })
        } else {
            if (event.target.value.length === 11) {
                this.setState({ phone: event.target.value });
            }
        }
        if (event.target.value.length >= 4) {
            this.props.searchCustomerByPhone(event.target.value)
        }
    };
    handleCheckBoxes = (event: { target: { name: any; value: any; }; }) => {
        if (event.target.name == "is_emp") {
            if (this.state.setType == "is_emp") {
                this.setState({ setType: "", specificCustomer: false, })
            } else {
                this.setState({ setType: event.target.name, specificCustomer: false, })
            }
        } else if (event.target.name == "specificCustomer") {
            if (this.state.setType == "specificCustomer") {
                this.setState({ specificCustomer: false, setType: "", vipCustomer: false })
            } else {
                this.setState({ specificCustomer: true, setType: 'specificCustomer', vipCustomer: false })
            }
        } else if (event.target.name == "is_nps") {
            this.setState({ is_nps: !this.state.is_nps })
        }
    }
    handleSubmit = (event: any) => {
        let { minorders, minamount, setType, is_nps, totalusagebycus, couponname, coupondesc, couponcode, pos_code, couponvalue, startDate, channel, expiryDate, mode, type, limit, type_id, percent, multiJson, multiStoreJson, freeDelivery, specificCustomer, multiCustomerJson, multiGroupJson, discountType, usageDuration } = this.state;
        let id = this.props.match.params.id;
        let data: any = {
            coupon_name: couponname,
            coupon_code: couponcode,
            pos_code: pos_code,
            coupon_description: coupondesc,
            coupon_value: couponvalue,
            start_date: moment(startDate).utc(false).format('YYYY-MM-DD HH:mm:ss'),
            expire_date: moment(expiryDate).utc(false).format('YYYY-MM-DD HH:mm:ss'),
            mode: mode,
            channel: channel,
            limit: limit,
            percent: percent,
            min_amount: minamount,
            min_orders: minorders,
            total_usage_customer: (totalusagebycus !== "" && totalusagebycus > 0) ? totalusagebycus : 0,
            usage_duration: usageDuration !== "" ? usageDuration : null,
            discount_type: discountType
        }
        if (limit !== this.props.couponData.limit) {
            data.total_usage = limit;
        }
        if (is_nps) {
            data.is_nps = 1
        } else {
            data.is_nps = 0
        }
        if (type == "menu") {
            data.type_id = type_id;
            data.items_json = null;
            data.stores_json = null;
            data.multiGroupJson = null;
        } else if (type == "group") {
            data.type_id = null;
            data.items_json = null;
            data.stores_json = null;
            data.multiGroupJson = JSON.stringify(multiGroupJson);
        } else if (type == "item") {
            data.type_id = null;
            data.multiGroupJson = null;
            data.stores_json = null;
            data.items_json = JSON.stringify(multiJson);
        } else if (type == "store") {
            data.type_id = null;
            data.items_json = null;
            data.multiGroupJson = null;
            data.stores_json = JSON.stringify(multiStoreJson);
        }
        if (specificCustomer) {
            data.customers_json = JSON.stringify(multiCustomerJson);
            data.for_customer = 1;
            data.is_emp = 0;
            data.is_partner = 0;
        } else {
            data.for_customer = 0;
        }
        if (setType == "is_emp") {
            data.is_emp = 1;
            data.is_partner = 0;
        } else if (setType == "is_partner") {
            data.is_partner = 1;
            data.is_emp = 0;
        } else {
            data.is_partner = 0;
            data.is_emp = 0;
        }
        // if (vipCustomer) {
        //     data.is_vip = 1
        // } else {
        //     data.is_vip = 0;
        // }
        if (freeDelivery) {
            data.type_id = null;
            data.type = type;
            data.stores_json = null;
            data.items_json = null;
            data.free_delivery = 1
        } else {
            data.type = type;
            data.free_delivery = 0
        }
        this.props.editCoupon(id, data)
        event.preventDefault();
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const { couponData, items, menus, groups, stores, searchCustomers } = this.props;
        const { expiryDate, startDate, is_nps } = this.state
        return (
            <div className="page">
                <CheckChanges path="/edit-coupon" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Compaigns Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/marketing/0" className="text-primary">Compaigns</Link></li>
                                <li className="breadcrumb-item active">Edit Coupon</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Coupon name <span className="text-danger">*</span></label>
                                                            <input id="couponname" type="text" defaultValue={couponData.coupon_name} name="couponname" required data-msg="Please enter Coupon Name" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Description</label>
                                                            <input id="coupondesc" type="text" name="coupondesc" defaultValue={couponData.coupon_description} required className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row my-3 d-flex justify-content-between align-items-center">
                                                    <div className="col-lg-3 col-md-3 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Pulse coupon code <span className="text-danger">*</span></label>
                                                            <input id="pos_code" type="text" name="pos_code" value={this.state.pos_code} required data-msg="Please enter POS Code" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-md-3 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Coupon Code <span className="text-danger">*</span></label>
                                                            <input id="couponcode" type="text" name="couponcode" value={this.state.couponcode} required data-msg="Please enter Coupon Code" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-md-2 col-6">
                                                        <button className="btn btn-sm btn-primary ml-2" title='Generate Coupon Code' onClick={() => this.generateCouponCode()}><i className="fa fa-refresh"></i></button>
                                                    </div>
                                                    <div className="col-lg-3 col-md-4 col-6 mt-3">
                                                        <div>
                                                            <input id="checkboxCustom2" type="checkbox" name="freeDelivery" checked={this.state.freeDelivery} onChange={this.handleFreeDelivery} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom2">Free Delivery
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Free Delivery represents that Customer can place order with 0 delivery fee">
                                                                    i
                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {this.state.freeDelivery == false &&
                                                    <div className="row">
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Coupon Level <span className="text-danger">*</span>
                                                                    <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Coupon level represents that coupon can be used in which level.">
                                                                        i
                                                                    </span></label>
                                                                <select name="type" className="form-control text-capitalize mt-2" required data-msg="Please select Type" onChange={this.handleInputChange}>
                                                                    <option {...couponData.type === 'menu' && { selected: true }} value='menu' >Menu</option>
                                                                    <option {...couponData.type === 'group' && { selected: true }} value='group' >Group</option>
                                                                    {/*<option {...couponData.type === 'item' && { selected: true }} value='item' >Item</option> */}
                                                                    <option {...couponData.type === 'store' && { selected: true }} value='store'>Store</option>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        {this.state.type === "menu" &&
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Select Menu <span className="text-danger">*</span></label>
                                                                    <select name="type_id" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleInputChange}>
                                                                        <option value="">Select Menu</option>
                                                                        {menus &&
                                                                            menus.map((menu: any, index: any) => (
                                                                                <option key={index} value={menu.menu_id} {...couponData.type_id === menu.menu_id && { selected: true }}>{menu.menu_name}</option>
                                                                            ))
                                                                        }
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        }

                                                        {this.state.type === "group" &&
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Select Group <span className="text-danger">*</span></label>
                                                                    <Select
                                                                        isMulti
                                                                        name="groups"
                                                                        options={groups}
                                                                        defaultValue={(couponData && couponData.multiGroupJson) && JSON.parse(couponData.multiGroupJson)}
                                                                        className="text-capitalize basic-multi-select mt-2"
                                                                        classNamePrefix="select"
                                                                        onChange={(e, i) => this.handleGroupsInputChange(e, i)}
                                                                    />
                                                                    {/* <select name="type_id" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleInputChange}>
                                                                        <option value="">Select Group</option>
                                                                        {groups &&
                                                                            groups.map((group: any, index: any) => (
                                                                                <option key={index} value={group.group_id} {...couponData.type_id === group.group_id && { selected: true }}>{group.group_name}</option>
                                                                            ))
                                                                        }
                                                                    </select> */}
                                                                </div>
                                                            </div>
                                                        }
                                                        {this.state.type === "item" &&
                                                            <div className="col-md-6">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Select Items <span className="text-danger">*</span></label>
                                                                    <Select
                                                                        isMulti
                                                                        name="items"
                                                                        options={items}
                                                                        defaultValue={(couponData && couponData.items_json) && JSON.parse(couponData.items_json)}
                                                                        className="text-capitalize basic-multi-select mt-2"
                                                                        classNamePrefix="select"
                                                                        onChange={(e, i) => this.handleItemsInputChange(e, i)}
                                                                    />
                                                                </div>
                                                            </div>
                                                        }
                                                        {this.state.type === "store" &&
                                                            <div className="col-md-6">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Select Stores <span className="text-danger">*</span></label>
                                                                    <Select
                                                                        isMulti
                                                                        name="stores"
                                                                        options={stores}
                                                                        defaultValue={(couponData && couponData.stores_json) && JSON.parse(couponData.stores_json)}
                                                                        className="text-capitalize basic-multi-select mt-2"
                                                                        classNamePrefix="select"
                                                                        onChange={(e, i) => this.handleStoresInputChange(e, i)}
                                                                    />
                                                                </div>
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                                <div className="row">
                                                    <div className="col-lg-4 col-md-4 col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Ordering Channel <span className="text-danger">*</span>
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Ordering Channel represents customer can use coupon in which channel.">
                                                                    i
                                                                </span></label>
                                                            <select name="channel" className="form-control text-capitalize mt-2" required data-msg="Please select Type" onChange={this.handleInputChange}>
                                                                <option {...couponData.channel == 'all' && { selected: true }} value='all'>All</option>
                                                                <option {...couponData.channel == 'delivery' && { selected: true }} value='delivery'>Delivery</option>
                                                                <option {...couponData.channel == 'pickup' && { selected: true }} value='pickup' >Pick Up</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-md-4 col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Ordering Mode <span className="text-danger">*</span>
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Ordering Mode represents customer can use coupon in which order mode.">
                                                                    i
                                                                </span></label>
                                                            <select name="mode" className="form-control text-capitalize mt-2" required data-msg="Please select Type" onChange={this.handleInputChange}>
                                                                <option {...couponData.mode == 'all' && { selected: true }} value='all' >All</option>
                                                                <option {...couponData.mode == 'online' && { selected: true }} value='online'>Online</option>
                                                                <option {...couponData.mode == 'callcenter' && { selected: true }} value='callcenter' >Call Center</option>
                                                                <option {...couponData.mode == 'mobile' && { selected: true }} value='mobile' >Mobile</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-md-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Coupon Limit <span className="text-danger">*</span>
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Coupon Limit represents that coupon can be used for only x time. ">
                                                                    i
                                                                </span></label>
                                                            <input id="limit" type="number" onWheel={(e: any) => e.target.blur()} name="limit" min="1" defaultValue={couponData.limit} required data-msg="Please enter Limit" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Coupon Type <span className="text-danger">*</span>
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Coupon Type represents that coupon will be applicable on flat cart value or will on percentage of cart value.">
                                                                    i
                                                                </span></label>
                                                            <select name="discountType" className="form-control text-capitalize mt-2" required data-msg="Please select Type" onChange={this.handleDiscountType}>
                                                                <option {...this.state.discountType === 'value' && { selected: true }} value='value'>Value</option>
                                                                <option {...this.state.discountType === 'percentage' && { selected: true }} value='percentage' >Percentage</option>
                                                                <option {...this.state.discountType === 'combo' && { selected: true }} value='combo' >Combo</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    {this.state.discountType === "percentage" &&
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Coupon Percentage <span className="text-danger">*</span></label>
                                                                <input id="percent" defaultValue={couponData.percent} min="1" onKeyDown={this.blockInvalidChar} type="number" onWheel={(e: any) => e.target.blur()} name="percent" required data-msg="Please enter Coupon Percentage" className="input-material" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                    }
                                                    {this.state.discountType === "value" &&
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Coupon value <span className="text-danger">*</span></label>
                                                                <input id="couponvalue" defaultValue={couponData.coupon_value} min="1" onKeyDown={this.blockInvalidChar} type="number" onWheel={(e: any) => e.target.blur()} name="couponvalue" required data-msg="Please enter Coupon Value" className="input-material" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Usage/customer
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Coupon usage/customer represents that customer can use coupon for only x time.">
                                                                    i
                                                                </span></label>
                                                            <input id="totalusagebycus" type="number" onWheel={(e: any) => e.target.blur()} name="totalusagebycus" defaultValue={couponData.total_usage_customer} min="0" onKeyDown={this.blockInvalidChar} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Usage/customer Duration
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Usage/customer Duration represents that customer can use coupon according to duration.">
                                                                    i
                                                                </span>
                                                            </label>
                                                            <select name="usageDuration" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleInputChange}>
                                                                <option value={""}>No Restriction</option>
                                                                <option value={"daily"} {...couponData.usage_duration === "daily" && { selected: true }}>Daily</option>
                                                                <option value={"weekly"} {...couponData.usage_duration === "weekly" && { selected: true }}>Weekly</option>
                                                                <option value={"monthly"} {...couponData.usage_duration === "monthly" && { selected: true }}>Monthly</option>
                                                                <option value={"quarterly"} {...couponData.usage_duration === "quarterly" && { selected: true }}>Quarterly</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Min total
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Min Total represents the minimum cart value customer holds to fulfill coupon requirement.">
                                                                    i
                                                                </span></label>
                                                            <input id="minamount" type="number" onWheel={(e: any) => e.target.blur()} name="minamount" defaultValue={couponData.min_amount} min="0" onKeyDown={this.blockInvalidChar} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Min Orders
                                                                <span className="tooltip-btn" data-toggle="tooltip" data-placement="top" title="Min Orders represents the minimum orders customer holds to fulfill coupon requirement.">
                                                                    i
                                                                </span></label>
                                                            <input id="minorders" type="number" onWheel={(e: any) => e.target.blur()} name="minorders" min="0" defaultValue={couponData.min_orders} onKeyDown={this.blockInvalidChar} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-lg-3 col-md-3 col-6 my-2">
                                                        <div>
                                                            <input id="checkboxCustom1" type="checkbox" name="is_emp" checked={this.state.setType === 'is_emp'} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom1">For Employees</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-md-3 col-6 my-2">
                                                        <div>
                                                            <input id="checkboxCustom3" type="checkbox" name="specificCustomer" checked={this.state.setType === 'specificCustomer'} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom3">For Specific Customer</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-md-3 col-6 my-2">
                                                        <div>
                                                            <input id="checkboxCustom4" type="checkbox" name="is_nps" checked={is_nps == true && true} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom4">For NPS</label>
                                                        </div>
                                                    </div>
                                                    {/* {this.state.setType !== "" && this.state.setType !== "specificCustomer" &&
                                                        <div className="col-lg-3 col-md-3 col-6 my-2">
                                                            <div>
                                                                <input id="checkboxCustom4" type="checkbox" name="vip" checked={this.state.vipCustomer} onChange={this.handleVip} className="checkbox-template" />
                                                                <label htmlFor="checkboxCustom4">For VIP</label>
                                                            </div>
                                                        </div>
                                                    } */}
                                                </div>
                                                {this.state.specificCustomer &&
                                                    <div className="row">
                                                        <div className="col-md-6">
                                                            <div className="form-group">
                                                                <label className="form-control-lable">Phone</label>
                                                                <input id="phone" type="number" onWheel={(e: any) => e.target.blur()} value={this.state.phone} onKeyDown={this.blockInvalidChar} name="phone" required data-msg="Please enter Phone" className="input-material" onChange={this.validatePhone} />
                                                            </div>
                                                        </div>
                                                        <div className="col-md-6">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Select Customers <span className="text-danger">*</span></label>
                                                                <Select
                                                                    isMulti
                                                                    name="customers"
                                                                    options={searchCustomers}
                                                                    value={this.state.multiCustomerJson}
                                                                    className="text-capitalize basic-multi-select mt-2"
                                                                    classNamePrefix="select"
                                                                    onChange={(e, i) => this.handleCustomersInputChange(e, i)}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Valid from <span className="text-danger">*</span></label>
                                                            <input id="startDate" type="datetime-local" name="startDate" value={startDate} required data-msg="Please enter Start Date" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Valid till <span className="text-danger">*</span></label>
                                                            <input id="expiryDate" type="datetime-local" name="expiryDate" value={expiryDate} required data-msg="Please enter Expire Date" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                {/* <div className="row">
                                                        <div className="col py-4">
                                                            <div className="i-checks">
                                                                <input id="checkboxCustom1" type="checkbox" name="status" {...(couponData.is_archive === 0 && { defaultChecked: true }) || (couponData.is_archive === 1 && { defaultChecked: false })} onChange={this.handleChangeChk} className="checkbox-template" />
                                                                <label htmlFor="checkboxCustom1">Active</label>
                                                            </div>
                                                        </div>
                                                    </div> */}
                                                <div className="form-group float-right">
                                                    <button onClick={this.handleSubmit} disabled={!this.isCouponReady()} className="btn btn-primary">Update Coupon</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div>
        );
    }
}
const mapStateToProps = (state: any) => {
    return {
        couponData: state.coupon.couponData,
        brands: state.store.brands,
        menus: state.menu.menus,
        groups: state.menu.groupsptions,
        items: state.menu.allActiveItems,
        message: state.coupon.message,
        isUpdated: state.coupon.isUpdated,
        stores: state.report.stores,
        searchCustomers: state.customer.searchCustomers
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        editCoupon: function (id: any, data: any) {
            dispatch(editCoupon(id, data));
        },
        getCoupon: function (id: number) {
            dispatch(getCoupon(id));
        },
        menusList: function () {
            dispatch(menusList())
        },
        groupsList: function () {
            dispatch(groupsListForMultiSelect("campaign"))
        },
        itemsListForMultiSelect: function () {
            dispatch(itemsListForMultiSelect("campaign"))
        },
        storesList: () => {
            dispatch(storesList())
        },
        searchCustomerByPhone: (phone: any) => {
            dispatch(searchCustomerByPhone(phone))
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(EditCoupon);