import React, { Component } from 'react';
import { Link, Redirect } from 'react-router-dom'
import { EditKMLZoneProps } from '../../interfaces/tradeZone'
import { connect } from 'react-redux'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import Map from './map';
import { logoutUser, handleZoneInput } from '../../redux'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import CheckChanges from '../../components/confirmOnLeave'
import { editkmlTradeZone, getkmlTradeZone } from '../../redux/actions/tradezoneAction';

class EditKMLTradeZone extends Component<EditKMLZoneProps, {
    kmlFile: any,
    kml: any, lat: any, lng: any, newlat: any,
    newlng: any, [x: number]: any
}> {
    constructor(props: any) {
        super(props);
        this.state = {
            kmlFile: null,
            kml: "",
            lat: 0,
            lng: 0,
            newlat: 0,
            newlng: 0,
        }
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
    }
    componentDidMount() {
        let id = this.props.match.params.id;
        this.props.getTradeZone(id)
        document.title = "SimplexYum | KML Zones"
    }
    UNSAFE_componentWillReceiveProps(nextProps: any) {
        this.setState({
            kml: nextProps.tradeZoneData.trade_zone_shape,
            lat: nextProps.tradeZoneData.lat,
            lng: nextProps.tradeZoneData.lng,
        })
    }
    isZoneReady = () => {
        const { tradezonename } = this.props;
        return (tradezonename !== "");
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        this.setState({
            [event.target.name]: event.target.value
        });
    }
    fileSelectedHandler = (e: any) => {
        this.setState({ kmlFile: e.target.files })
    }
    handleSubmit = () => {
        let { tradezonename } = this.props;
        let id = this.props.match.params.id;
        let { kml, kmlFile, lat, lng } = this.state;
        const data: any = new FormData();
        if (this.state.kmlFile) {
            data.append('file', kmlFile[0]);
        }
        data.append('trade_zone_name', tradezonename);
        data.append('trade_zone_shape', kml);
        data.append('lat', lat);
        data.append('lng', lng);
        data.append('is_active', 1);
        this.props.editTradeZone(id, data);
    }
    handleMapData = (obj: any) => {    //to Get the values from the map component and set in state
        this.setState({
            lat: obj.lat,
            lng: obj.lng
        })
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const { tradezonename } = this.props;
        return (
            <div className="page">
                <CheckChanges path="/edit-kml" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">KML Zones Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/kml" className="text-primary">KML Zones</Link></li>
                                <li className="breadcrumb-item active">Edit Zone</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Trade Zone name <span className="text-danger">*</span></label>
                                                            <input id="tradezonename" type="text" name="tradezonename" value={tradezonename} required className="input-material" onChange={this.props.handleZoneInput} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div style={{ width: '100%' }}>
                                                    <div className="row">
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label htmlFor="">Latitude</label>
                                                                <input type="text" name="newlat" className="form-control" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label htmlFor="">Longitude</label>
                                                                <input type="text" name="newlng" className="form-control" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <Map
                                                        data={this.state}
                                                        center={{ lat: this.state.newlat, lng: this.state.newlng }}
                                                        kml={this.state.kml}
                                                        onSetData={this.handleMapData}
                                                        google={this.props.google}
                                                        height='400px'
                                                        zoom={13}
                                                    />
                                                </div>
                                                <div className="row">
                                                    <div className="col-md-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label mb-3">KML File</label>
                                                            <input id="files" type="file" name="files" className="form-control-file" onChange={this.fileSelectedHandler} />
                                                        </div>
                                                    </div>
                                                    <div className="col-md-8 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label ">KML Link</label>
                                                            <input id="kml" type="text" name="kml" defaultValue={this.state.kml} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="form-group d-flex justify-content-end mt-4">
                                                    {/* <input type="reset" value="cancel" className="btn btn-danger mr-2" /> */}
                                                    <button onClick={this.handleSubmit} disabled={!this.isZoneReady()} className="btn btn-primary">Update TradeZone</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>

                </div>
            </div >
        );
    }
}
;
const mapStateToProps = (state: any) => {
    return {
        tradeZoneData: state.tradezone.tradeZoneData,
        tradezonename: state.tradezone.tradezonename
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: () => { dispatch(logoutUser()); },
        handleZoneInput: (event: any) => { dispatch(handleZoneInput(event)) },
        editTradeZone: (id: any, data: any) => { dispatch(editkmlTradeZone(id, data)); },
        getTradeZone: (id: number) => { dispatch(getkmlTradeZone(id)) }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(EditKMLTradeZone);