.custom-classname.react-toggle--checked .react-toggle-track {
    background-color: #20A5D6;
  }
  .custom-classname.react-toggle--checked .react-toggle-track:hover {
    background-color: #20A5D6;
  }

  .search-box{
    padding: 2%;
    display: flex;
    border: 1px solid #EAEAEA;
    align-items: center;
    height: 40px;
  }
@media (min-width: 576px){
  .nut-dialog {
    max-width: 1200px !important;
  }
}