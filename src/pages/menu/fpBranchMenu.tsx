import React, { Component } from 'react';
import { Redirect, Link } from 'react-router-dom'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import { connect } from 'react-redux'
import { getAllMenuList, logoutUser,updateItemAvailability,statusChangeReasons, updateItemStatus, searchMenuItem, searchGroup, searchCombos, searchModifiers,syncBranchMenuWithStore} from '../../redux';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import Toggle from 'react-toggle'
import 'react-tabs/style/react-tabs.css';
import "react-toggle/style.css"
import './menuStyle.css'
import Select from 'react-select'
import { storesList } from '../../redux/actions/reportAction';
import { searchSubGroup, searchVariants } from '../../redux/actions/menuAction';
import store from '../../redux/store';
interface MenuState {
    [x: number]: any,
    activeTab: any, status: any,
    statusReason: any,
    data: any,
    store: any
    is_fp_active:any,
    isavailable:any,
    date:any,
    selectedtype:any,
    menu_item_id:any,
    id:any,
    store_id:any;  
}
interface MenuProps {
    history: any
    groupsData: any,
    subgroupsData:any,
    itemsData: any,
    variantsData: any,
    combosData: any,
    modifiersData: any,
    searchItemData: any,
    searchVariantData:any,
    searchGroupData: any,
    searchSubGroupData: any,
    searchModData: any,
    searchComboData: any,
    itemStatusReasons: any[],
    stores: any[],
    searchMenuItem: (value: any, data: any) => {}
    searchGroup: (value: any, data: any) => {}
    searchCombo: (value: any, data: any) => {}
    updateItemAvailability:(id: any, type: any,availability:any,date:any,store:any,id_simp:any)=>{}
    searchModifiers: (value: any, data: any) => {}
    storesList: () => {},
    getAllMenuList: (store_id: any) => {},
    syncBranchMenuWithStore: (store_id: any) => {},
    statusChange: () => {},
    updateItemStatus: (data: any, history: any) => {}
}
class FpBranchMenu extends Component<MenuProps, MenuState> {
    openModal: any;
    closeModal: any;
    constructor(readonly props: any) {
        super(props);
        this.state = {
            activeTab: 0,
            status: false,
            data: {},
            statusReason: "",
            store: "",
            is_fp_active:" ",
            isavailable:'',
            date:'',
            menu_item_id:'',
            selectedtype:'',
            id:'',
            store_id:'',
        }
        this.handleToggleChange = this.handleToggleChange.bind(this);
        this.saveLogsForToggle = this.saveLogsForToggle.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
    }
    componentDidMount() {
        this.props.statusChange();
        this.props.storesList();
        document.title = "DominosCMS | FP Branch Menu"
    }
    handleStoresInputChange = (e: any, index: any) => {
        if (e && e.value > 0) {
            this.props.getAllMenuList(e.value);
            this.setState({ store: e.fp_branch_code,store_id:e.value });
        } else {
            this.setState({ store: " ",store_id:" " });
        }
        //console.log("E ", e.value);
    };
    handleDate =(e:any)=>{
        this.setState({date:e.target.value})

    }
    handleToggleChange(e: any, data: any, type:any, id:any, simp_id:any) {
        this.openModal.click();
        this.setState({
            data: data,
            selectedtype:type,
            menu_item_id:id,
            id:simp_id,
            is_fp_active: e.target.checked == false ? 0 : 1
        })
    }
    handleAvailability = (e:any) => {
        this.setState({isavailable:e.target.value})
    }

    handleConfirmAvailability= () => {
    
            let newDate =`${this.state.date}:00Z`
            this.props.updateItemAvailability(this.state.menu_item_id, this.state.selectedtype ,this.state.isavailable,newDate,this.state.store,this.state.id);
            
            
        }

    
    saveLogsForToggle() {
        let { data, is_fp_active, statusReason, store } = this.state;
        let body: any = {
            is_fp_active: is_fp_active,
            reason: statusReason,
            status: is_fp_active == 1 ? "UnBlock" : "Block",
            store_id: store
        }
        if (data.group_id) {
            body.group_id = data.group_id.group_id
        } else if (data.subgroup_id) {
            body.subgroup_id = data.subgroup_id.id
        }  else if (data.menu_item_id) {
            body.menu_item_id = data.menu_item_id.menu_item_id
        } else if(data.item_variant_id){
            body.item_variant_id = data.item_variant_id.id
        } else if (data.combo_id) {
            body.combo_id = data.combo_id.combo_id
        } else if (data.modifier_id) {
            body.modifier_id = data.modifier_id.modifier_id
        }
        this.props.updateItemStatus(body, this.props.history);
        this.closeModal.click();
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        this.setState({
            [event.target.name]: event.target.value
        });
        if (event.target.name == "menu_item") {
            this.props.searchMenuItem(event.target.value, this.props.itemsData)
        } else if (event.target.name == "menu_item_variant") {
            this.props.searchVariants(event.target.value, this.props.variantsData)
        } else if (event.target.name == "group") {
            this.props.searchGroup(event.target.value, this.props.groupsData)
        } else if (event.target.name == "subgroup") {
            this.props.searchSubGroup(event.target.value, this.props.subgroupsData)
        }  else if (event.target.name == "combo") {
            this.props.searchCombo(event.target.value, this.props.combosData)
        } else if (event.target.name == "modifier") {
            this.props.searchModifiers(event.target.value, this.props.modifiersData)
        }
    }
    isSaveStatusReady = () => {
        const { statusReason,is_fp_active } = this.state;
        if(is_fp_active == 0){
            return (statusReason !== "");
        }else{
            return true;
        } 
    }
    changeTab = (index: any) => {
        this.setState({
            activeTab: index
        })
        //this.props.getAllMenuList(this.state.store)
    }
    handleConfirm=()=>{
        if(this.state.store_id){
            this.props.syncBranchMenuWithStore(this.state.store_id)

        }
        else{
            alert('Please Select Store')

        }
    }
    render() {
        let roleId

        if (localStorage.token) {
            let tokendata: any = jwt.decode(localStorage.token)
             roleId = tokendata.role_id;
            
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        let { searchGroupData, searchSubGroupData, searchItemData,searchVariantData, searchComboData, itemStatusReasons, searchModData } = this.props;
        let { is_fp_active } = this.state;
        return (
            <div className="page">
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/* <!-- Page Header--> */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Food Panda Menu Management</h4>
                                </div>
                            </div>
                        </header>
                        <section className="tables pt-4">
                            <div className="container-fluid">
                                <div className="row form-group">
                                    <label
                                        className="col-md-3 col-sm-3 form-control-label font-weight-bold"
                                        style={{ paddingTop: "0.5em" }}
                                    >
                                        Stores
                                    </label>
                                    <div className="col-md-9 col-sm-9">
                                        <Select
                                            //isMulti
                                            name="storeType"
                                            isClearable
                                            options={this.props.stores}
                                            className="text-capitalize select mt-2"
                                            classNamePrefix="select"
                                            onChange={(e, i) => this.handleStoresInputChange(e, i)}
                                        />
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col">
                                        <div className="card">

                                            <div className="card-body p-0 mb-5">
                                                <Tabs
                                                    selectedIndex={this.state.activeTab}
                                                    onSelect={(index) =>
                                                        this.changeTab(index)
                                                    }
                                                >
                                                    <TabList
                                                        style={{
                                                            background: "#20a5d6",
                                                            color: "#FFF",
                                                            padding: "10px",
                                                        }}
                                                    >
                                                       
                                                        <Tab>Menu Items</Tab>

                                                        <Tab>Combos</Tab>
                                                        <Tab>Modifiers</Tab>

                                                        { 
                                                            (roleId== 1 || roleId== 2 ) &&
                                                            <button  title="Sync Menu" data-toggle="modal" data-target={`#sync`} style={{ display:"inline", marginLeft:'70%'}} className="sync_button btn btn-outline-info"><img title="Sync Menu with Aggregator" alt="sync menu" className="rounded-circle" src={process.env.PUBLIC_URL + '/assets/img/sync.png'} width="20px" height="20px"/></button>
                                                        }
                                                        <div id={`sync`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-dark">
                                                            <div role="document" className="modal-dialog">  
                                                                <div className="modal-content">
                                                                    <div className="modal-header">
                                                                        <h4 id="exampleModalLabel" className="modal-title">Sync Food Panda Branch Menu With Main Menu</h4>
                                                                        <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                                                                    </div>
                                                                    <div className="modal-body">
                                                                        <p>Are you sure,you want to Sync Branch Menu With Main Menu?</p></div>
                                                                    <div className="modal-footer">
                                                                        <button type="button" data-dismiss="modal" className="btn btn-secondary">Close</button>
                                                                    <button  onClick={()=>this.handleConfirm()}className="btn btn-primary">Confirm</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </TabList>

                                                  
                                                    <TabPanel style={{ marginTop: '2em' }}>
                                                        <div className="container-fluid">

                                                            <div className="row">
                                                                <div className="col-lg-12 col-12">
                                                                    <div className="card-header">
                                                                        <div className="row">
                                                                            <div className="col-lg-6">
                                                                                <strong>All Items</strong>
                                                                            </div>
                                                                            <div className="col-lg-6">
                                                                                <div className="search-box">
                                                                                    <label className="mb-0">
                                                                                        <i className="fa fa-search"></i>
                                                                                    </label>
                                                                                    <input id="menu_item" type="text" name="menu_item" style={{ border: 'none', marginLeft: '10px', width: '100%' }} required data-msg="Please enter item" onChange={this.handleInputChange} />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div className="card">
                                                                        <div className="card-body">
                                                                            <div className="row">
                                                                                {searchItemData.length > 0 ? searchItemData.map((element: any, index: any) => {
                                                                                    return (
                                                                                        <div className="col-lg-6 my-2">
                                                                                            <div className="d-flex justify-content-between">
                                                                                                <div>{element.menu_item_id.item_name}</div>
                                                                                                <div>
                                                                                                    <Toggle
                                                                                                        className='custom-classname'
                                                                                                        checked={element.is_fp_active == 0 ? false : true}
                                                                                                        icons={false}
                                                                                                        onChange={(e: any) => this.handleToggleChange(e, element,"menuitem",(`${element.menu_item_id.item_name}_${element.menu_item_id.menu_item_id}`).trim(),element.menu_item_id.menu_item_id)} />
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    )
                                                                                }) :
                                                                                    <div className="col-12 text-center">
                                                                                        <h5>Menu Items not found</h5>
                                                                                    </div>}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </TabPanel>
                                                   
                                                    <TabPanel style={{ marginTop: '2em' }}>
                                                        <div className="container-fluid">
                                                            <div className="row">
                                                                <div className="col-lg-12 col-12">
                                                                    <div className="card-header">
                                                                        <div className="row">
                                                                            <div className="col-lg-6">
                                                                                <strong>All Combos</strong>
                                                                            </div>
                                                                            <div className="col-lg-6">
                                                                                <div className="search-box">
                                                                                    <label className="mb-0">
                                                                                        <i className="fa fa-search"></i>
                                                                                    </label>
                                                                                    <input id="combo" type="text" name="combo" style={{ border: 'none', marginLeft: '10px', width: '100%' }} required data-msg="Please enter combo" onChange={this.handleInputChange} />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div className="card">
                                                                        <div className="card-body">
                                                                            <div className="row">
                                                                                {searchComboData.length > 0 ? searchComboData.map((element: any, index: any) => {
                                                                                    return (
                                                                                        <div className="col-lg-6 my-2">
                                                                                            <div className="d-flex justify-content-between">
                                                                                                <div>{element.combo_id.combo_name}</div>
                                                                                                <div>
                                                                                                    <Toggle
                                                                                                        className='custom-classname'
                                                                                                        checked={element.is_fp_active == 0 ? false : true}
                                                                                                        icons={false}
                                                                                                        onChange={(e: any) => this.handleToggleChange(e, element,"combo",`${(element.combo_id.pos_code).trim()}`,element.combo_id.combo_id)} />
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    )
                                                                                }) :
                                                                                    <div className="col-12 text-center">
                                                                                        <h5 >Combos not found</h5>
                                                                                    </div>}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </TabPanel>

                                                    <TabPanel style={{ marginTop: '2em' }}>
                                                        <div className="container-fluid">
                                                            <div className="row">
                                                                <div className="col-lg-12 col-12">
                                                                    <div className="card-header">
                                                                        <div className="row">
                                                                            <div className="col-lg-6">
                                                                                <strong>All Modifiers</strong>
                                                                            </div>
                                                                            <div className="col-lg-6">
                                                                                <div className="search-box">
                                                                                    <label className="mb-0">
                                                                                        <i className="fa fa-search"></i>
                                                                                    </label>
                                                                                    <input id="modifier" type="text" name="modifier" style={{ border: 'none', marginLeft: '10px', width: '100%' }} required data-msg="Please enter modifier" onChange={this.handleInputChange} />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div className="card">
                                                                        <div className="card-body">
                                                                            <div className="row">
                                                                                {searchModData.length > 0 ? searchModData.map((element: any, index: any) => {
                                                                                    return (
                                                                                        <div className="col-lg-6 my-2">
                                                                                            <div className="d-flex justify-content-between">
                                                                                                <div>{element.modifier_id.modifier_name}</div>
                                                                                                <div>
                                                                                                    <Toggle
                                                                                                        className='custom-classname'
                                                                                                        checked={element.is_fp_active == 0 ? false : true}
                                                                                                        icons={false}
                                                                                                        onChange={(e: any) => this.handleToggleChange(e, element,"modifier",`${(element.modifier_id.pos_code).trim()}-ITEM`,element.modifier_id.modifier_id)} />
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    )
                                                                                }) :
                                                                                    <div className="col-12 text-center">
                                                                                        <h5>Modifiers not found</h5>
                                                                                    </div>}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </TabPanel>
                                                </Tabs>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
                {/* End Print Preview */}
                <div style={{ display: "none" }}>
                    <button ref={el => this.openModal = el} data-toggle="modal" data-target={`#saveLogsNew`} className="btn btn-outline-info ml-2"><i className="fa fa-file-pdf-o"></i></button>
                </div>
                <div id={`saveLogs`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">{is_fp_active == 1 ? "UnBlock" : "Block"} Item</h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12">
                                        <h6>Are you sure,you want to {is_fp_active == 1 ? "UnBlock" : "Block"} the Item?</h6>
                                    </div>
                                    {is_fp_active == 0 &&
                                        <div className="col-12">
                                            <div className="form-group">
                                                <select
                                                    name="statusReason"
                                                    onChange={this.handleInputChange}
                                                    className="form-control mt-2">
                                                    <option value="">Select Reason</option>
                                                    {itemStatusReasons &&
                                                        itemStatusReasons.map((reason: any, index: any) => (
                                                            <option key={index} defaultValue={this.state.statusReason} value={reason.id}>{reason.reason}</option>
                                                        ))
                                                    }
                                                </select>

                                            </div>
                                        </div>
                                    }
                                </div>
                                <div className="form-group d-flex justify-content-end mt-4">
                                    <button className='btn btn-primary' disabled={!this.isSaveStatusReady()} onClick={() => this.saveLogsForToggle()}>Save</button>
                                    {/*  */}
                                </div>
                            </div>
                            <div style={{ display: "none" }}>
                                <button ref={el => this.closeModal = el} type="button" data-dismiss="modal" className="btn btn-info" />
                            </div>
                        </div>
                    </div>
                </div>

                <div id={`saveLogsNew`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">

                    <div role="document" className="modal-dialog">
                            <div className="modal-content">
                                <div className="modal-header">
                            
                                    <h4 id="exampleModalLabel" className="modal-title">Food Panda Item Availability</h4>
                                    <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                                </div>
                                <div className="modal-body">
                                <div className="col-12">
                                            <div className="form-group">

                                                    <select
                                                    name="fp_store_status"
                                                    value={this.state.isavailable} onChange={this.handleAvailability}
                                                    className="form-control mt-2">
                                                    <option value="">Select Status</option>
                                                    <option value={"true"}>{"AVAILABLE"}</option>
                                                    <option value={"false"}>{"UNAVAILABLE"}</option>
                                                    
                                                    {/* <option value={"CLOSED_UNTIL"}>{"CLOSED_UNTIL"}</option> */}
                                                </select>

                                            </div>
                                            {  
                                            (this.state.isavailable == 'false')?
                                            
                                                <>
                                                {/* <span>Will Be Avaiable From</span> */}
                                            {/* <input value={this.state.date} onChange={this.handleDate} id="storeopen" type="datetime-local" name="storeopen"  required data-msg="Please enter Opening Time" className="input-material"  />  */}
                                            </>
                                            :<></>
                                            }
                                            

                                        </div>
                                </div>

                                <div className="modal-footer">
                                    <button type="button" data-dismiss="modal" className="btn btn-secondary" onClick={()=>this.setState({isavailable:""})}>Close</button>
                                    {
                                        (this.state.isavailable == 'true' ||  this.state.isavailable == 'false')?
                                        <button type="button" data-dismiss="modal" className="btn btn-primary" onClick={()=>this.handleConfirmAvailability()}>Confirm</button>
                                        :
                                        <button type="button" data-dismiss="modal" className="btn btn-primary"disabled>Confirm</button>

                                    }

                                </div>
                            </div>
                    </div>
                </div>
            </div >
        );
    }
}
;
const mapStateToProps = (state: any) => {
    return {
        groupsData: state.menu.groupsData,
        subgroupsData: state.menu.subgroupsData,
        itemsData: state.menu.itemsData,
        combosData: state.menu.combosData,
        modifiersData: state.menu.modifiersData,
        variantsData: state.menu.variantsData,
        searchItemData: state.menu.searchItemData,
        searchModData: state.menu.searchModData,
        searchGroupData: state.menu.searchGroupData,
        searchSubGroupData: state.menu.searchSubGroupData,
        searchComboData: state.menu.searchComboData,
        searchVariantData: state.menu.searchVariantData,
        itemStatusReasons: state.menu.statusChangeReasons,
        stores: state.report.stores
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        getAllMenuList: (store_id: any) => {
            dispatch(getAllMenuList(store_id,true));
        },
        updateItemStatus: (data: any, history: any) => {
            dispatch(updateItemStatus(data, history))
        },
        statusChange: () => {
            dispatch(statusChangeReasons())
        },
        storesList: () => {
            dispatch(storesList())
        },
        searchGroup: (value: any, data: any) => {
            dispatch(searchGroup(value, data))
        },
        searchSubGroup: (value: any, data: any) => {
            dispatch(searchSubGroup(value, data))
        },
        searchMenuItem: (value: any, data: any) => {
            dispatch(searchMenuItem(value, data))
        },
        searchCombo: (value: any, data: any) => {
            dispatch(searchCombos(value, data))
        },
        searchModifiers: (value: any, data: any) => {
            dispatch(searchModifiers(value, data))
        },
        searchVariants: (value: any, data: any) => {
            dispatch(searchVariants(value, data))
        },
        updateItemAvailability: function (id: any, type: any,availability:any,date:any,store:any,id_simp:any) {
            dispatch(updateItemAvailability(id, type,availability,date,store,id_simp))
        },
        syncBranchMenuWithStore:function(store_id: any){
            dispatch(syncBranchMenuWithStore(store_id,'fpbranchmenu'))
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(FpBranchMenu);