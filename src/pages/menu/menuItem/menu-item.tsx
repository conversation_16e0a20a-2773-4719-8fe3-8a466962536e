import React, { Component } from 'react';
import { Redirect, Link } from 'react-router-dom'
import { BootstrapTable, TableHeaderColumn } from 'react-bootstrap-table';
import Topbar from '../../../components/topbar'
import Sidebar from '../../../components/sidebar'
import Footer from '../../../components/footer/main'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../../secret'
import { connect } from 'react-redux'
import { itemsList, blockunblockitem, logoutUser, statusChangeReasons } from '../../../redux'
import { MenuItemProps } from '../../../interfaces/menu';
import moment from 'moment';
import { currency } from '../../../client-config'
import { addStoreSpecificPrice, storesListForMultiSelect, updateItemSnooze } from '../../../redux/actions/menuAction';
import Select from 'react-select';
function priceFormatter(cell: any) {
    return `${currency} ${cell}`;
}
class ActionFormatter extends Component<{ row: any, itemStatusReasons: any[], }, { [x: number]: any, startDate: any, endDate: any, snoozeReason: any, storesJson: any[], allRemove: any }> {
    constructor(readonly props: any) {
        super(props);
        this.state = {
            startDate: '',
            endDate: '',
            snoozeReason: '',
            allRemove: false,
            storesJson: [
                {
                    store_id: null,
                    mrp: 0
                }
            ],
        }
        this.handleInputChange = this.handleInputChange.bind(this);
    }
    handleBlockUnblock = (id: any, is_active: any) => {
        this.props.data.blockunblockitem(id, is_active, this.props.row.item_name);
    };
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        this.setState({
            [event.target.name]: event.target.value
        });
    }
    isItemSnoozeReady = () => {
        const { startDate, endDate, snoozeReason } = this.state
        return (startDate !== "" && endDate !== "" && snoozeReason !== "");
    }
    // handle click event of the Remove button
    handleRemoveClick = (index: any) => {
        const list = this.state.storesJson;
        list.splice(index, 1);
        this.setState({ storesJson: list });
    };
    // handle click event of the Add button
    handleAddClick = (i: any) => {
        const list: any = this.state.storesJson;
        if (list[i]["store_id"] == null || list[i]["mrp"] == 0) {
            alert("Please fill all mandatory fields")
        } else {
            const newlist: any = { store_id: null, mrp: 0 }
            const stores: any = this.state.storesJson.concat(newlist)
            this.setState({ storesJson: stores });
        }
    };
    handleStoresInputChange = (e: any, index: any) => {
        if (e && e.target) {
            const { name, value } = e.target;
            const list: any = this.state.storesJson;
            list[index][name] = value !== "" ? value : 0;
            this.setState({ storesJson: list });
        } else {
            const list: any = this.state.storesJson;
            if (e && e.value > 0) {
                list[index]["store_id"] = e.value;
            } else {
                list[index]["store_id"] = null;
            }
            this.setState({ storesJson: list });
        }
    };
    handlePriceForSpecificStores = (menu_item_id: any) => {
        let { storesJson,allRemove } = this.state;
        if(allRemove){
            let data = {
                storesJson: null
            }
            this.props.addStoreSpecificPrice(menu_item_id, data);
        }else{
            if (storesJson[storesJson.length - 1]["store_id"] !== null && storesJson[storesJson.length - 1]["mrp"] !== 0) {
                let data = {
                    storesJson: JSON.stringify(storesJson)
                }
                this.props.addStoreSpecificPrice(menu_item_id, data);
            } else {
                alert("Please fill all mandatory fields")
            }
        }
    }
    handleSnooze = (menu_item_id: any) => {
        let { startDate, endDate, snoozeReason } = this.state;
        let data = {
            menu_item_id: menu_item_id,
            snooze_start_time: startDate,
            snooze_end_time: endDate,
            snoozeReason: snoozeReason
        }
        this.props.updateItemSnooze(menu_item_id, data);
    }
    handleCheckBoxes = (event: { target: { name: any; value: any; }; }) => {
        const { allRemove } = this.state;
        if (event.target.name == "allRemove") {
            this.setState({ allRemove: !allRemove })
        }
    }
    render() {
        const { row, itemStatusReasons, data } = this.props;
        let { storesJson,allRemove } = this.state;
        let today = moment().format("YYYY-MM-DDThh:mm");
        return (
            <div>
                <button title={row.is_active === 0 ? "UnBlock" : "Block"} data-toggle="modal" data-target={`#blockunblock${row.menu_item_id}`} className={row.is_active === 0 ? "btn btn-outline-success" : "btn btn-outline-danger"}><i className={row.is_active === 0 ? "fa fa-unlock" : "fa fa-lock"}></i></button>
                {/* <button title="View Details" data-toggle="modal" data-target={`#itemdetails${row.menu_item_id}`} className="btn btn-outline-info"><i className="fa fa-list"></i></button> */}
                <Link title="Edit Item" className="btn btn-outline-primary mx-2" to={`/edit-item/${row.menu_item_id}`}><i className="fa fa-edit"></i></Link>
                <Link to="#" title={!row.snooze_start_time ? "Snooze Item" : "End Snooze Item"} data-toggle="modal" data-target={`#snoozeItem${row.menu_item_id}`} className={"btn btn-outline-warning mx-2"}><i className={"fa fa-clock-o"}></i></Link>
                {/* <Link to="#" title={"Price for Specific Stores"} data-toggle="modal" data-target={`#diffPrice${row.menu_item_id}`} className={"btn btn-outline-warning"} onClick={() => row.storesJson && this.setState({ storesJson: JSON.parse(row.storesJson) })}><i className={"fa fa-money"}></i></Link> */}
                {/* <!-- Modal--> */}
                <div id={`itemdetails${row.menu_item_id}`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">Item Details</h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                <div className="row mb-2">
                                    <div className="col-6">
                                        <h4 className="d-inline">Item Name:</h4>
                                        <p>{row.item_name}</p>
                                    </div>
                                    {(row.image_url) &&
                                        <div className="col-6">
                                            <img className="rounded-circle" src={row.image_url} alt="menu_item" width="100%" height="150px" />
                                        </div>
                                    }
                                </div>
                                <div className="row">
                                    <div className="col-12">
                                        <div className="form-group text-capitalize">
                                            <h4>Variant</h4>
                                            <BootstrapTable data={row.item_size !== "" && JSON.parse(row.item_size)} hover>
                                                <TableHeaderColumn dataField='size' columnTitle isKey>Size</TableHeaderColumn>
                                                <TableHeaderColumn dataField='cost' dataFormat={priceFormatter} columnTitle>Item Cost Price</TableHeaderColumn>
                                                {/* <TableHeaderColumn dataField='price' dataFormat={priceFormatter} columnTitle>Item Sale Price</TableHeaderColumn> */}
                                                <TableHeaderColumn dataField='mrp' dataFormat={priceFormatter} columnTitle>Max Retail Price</TableHeaderColumn>
                                            </BootstrapTable>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button type="button" data-dismiss="modal" className="btn btn-danger">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
                {/* <!-- Block/Unblock Modal--> */}
                <div id={`blockunblock${row.menu_item_id}`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">{row.is_active === 0 ? "UnBlock" : "Block"} Menu Item</h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                <p style={{ textTransform: "none" }}>Are you sure,you want to {row.is_active === 0 ? "UnBlock" : "Block"} this item?</p>
                                {/* {(row.is_active === 1 && row.combos && row.combos.length > 0) && <p style={{ textTransform: "none" }}><b style={{ color: "red" }}>!</b> Follwing deals will be blocked due to this item block?</p>}
                                {(row.is_active === 1 && row.combos && row.combos.length > 0) && <ul>
                                    {row.combos && row.combos.map((obj: any) => {
                                        return (
                                            obj.combo_id.is_active == 1 && <li>{obj.combo_id.combo_name}</li>
                                        )
                                    })}

                                </ul>
                                } */}
                            </div>
                            <div className="modal-footer">
                                <button type="button" data-dismiss="modal" className="btn btn-secondary">Close</button>
                                <button onClick={() => this.handleBlockUnblock(row.menu_item_id, row.is_active)} className="btn btn-primary">Confirm</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div id={`snoozeItem${row.menu_item_id}`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">{!row.snooze_start_time ? "Snooze" : "End Snooze"} Menu Item </h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                {row.snooze_start_time &&
                                    <div className="row">
                                        <div className="col-12">
                                            <p>Are you sure,you want to End Snooze of this menu item?</p>
                                        </div>
                                    </div>}
                                <div className='row'>
                                    <div className="col-6">
                                        <div className="form-group">
                                            <label className="form-control-label mb-3">Snooze Start <span className="text-danger">*</span></label>
                                            <input type="datetime-local" name="startDate" disabled={row.snooze_start_time ? true : false} min={today} defaultValue={moment(row.snooze_start_time).utc(false).format('YYYY-MM-DDTHH:mm:ss')} required data-msg="Please enter Date" placeholder="DD/MM/YYYY" className="form-control" onChange={this.handleInputChange} />
                                        </div>
                                    </div>
                                    <div className="col-6">
                                        <div className="form-group">
                                            <label className="form-control-label mb-3">Snooze End <span className="text-danger">*</span></label>
                                            <input type="datetime-local" name="endDate" disabled={row.snooze_start_time ? true : false} min={today} defaultValue={moment(row.snooze_end_time).utc(false).format('YYYY-MM-DDTHH:mm:ss')} required data-msg="Please enter Date" placeholder="DD/MM/YYYY" className="form-control" onChange={this.handleInputChange} />
                                        </div>
                                    </div>
                                    {!row.snooze_start_time &&
                                        <div className="col-12">
                                            <div className="form-group">
                                                <label className="form-control-label">Snooze Reason <span className="text-danger">*</span></label>
                                                <select
                                                    name="snoozeReason"
                                                    onChange={this.handleInputChange}
                                                    className="form-control mt-2">
                                                    <option value="">Select Reason</option>
                                                    {itemStatusReasons &&
                                                        itemStatusReasons.map((reason: any, index: any) => (
                                                            <option key={index} value={reason.reason}>{reason.reason}</option>
                                                        ))
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>

                            <div className="modal-footer">
                                <button type="button" data-dismiss="modal" className="btn btn-secondary">Close</button>
                                <button onClick={() => this.handleSnooze(row.menu_item_id)} disabled={!row.snooze_start_time ? !this.isItemSnoozeReady() : false} className="btn btn-primary">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Diff pricing for Diff Stores */}
                <div id={`diffPrice${row.menu_item_id}`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">Price for Specific Stores </h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                <div>
                                    <input id="checkboxCustom1" type="checkbox" name="allRemove" checked={allRemove == true && true} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                    <label htmlFor="checkboxCustom1">All Store Remove</label>
                                </div>
                                {storesJson.map((x: any, ind: any) => {
                                    return (
                                        <div className='row' key={ind}>
                                            <div className="col-6" >
                                                <div className="form-group">
                                                    <label className="form-control-label">Select Store <span className="text-danger">*</span></label>
                                                    <Select
                                                        name="stores"
                                                        options={data.stores.filter((o1: any) => !storesJson.some((o2: any) => o1.value === o2.store_id))}
                                                        value={data.stores.map((element: any) => {
                                                            if (element.value == x.store_id) {
                                                                return { label: element.label, value: element.value }
                                                            }
                                                        })}
                                                        className="text-capitalize select mt-2"
                                                        classNamePrefix="select"
                                                        onChange={(e, i) => this.handleStoresInputChange(e, ind)}
                                                    />
                                                </div>
                                            </div>
                                            <div className="col-6">
                                                <div className="form-group">
                                                    <label className="form-control-label">Max Retail Price <span className="text-danger">*</span></label>
                                                    <input
                                                        name="mrp"
                                                        type="number" onWheel={(e: any) => e.target.blur()}
                                                        value={x.mrp}
                                                        data-msg="Please enter mrp"
                                                        className="input-material"
                                                        onChange={(e) => this.handleStoresInputChange(e, ind)}
                                                    />
                                                </div>
                                            </div>
                                            <div className="col-12 d-flex justify-content-end">
                                                <div className="form-group">
                                                    {storesJson.length !== 1 &&
                                                        <button className="btn btn-sm btn-outline-danger" onClick={() => this.handleRemoveClick(ind)}><i className="fa fa-trash"></i></button>}
                                                    {storesJson.length - 1 === ind && <button className="btn btn-sm btn-primary ml-2 float-right" onClick={() => this.handleAddClick(ind)}><i className="fa fa-plus"></i></button>}
                                                </div>
                                            </div>
                                        </div>
                                    )
                                })
                                }
                            </div>
                            <div className="modal-footer">
                                <button type="button" data-dismiss="modal" className="btn btn-secondary">Close</button>
                                <button onClick={() => this.handlePriceForSpecificStores(row.menu_item_id)} className="btn btn-primary">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div >
        )
    }
}
function actionFormatter(cell: any, row: any, props: any) {
    return (
        <ActionFormatter updateItemSnooze={props.updateItemSnooze} addStoreSpecificPrice={props.addStoreSpecificPrice} itemStatusReasons={props.itemStatusReasons} row={row} data={props} />
    );
}
function orderModesFormatter(cell: any, row: any, props: any) {
    return (
        <OrderModesFormatter row={row} data={props} />
    );
}
class OrderModesFormatter extends Component<{ row: any }, {}> {
    constructor(readonly props: any) {
        super(props);
    }
    IsJsonString = (str: any) => {
        try {
            JSON.parse(str);
        } catch (e) {
            return false;
        }
        return true;
    }
    render() {
        const { row } = this.props;
        return (
            <div>
                {row.item_mode &&
                    <button title="View Modes" data-toggle="modal" data-target={`#itemmodes${row.menu_item_id}`} className="btn btn-outline-info"><i className="fa fa-list"></i></button>
                }
                {/* <!-- Modal--> */}
                <div id={`itemmodes${row.menu_item_id}`} role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" className="modal fade text-left">
                    <div role="document" className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h4 id="exampleModalLabel" className="modal-title">Order Modes</h4>
                                <button type="button" data-dismiss="modal" aria-label="Close" className="close"><span aria-hidden="true">×</span></button>
                            </div>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12">
                                        <div className="form-group text-capitalize">
                                            <BootstrapTable version='4' data={(row.item_mode && this.IsJsonString(row.item_mode)) ? JSON.parse(row.item_mode) : ""} hover>
                                                <TableHeaderColumn dataField='label' columnTitle isKey>Order Mode</TableHeaderColumn>
                                            </BootstrapTable>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button type="button" data-dismiss="modal" className="btn btn-danger">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        )
    }
}
class MenuItem extends Component<MenuItemProps, {}> {
    constructor(readonly props: any) {
        super(props);
    }
    componentDidMount() {
        this.props.statusChangeReasons();
        this.props.itemsList()
        this.props.storesList();
        document.title = "DominosCMS | Menu Items"
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const options: any = {
            // sizePerPageList: [5,10,15],
            sizePerPage: 10, // which size per page you want to locate as default
            page: 1,
            pageStartIndex: 1, // where to start counting the pages
            paginationSize: 3,  // the pagination bar size.
            hideSizePerPage: true, //You can hide the dropdown for sizePerPage
            insertModal: () => { return <Redirect to="/add-item" /> },
            noDataText: 'Menu Items Not Found'
        };
        return (
            <div className="page">
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/* <!-- Page Header--> */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Items Management</h4>
                                </div>
                            </div>
                        </header>
                        <section className="tables">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col">
                                        <div className="card">
                                            <div className="card-body">
                                                <BootstrapTable version="4" data={this.props.data} search={true} pagination={this.props.data.length > 10 && true} options={options} exportCSV={true} insertRow csvFileName='menus.csv' hover>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='menu_item_id' csvHeader='#' width='70' dataSort={true} isKey>#</TableHeaderColumn>
                                                    {/* <TableHeaderColumn dataField='erp_id' csvHeader='ERP ID' width='100' columnTitle>ERP ID</TableHeaderColumn>
                                                    <TableHeaderColumn dataField='pos_code' csvHeader='POS Code' width='100' columnTitle>POS Code</TableHeaderColumn> */}
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='item_name' csvHeader='Item Name' width='200' columnTitle>Item name</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='group_name' csvHeader='Group Name' width='150' columnTitle>Group Name</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='menu_name' csvHeader='Menu Name' width='130' columnTitle>Menu</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='item_mode' csvHeader='Mode' width='100' dataFormat={orderModesFormatter} columnTitle>Mode</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='priority' csvHeader='Priority' width='100' columnTitle>Priority</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='recipe' csvHeader='recipe' width='130' columnTitle>Recipe</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='item_start_time' csvHeader='Item Start Time' dataFormat={timeFormatter} width='150'>Item Start Time</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='item_close_time' csvHeader='Item Close Time' dataFormat={timeFormatter} width='150'>Item Close Time</TableHeaderColumn>
                                                    {/* <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='hero_item' width='100' dataFormat={heroItemFormatter} export={false}>HeroItem</TableHeaderColumn> */}
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='is_snooze' width='100' dataFormat={SnoozeFormatter} export={false}>Snooze</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='is_suggestive_item' width='120' dataFormat={suggestiveItemFormatter} export={false}>Suggestive Item</TableHeaderColumn>
                                                    <TableHeaderColumn thStyle={{ fontSize: 15, whiteSpace: 'normal' }} tdStyle={{ fontSize: 15, whiteSpace: 'normal' }} dataField='action' width='250' dataFormat={actionFormatter} formatExtraData={this.props} export={false}>Action</TableHeaderColumn>
                                                </BootstrapTable>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div>
        );
    }
}
;
function heroItemFormatter(cell: any) {
    return (
        <div className="text-capitalize">
            <span {...(cell == 1 && { className: "badge badge-success p-2" })}>{cell == 1 && <i className="fa fa-check-square-o"></i>}</span>
        </div>
    )
}
function SnoozeFormatter(cell: any) {
    return (
        <div className="text-capitalize">
            <span {...(cell === 1 && { className: "badge badge-success p-2" })}>{cell === 1 && <i className="fa fa-check-square-o"></i>}</span>
        </div>
    )
}
function suggestiveItemFormatter(cell: any) {
    return (
        <div className="text-capitalize">
            <span {...(cell == 1 && { className: "badge badge-success p-2" })}>{cell == 1 && <i className="fa fa-check-square-o"></i>}</span>
        </div>
    )
}
function timeFormatter(cell: any) {
    return (
        <div>
            <span {...((cell !== "" && cell !== null) && { title: moment(cell).local().format('YYYY-MM-DD HH:mm') })}> {(cell !== "" && cell !== null) && moment(cell).local().format('YYYY-MM-DD HH:mm')} </span>
        </div>
    )
}
const mapStateToProps = (state: any) => {
    return {
        data: state.menu.items,
        itemStatusReasons: state.menu.statusChangeReasons,
        stores: state.menu.storesoptions,
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        itemsList: function () {
            dispatch(itemsList())
        },
        blockunblockitem: function (id: any, is_active: any, itemName: any) {
            dispatch(blockunblockitem(id, is_active, itemName))
        },
        statusChangeReasons: () => {
            dispatch(statusChangeReasons())
        },
        updateItemSnooze: (id: any, data: any) => {
            dispatch(updateItemSnooze(id, data))
        },
        addStoreSpecificPrice: (id: any, data: any) => {
            dispatch(addStoreSpecificPrice(id, data))
        },
        storesList: () => {
            dispatch(storesListForMultiSelect())
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(MenuItem);