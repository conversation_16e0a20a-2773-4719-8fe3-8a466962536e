import React, { Component } from 'react';
import { AddMenuItemProps, AddMenuItemState } from '../../../interfaces/menu'
import { connect } from 'react-redux'
import Footer from '../../../components/footer/main';
import Sidebar from '../../../components/sidebar';
import Topbar from '../../../components/topbar';
import jwt from 'jsonwebtoken'
import { secretKey } from '../../../secret'
import { addItems, channelList, checkDuplicateErpId, checkDuplicatePosCode, groupsList, logoutUser, menusList, statesList, storesListForMultiSelect } from '../../../redux'
import { Link, Redirect } from 'react-router-dom';
import CheckChanges from '../../../components/confirmOnLeave'
import moment from 'moment';
import Select from 'react-select';
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Loader from "react-loader-spinner";
import "react-loader-spinner/dist/loader/css/react-spinner-loader.css";
import { OrderingModes, subGroupListByGroupId, variantsList } from '../../../redux/actions/menuAction';
toast.configure();
class AddMenuItem extends Component<AddMenuItemProps, AddMenuItemState> {
    weeklyDays: { value: string; label: string; }[];
    constructor(props: any) {
        super(props);
        this.weeklyDays = [
            { value: 'MONDAY', label: 'Mon' },
            { value: 'TUESDAY', label: 'Tue' },
            { value: 'WEDNESDAY', label: 'Wed' },
            { value: 'THURSDAY', label: 'Thu' },
            { value: 'FRIDAY', label: 'Fri' },
            { value: 'SATURDAY', label: 'Sat' },
            { value: 'SUNDAY', label: 'Sun' },
        ];
        this.state = {
            itemname: "",
            group: "",
            subgroup: "",
            type: "",
            priority: "",
            images: null,
            hero_image: null,
            hero_mobile_image: null,
            hero_item: false,
            settime: 'alltime',
            tax_percent: '',
            itemstart: "",
            itemclose: "",
            state: "",
            mode: [],
            is_lsm: '0',
            flag: false,
            storesJson: [],
            metaTitle: "",
            metaDesc: "",
            is_featured: false,
            is_foodpanda:false,
            is_half_n_half: false,
            is_full_pizza: false,
            recipe: "",
            is_new: false,
            is_veg: false,
            is_hot: false,
            is_vegan: false,
            is_best: false,
            cross_sell: false,
            is_glutenFree: false,
            is_suggestive: false,
            channel: "",
            specific_days: false,
            daysTiming: [
                {
                    day: "",
                    label: "",
                    timePeriods: [{
                        open: "",
                        close: ""
                    }]
                }
            ],
            sizejson: [
                {
                    pos_code: "",
                    erp_id: "",
                    nutritional_info: "",
                    serving:"",
                    size: "",
                    cost: "",
                    mrp: "",
                    variant_id: "",
                    extra_price: "",
                    description: "",
                    alt_text: "",
                    image_url: null,
                    thumbnail_url: null,
                    order_modes_price: []
                }
            ]
        }
        this.handleSaveBtnClick = this.handleSaveBtnClick.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
        this.handleItemsByGroupId = this.handleItemsByGroupId.bind(this);
    }
    componentDidMount() {
        this.props.menusList();
        this.props.groupsList();
        this.props.storesList();
        this.props.OrderingModes();
        this.props.variantsList();
        this.props.channelList();
        document.title = "DominosCMS | Menu Items"
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        this.setState({
            [event.target.name]: event.target.value
        });
    }
    handleSpecificDaysCheck = (event: { target: { name: any; value: any; }; }) => {
        const { specific_days } = this.state;
        this.setState({ specific_days: !specific_days })
    }
    handleItemsByGroupId(event: { target: { name: any; value: any; }; }) {
        this.setState({
            group: event.target.value,
            subgroup: "",
            sizejson: [{
                pos_code: "",
                erp_id: "",
                nutritional_info: "",
                serving:"",
                size: "",
                cost: "",
                mrp: "",
                variant_id: "",
                extra_price: "",
                description: "",
                alt_text: "",
                image_url: null,
                thumbnail_url: null,
                order_modes_price: []
            }]
        });
        this.props.subGroupListByGroupId(event.target.value)
    }
    // handle click event of the Time Radio buttons
    handleChangeRad = (event: { target: { name: any; value: any; } }) => {
        this.setState({ [event.target.name]: event.target.value })
    }
    handleVariantInputChange = (e: any, index: any) => {
        const { name, value } = e.target;
        const list: any = this.state.sizejson;
        list[index][name] = value;
        this.setState({ sizejson: list });
        if (name == "pos_code") {
            this.props.checkDuplicatePosCode(value)
        }
        if (name == "erp_id") {
            this.props.checkDuplicateErpId(value)
        }
    };
    // handle click event of the Remove button
    handleRemoveClick = (index: any) => {
        const list = this.state.sizejson;
        list.splice(index, 1);
        this.setState({ sizejson: list });
    };
    // handle click event of the Add button
    handleAddClick = (i: any) => {
        const list: any = this.state.sizejson;
        let { duplicateItemErp, duplicateItemPos } = this.props;
        if (list[i].order_modes_price[list[i].order_modes_price.length - 1]) {
            if (list[i]["size"] == "" || list[i]["cost"] == "" || (list[i]["pos_code"] !== "" && duplicateItemPos == true) || (list[i]["erp_id"] !== "" && duplicateItemErp == true) || list[i]["image_url"] == null || list[i]["thumbnail_url"] == null || list[i].order_modes_price[list[i].order_modes_price.length - 1].mrp == 0) {
                alert("Please fill all mandatory variant fields")
            } else if (i == '9') {
                alert("Sorry you can select only 10 variants")
            } else {
                const newlist: any = { pos_code: "", erp_id: "", size: "", cost: "", mrp: "", variant_id: "", extra_price: "", description: "", image_url: null, thumbnail_url: null, order_modes_price: [] }
                this.state.mode && this.state.mode.forEach((elem) => {
                    let mode_id = elem.value;
                    let mode_label = elem.label;
                    let obj = {
                        item_variant_id: "",
                        order_mode_id: mode_id,
                        mrp: 0,
                        extra_price: 0,
                        label: mode_label,
                        value: mode_id
                    }
                    newlist.order_modes_price.push(obj);
                })
                const menu_items: any = this.state.sizejson.concat(newlist)
                this.setState({ sizejson: menu_items });
            }
        } else {
            alert("Please select at least one ordering mode!")
        }
    };
    itemFileSelectedHandler = (e: any, index: any) => {
        const list: any = this.state.sizejson;

        // if (e.target.files.length > 0) {

        //     list[index]["image_url"] = e.target.files;
        //     this.setState({ sizejson: list });
        // }
        if (e.target.files.length > 0) {
            var size = 153600;

            if (e.target.files[0].size > size) {

                toast.error("Image size too large. Upload image less then 150kb", { position: toast.POSITION.BOTTOM_RIGHT, hideProgressBar: false, autoClose: 3000 })

                this.setState({
                    flag: false
                })
            }
            else {
                list[index]["image_url"] = e.target.files;
                this.setState({ sizejson: list, flag: true });
            }
        }
    }
    thumbnailFileSelectedHandler = (e: any, index: any) => {
        const list: any = this.state.sizejson;
        // if (e.target.files.length > 0) {
        //     list[index]["thumbnail_url"] = e.target.files;
        //     this.setState({ sizejson: list });
        // }
        if (e.target.files.length > 0) {
            var size = 51200;

            if (e.target.files[0].size > size) {

                toast.error("Image size too large. Upload image less then 50kb", { position: toast.POSITION.BOTTOM_RIGHT, hideProgressBar: false, autoClose: 3000 })

                this.setState({
                    flag: false
                })
            }
            else {
                list[index]["thumbnail_url"] = e.target.files;
                this.setState({ sizejson: list, flag: true });
            }
        }

    }
    handleOrderModesInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            if (e.length < this.state.mode.length) {
                let tempsizeJson = this.state.sizejson;
                for (let i = 0; i < tempsizeJson.length; i++) {
                    let tempArr: any[] = tempsizeJson[i].order_modes_price;
                    let selectedItems = tempArr.filter(obj => e.find((s: any) => s.value === obj.value));
                    tempsizeJson[i].order_modes_price = selectedItems;
                }
                this.setState({ sizejson: tempsizeJson })
            } else {
                let tempsizeJson = this.state.sizejson;
                for (let i = 0; i < tempsizeJson.length; i++) {
                    let item_variant_id = this.state.sizejson[i].id;
                    let mode_id = e[this.state.sizejson[i].order_modes_price.length].value;
                    let mode_label = e[this.state.sizejson[i].order_modes_price.length].label;
                    let mrp = this.state.sizejson[i].mrp;
                    let extra_price = this.state.sizejson[i].extra_price;
                    let obj = {
                        item_variant_id: item_variant_id,
                        order_mode_id: mode_id,
                        order_mode: mode_label,
                        mrp: mrp ? mrp : 0,
                        extra_price: extra_price ? extra_price : 0,
                        label: mode_label,
                        value: mode_id
                    }
                    tempsizeJson[i].order_modes_price.push(obj);
                }
                this.setState({ sizejson: tempsizeJson })
            }
            this.setState({ mode: e });
        } else {
            this.setState({ mode: [] });
            let tempsizeJson = this.state.sizejson;
            for (let i = 0; i < tempsizeJson.length; i++) {
                tempsizeJson[i].order_modes_price = [];
            }
            this.setState({ sizejson: tempsizeJson })
        }
    };
    handleVariantPriceInputChange = (e: any, index: any, sizeJsonindex: any) => {
        const { name, value } = e.target;
        const list: any = this.state.sizejson;
        list[sizeJsonindex].order_modes_price[index][name] = value;
        this.setState({ sizejson: list });
    };
    handlePricingOrderModesInputChange = (e: any, indexing: any, ind: any) => {
        if (e && e.length > 0) {
            if (e.length === this.state.sizejson[ind].order_modes_price.length + 1) {
                if (this.state.sizejson[ind].order_modes_price.length == 0) {
                    let mode_id = e[this.state.sizejson[ind].order_modes_price.length].value;
                    let mode_label = e[this.state.sizejson[ind].order_modes_price.length].label;
                    // this.setState({ mode: e });
                    let tempsizeJson = this.state.sizejson;
                    let obj = {
                        item_variant_id: "",
                        order_mode_id: mode_id,
                        mrp: 0,
                        extra_price: 0,
                        label: mode_label,
                        value: mode_id
                    }
                    tempsizeJson[ind].order_modes_price.push(obj);
                    this.setState({ sizejson: tempsizeJson })
                } else {
                    if (this.state.sizejson[ind].order_modes_price[this.state.sizejson[ind].order_modes_price.length - 1].mrp > 0) {
                        let mode_id = e[this.state.sizejson[ind].order_modes_price.length].value;
                        let mode_label = e[this.state.sizejson[ind].order_modes_price.length].label;
                        // this.setState({ mode: e });
                        let tempsizeJson = this.state.sizejson;
                        let obj = {
                            item_variant_id: "",
                            order_mode_id: mode_id,
                            mrp: 0,
                            extra_price: 0,
                            label: mode_label,
                            value: mode_id
                        }
                        tempsizeJson[ind].order_modes_price.push(obj);
                        this.setState({ sizejson: tempsizeJson })
                    } else {
                        toast.error("Max retail price should not be 0,Please update", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    }
                }
            }
            else {
                console.log(indexing)
                let tempsizeJson = this.state.sizejson;
                let dubtempsizeJson = tempsizeJson[ind].order_modes_price.filter((item: any) => (item.order_mode_id != indexing.removedValue.value))
                tempsizeJson[ind].order_modes_price = dubtempsizeJson;
                this.setState({ sizejson: tempsizeJson })
            }
        } else {
            let tempsizeJson = this.state.sizejson;
            tempsizeJson[ind].order_modes_price = []
            this.setState({ sizejson: tempsizeJson })
        }
    };
    handleWeeklyDaysInputChange = (e: any, index: any, pindex?: any) => {
        if (e.target) {
            const { name, value } = e.target;
            const list: any = this.state.daysTiming;
            let timeP = list[index].timePeriods;
            timeP[pindex][name] = value;
            list[index].timePeriods = timeP;
            this.setState({ daysTiming: list });
        } else {
            console.log("e", e)
            const list: any = this.state.daysTiming;
            list[index]["day"] = e.value;
            list[index]["label"] = e.label;
            this.setState({ daysTiming: list });
        }
    };
    handleWeeklyDaysRemoveClick = (index: any, pindex?: any) => {
        const list = this.state.daysTiming;
        if (pindex >= 0) {
            let timePer = list[index].timePeriods;
            timePer.splice(pindex, 1);
            list[index].timePeriods = timePer;
            this.setState({ daysTiming: list });
        } else {
            list.splice(index, 1);
            this.setState({ daysTiming: list });
        }
    };
    handleweeklyDaysAddClick = (e: any, i: any, pindex?: any) => {
        const list: any = this.state.daysTiming;
        if (pindex >= 0) {
            if (list[i].timePeriods[list[i].timePeriods.length - 1].open == "" || list[i].timePeriods[list[i].timePeriods.length - 1].close == "") {

                toast.error("Please fill in selected day hours", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
            } else {
                let timePeriods = {
                    open: "",
                    close: ""
                }
                list[i].timePeriods.push(timePeriods)
                this.setState({ daysTiming: list });
            }
        } else {
            if (list[i]["day"] == "" || list[i].timePeriods[list[i].timePeriods.length - 1].open == "" || list[i].timePeriods[list[i].timePeriods.length - 1].close == "") {

                toast.error("Please fill in selected day timing", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
            } else {
                let timePeriods = {
                    open: "",
                    close: ""
                }
                const newlist: any = {
                    day: "",
                    timePeriods: []
                }
                newlist.timePeriods.push(timePeriods)
                const days: any = this.state.daysTiming.concat(newlist)
                this.setState({ daysTiming: days });
            }
        }
        e.preventDefault()
    };

    handleStoresInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ storesJson: e });
        } else {
            this.setState({ storesJson: [] });
        }
    };
    handleHeroItem = (event: { target: { name: any; value: any; }; }) => {
        const { hero_item } = this.state;
        this.setState({ hero_item: !hero_item })
    }

    handleFeatured = (event: { target: { name: any; value: any; }; }) => {
        const { is_featured } = this.state;
        this.setState({ is_featured: !is_featured })
    }

    handleFoodPanda = (event: { target: { name: any; value: any; }; }) => {
        const { is_foodpanda } = this.state;
        this.setState({ is_foodpanda: !is_foodpanda })
    }

    heroItemImageSelectedHandler = (e: any) => {
        if (e.target.files.length > 0) {
            this.setState({ hero_image: e.target.files })
        }
    }
    heroItemMobileImageSelectedHandler = (e: any) => {
        if (e.target.files.length > 0) {
            this.setState({ hero_mobile_image: e.target.files })
        }
    }
    checkMRPForAllModes = () => {
        let tempsizeJson = this.state.sizejson;
        let variants: any = [];
        for (let i = 0; i < tempsizeJson.length; i++) {
            let order_modes_price: any[] = tempsizeJson[i].order_modes_price;
            var filtered = order_modes_price && order_modes_price.filter(function (el) {
                return (el.mrp == 0 || el.mrp == "");
            });
            if (filtered.length > 0) {
                variants.push(tempsizeJson[i])
            }
        }
        if (variants.length > 0) {
            return false;
        } else {
            return true
        }
    }
    isMenuItemReady = () => {
        const { itemname, group, subgroup, channel, settime, itemstart, itemclose, hero_item, mode, hero_image, is_lsm, storesJson, flag, metaTitle, metaDesc } = this.state;
        let { duplicateItemPos, duplicateItemErp } = this.props;
        if(metaTitle == "" || metaDesc == "") {
            return false;
        }
        if (settime == 'customtime') {
            if (hero_item == false) {
                if (is_lsm == "0") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && itemstart !== "" && itemclose !== "" && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                } else if (is_lsm == "1") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && itemstart !== "" && itemclose !== "" && storesJson.length > 0 && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                }
                else if (is_lsm == "2") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && itemstart !== "" && itemclose !== "" && storesJson.length > 0 && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                }
            } else {
                if (is_lsm == "0") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && hero_image !== null && itemstart !== "" && itemclose !== "" && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                } else if (is_lsm == "1") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && hero_image !== null && itemstart !== "" && itemclose !== "" && storesJson.length > 0 && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                }
                else if (is_lsm == "2") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && hero_image !== null && itemstart !== "" && itemclose !== "" && storesJson.length > 0 && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                }
            }
        } else {
            if (hero_item == false) {
                if (is_lsm == "0") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                } else if (is_lsm == "1") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && storesJson.length > 0 && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                }
                else if (is_lsm == "2") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && storesJson.length > 0 && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                }
            } else {
                if (is_lsm == "0") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && hero_image !== null && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                } else if (is_lsm == "1") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && hero_image !== null && storesJson.length > 0 && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                }
                else if (is_lsm == "2") {
                    return (itemname !== "" && channel !== "" && group !== "" && subgroup !== "" && hero_image !== null && storesJson.length > 0 && mode.length > 0 && duplicateItemPos !== true && duplicateItemErp !== true && this.checkMRPForAllModes());
                }
            }
        }
    }
    isUnique = (arr: any) => {
        const len = arr.length;
        for (let i = 0; i < len; i++) {
            for (let j = 0; j < len; j++) {
                // if the elements match, this wouldn't be a unique array
                if ((arr[i].pos_code !== "" && arr[j].pos_code !== "")) {
                    if (i !== j && (arr[i].pos_code === arr[j].pos_code)) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    blockSpecialCharacters = () => {
        var checkString = this.state.itemname;
        if (checkString != "") {
            if (/[%]/.test(checkString)) {
                alert("Your item name has '%' or '_' sign. \nIt is not allowed.\nPlease remove it and try again.");
                return (false);
            } else {
                return true
            }
        }
    }
    handleCheckBoxes = (event: { target: { name: any; checked: boolean; } }) => {
        
        let { name, checked } = event.target;
        this.setState({ [name]: checked });

    }
    handleSaveBtnClick = (event: any) => {
        let { itemname, group, subgroup, channel, type, specific_days, cross_sell, daysTiming, is_featured, is_foodpanda, is_half_n_half, is_full_pizza, priority, mode, hero_item, hero_image, hero_mobile_image, sizejson, settime, itemstart, itemclose, is_lsm, storesJson, metaTitle, metaDesc, recipe, is_new, is_veg, is_hot, is_vegan, is_glutenFree, is_best, is_suggestive } = this.state;
        const data: any = new FormData();
        const Days = JSON.stringify(daysTiming);
        let itemNamevalid = this.blockSpecialCharacters();
        if (itemNamevalid) {
            if (this.state.sizejson) {
                for (var x = 0; x < this.state.sizejson.length; x++) {
                    if (this.state.sizejson[x].image_url !== null) {
                        var file = this.state.sizejson[x].image_url[0];
                        var newFileName = file.name.split(".")[0] + `_variant_${x}.` + file.name.split(".")[1];
                        data.append('files', file, newFileName)
                    }

                }
                for (var x = 0; x < this.state.sizejson.length; x++) {
                    if (this.state.sizejson[x].thumbnail_url !== null) {
                        var file = this.state.sizejson[x].thumbnail_url[0];
                        var newFileName = file.name.split(".")[0] + `_thumbnail_${x}.` + file.name.split(".")[1];
                        data.append('files', file, newFileName)
                    }
                }
            }
            const SizeJsonString = JSON.stringify(sizejson)
            if (hero_image) {
                var file = hero_image[0];
                var newFileName = file.name.split(".")[0] + "_hero_image." + file.name.split(".")[1];
                data.append('files', file, newFileName);
            }
            if (hero_mobile_image) {
                var file = hero_mobile_image[0];
                var newFileName = file.name.split(".")[0] + "_hero_mobile_image." + file.name.split(".")[1];
                data.append('files', file, newFileName);
            }
            data.append('item_name', itemname)
            // if (erpid !== null) {
            //     data.append('erp_id', erpid)
            // }
            // if (poscode !== null) {
            //     data.append('pos_code', poscode)
            // }
            if (hero_item === true) {
                data.append('hero_item', 1)
            }
            if (is_lsm == '0') {
                data.append('is_lsm', 0)
            } else if (is_lsm == '1') {
                data.append('is_lsm', 1)
                data.append('stores_json', JSON.stringify(storesJson))
            }
            else if (is_lsm == '2') {
                data.append('is_lsm', 2)
                data.append('stores_json', JSON.stringify(storesJson))
            }
            data.append('item_channel_id', channel)
            data.append('item_group_id', group)
            data.append('subgroup_id', subgroup)
            data.append('item_mode', JSON.stringify(mode))
            data.append('item_type', type)
            data.append('priority', priority)
            data.append('meta_title', metaTitle)
            data.append('meta_description', metaDesc)
            data.append('recipe', recipe)
            data.append("is_featured", is_featured ? 1 : 0)
            data.append("is_foodpanda", is_foodpanda ? 1 : 0)
            data.append("is_half_n_half", is_half_n_half ? 1 : 0)
            data.append("is_full_pizza", is_full_pizza ? 1 : 0)
            if(!(is_half_n_half || is_full_pizza)){
                data.append("cross_sell", cross_sell ? 1 : 0)
            }
            else {
                data.append("cross_sell", 0)
            }
            // data.append("is_suggestive_item", is_suggestive ? 1 : 0)
            data.append("is_new", is_new ? 1 : 0)
            data.append("is_veg", is_veg ? 1 : 0)
            data.append("is_best", is_best ? 1 : 0)
            data.append("is_hot", is_hot ? 1 : 0)
            data.append("is_vegan", is_vegan ? 1 : 0)
            data.append("is_glutenFree", is_glutenFree ? 1 : 0)
            if (settime === 'customtime') {
                data.append('item_start_time', moment(itemstart).utc(false))
                data.append('item_close_time', moment(itemclose).utc(false))
            }
            if (sizejson[sizejson.length - 1]["size"] !== "" && sizejson[sizejson.length - 1]["variant_id"] !== "" && sizejson[sizejson.length - 1]["cost"] !== "" && sizejson[sizejson.length - 1]["image_url"] !== null && sizejson[sizejson.length - 1]["thumbnail_url"] !== null && (sizejson[sizejson.length - 1].order_modes_price.length > 0 && sizejson[sizejson.length - 1].order_modes_price[sizejson[sizejson.length - 1].order_modes_price.length - 1].mrp > 0)) {
                if (this.isUnique(sizejson)) {
                    data.append('itemSizes', SizeJsonString)
                    if (specific_days) {
                        if (daysTiming[daysTiming.length - 1]["day"] !== "" && daysTiming[daysTiming.length - 1].timePeriods[daysTiming[daysTiming.length - 1].timePeriods.length - 1].open !== "" && daysTiming[daysTiming.length - 1].timePeriods[daysTiming[daysTiming.length - 1].timePeriods.length - 1].close !== "") {
                            data.append('serving_hours', Days)
                            this.props.addItems(data);
                        } else {
                            alert("Please fill in selected day timing")
                        }
                    } else {
                        this.props.addItems(data);
                    }
                } else {
                    toast.error("Please fill unique POS codes", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                }
            } else {
                alert("Please fill all mandatory variant fields")
            }
        }
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const { groups, subGroups, variants, channels } = this.props;
        const { sizejson, settime, hero_item, is_lsm, specific_days, daysTiming } = this.state;
        return (
            <div className="page">
                <CheckChanges path="/add-item" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Menu Item Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/menu-items" className="text-primary">Menu Items</Link></li>
                                <li className="breadcrumb-item active">Add Menu Item</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col-lg-5 col-sm-12 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Item name <span className="text-danger">*</span></label>
                                                            <input id="itemname" type="text" name="itemname" required data-msg="Please enter Item Name" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-6 col-6" >
                                                        <div>
                                                            <input id="checkboxCustom8" type="checkbox" name="is_half_n_half" checked={this.state.is_half_n_half} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom8">HALF n HALF</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-sm-6 col-6" >
                                                        <div>
                                                            <input id="checkboxCustom9" type="checkbox" name="is_full_pizza" checked={this.state.is_full_pizza} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom9">Full Pizza</label>
                                                        </div>
                                                    </div>
                                                    {/* <div className="col-lg-3 col-sm-6 col-6" >
                                                        <div>
                                                            <input id="checkboxCustom10" type="checkbox" name="is_suggestive" checked={this.state.is_suggestive} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom19">Suggestive Item</label>
                                                        </div>
                                                    </div> */}
                                                    {/* <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">ERP Id</label>
                                                            <input id="erpid" type="text" name="erpid" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div> */}
                                                    {/* <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">POS Code <span className="text-danger">*</span></label>
                                                            <input id="poscode" type="text" name="poscode" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div> */}
                                                </div>
                                                <div className="row">
                                                    {/* <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Description</label>
                                                            <input id="itemdesc" type="text" name="itemdesc" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div> */}
                                                    <div className="col-lg-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Groups <span className="text-danger">*</span></label>
                                                            <select name="group" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleItemsByGroupId}>
                                                                <option>Select Group</option>
                                                                {groups &&
                                                                    groups.map((group, index) => (
                                                                        group &&
                                                                        <option key={index} value={group.group_id}>{group.group_name + "-" + group.menu_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">SubGroups <span className="text-danger">*</span></label>
                                                            <select name="subgroup" className="form-control text-capitalize mt-2" required data-msg="Please select SubGroup" onChange={this.handleInputChange}>
                                                                <option value="">Select SubGroup</option>
                                                                {subGroups &&
                                                                    subGroups.map((group, index) => (
                                                                        group &&
                                                                        <option key={index} value={group.id}>{group.sub_group_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Recipe</label>
                                                            <input id="recipe" type="text" name="recipe" required data-msg="Please enter recipe" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Meta title <span className="text-danger">*</span></label>
                                                            <input id="metaTitle" type="text" name="metaTitle" required className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Meta description <span className="text-danger">*</span></label>
                                                            <input id="metaDesc" type="text" name="metaDesc" required className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                {/* <div className="row">
                                                    <div className="col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label mb-3">Item Image <span className="text-danger">*</span></label>
                                                            <input id="files" type="file" name="files" className="form-control-file" onChange={this.fileSelectedHandler} multiple />
                                                            <small className="form-text">You can also choose multiple images.</small>
                                                        </div>
                                                    </div>
                                                </div> */}
                                                <div className="row">
                                                    <div className="col-12" >
                                                        <div className="form-group">
                                                            <label className="form-control-label">Ordering Mode <span className="text-danger">*</span></label>
                                                            <Select
                                                                isMulti
                                                                name="mode"
                                                                options={this.props.orderingModes}
                                                                className="text-capitalize basic-multi-select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handleOrderModesInputChange(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Variants</label>
                                                            {sizejson.map((x: any, ind: any) => {
                                                                return (
                                                                    <div key={ind}>
                                                                        <div className="row">
                                                                            <div className="col-lg-4 col-sm-4 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">POS Code</label>
                                                                                    <input type="text" name="pos_code" value={x.pos_code} className="input-material" onChange={e => this.handleVariantInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-4 col-sm-4 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">ERP Id</label>
                                                                                    <input type="text" name="erp_id" value={x.erp_id} className="input-material" onChange={e => this.handleVariantInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-4 col-sm-4 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Nutritional Info</label>
                                                                                    <input id="nutritional_info" type="number" onWheel={(e:any) => e.target.blur()} name="nutritional_info" value={x.nutritional_info} className="input-material" onChange={(e) => this.handleVariantInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-4 col-sm-4 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Serving</label>
                                                                                    <input type="text" name="serving" value={x.serving} className="input-material" onChange={e => this.handleVariantInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div className='row'>
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Size <span className="text-danger">*</span></label>
                                                                                    <input
                                                                                        name="size"
                                                                                        type="text"
                                                                                        value={x.size}
                                                                                        data-msg="Please enter size"
                                                                                        className="input-material"
                                                                                        onChange={e => this.handleVariantInputChange(e, ind)}
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Cost <span className="text-danger">*</span></label>
                                                                                    <input
                                                                                        name="cost"
                                                                                        type="number" onWheel={(e:any) => e.target.blur()}
                                                                                        value={x.cost}
                                                                                        data-msg="Please enter cost"
                                                                                        className="input-material"
                                                                                        onChange={e => this.handleVariantInputChange(e, ind)}
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Description</label>
                                                                                    <input id="description" type="text" name="description" value={x.description} className="input-material" onChange={(e) => this.handleVariantInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Variant  <span className="text-danger">*</span></label>
                                                                                    <select onChange={(e: any) => this.handleVariantInputChange(e, ind)} name="variant_id" className="form-control text-capitalize mt-2" >
                                                                                        <option value="Select variant" selected disabled>Select variant</option>
                                                                                        {variants &&
                                                                                            variants.map((variant, index) => (
                                                                                                <option value={variant.value}>{variant.label}</option>
                                                                                            ))
                                                                                        }
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                            {/* <div className="col-lg-12" >
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Pricing By Ordering Modes <span className="text-danger">*</span></label>
                                                                                    <Select
                                                                                        isMulti
                                                                                        name="mode"
                                                                                        options={this.state.mode}
                                                                                        value={x.order_modes_price}
                                                                                        className="text-capitalize basic-multi-select mt-2"
                                                                                        classNamePrefix="select"
                                                                                        onChange={(e, i) => this.handlePricingOrderModesInputChange(e, i, ind)}
                                                                                    />
                                                                                </div>
                                                                            </div>
 */}

                                                                            {x.order_modes_price.length > 0 && x.order_modes_price.map((channel: any, index: any) => (
                                                                                <>
                                                                                    <div className="col-12 mb-2">
                                                                                        <strong>{index + 1} -</strong>
                                                                                        <strong style={{ fontSize: '16px' }} className="ml-2">
                                                                                            {channel.label}
                                                                                        </strong>
                                                                                    </div>
                                                                                    <div className="col-6">
                                                                                        <div className="form-group">
                                                                                            <label className="form-control-label mb-3">Max Retail Price <span className="text-danger">*</span></label>
                                                                                            <input
                                                                                                name="mrp"
                                                                                                type="number" onWheel={(e:any) => e.target.blur()}
                                                                                                value={channel.mrp}
                                                                                                data-msg="Please enter mrp"
                                                                                                className="input-material"
                                                                                                onChange={e => this.handleVariantPriceInputChange(e, index, ind)}
                                                                                            />
                                                                                        </div>
                                                                                    </div>
                                                                                    <div className="col-6">
                                                                                        <div className="form-group">
                                                                                            <label className="form-control-label mb-3">Extra Price</label>
                                                                                            <input
                                                                                                name="extra_price"
                                                                                                type="number" onWheel={(e:any) => e.target.blur()}
                                                                                                value={channel.extra_price}
                                                                                                className="input-material"
                                                                                                onChange={e => this.handleVariantPriceInputChange(e, index, ind)}
                                                                                            />
                                                                                        </div>
                                                                                    </div>
                                                                                </>
                                                                            ))}
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Item Image <span className="text-danger">*</span></label>
                                                                                    <input id="files" type="file" name="files" className="form-control-file" onChange={(e) => this.itemFileSelectedHandler(e, ind)} />
                                                                                    <p style={{ color: "red", fontSize: "12px" }}>Item image size should be less than 150 kb</p>
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Thumbnail Image <span className="text-danger">*</span></label>
                                                                                    <input id="files" type="file" name="files" className="form-control-file" onChange={(e) => this.thumbnailFileSelectedHandler(e, ind)} />
                                                                                    <p style={{ color: "red", fontSize: "12px" }}>Thumbnail image size should be less than 50 kb</p>
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label mb-3">Alt Tag</label>
                                                                                    <input id="alt_text" type="text" name="alt_text" className="input-material" onChange={(e) => this.handleVariantInputChange(e, ind)} />
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-lg-3 col-sm-3 col-6 d-flex align-items-center">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label"> </label>
                                                                                    {sizejson.length !== 1 &&
                                                                                        <button className="btn btn-sm btn-outline-danger" onClick={() => this.handleRemoveClick(ind)}><i className="fa fa-trash"></i></button>}
                                                                                    {sizejson.length - 1 === ind && <button className="btn btn-sm btn-primary ml-2 float-right" onClick={() => this.handleAddClick(ind)}><i className="fa fa-plus"></i></button>}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom1" type="radio" name="settime" value="alltime" checked={settime === 'alltime'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom1">Available for all time</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom2" type="radio" name="settime" value="customtime" checked={settime === 'customtime'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom2">Available for specific time</label>
                                                        </div>
                                                    </div>
                                                    {/* <div className="col-lg-2 col-sm-6 col-6 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom1" type="checkbox" name="taxstatus" checked={hero_item} onChange={this.handleHeroItem} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom1">Hero Item</label>
                                                        </div>
                                                    </div> */}
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom1" type="checkbox" name="specific_days"
                                                                checked={specific_days} onChange={this.handleSpecificDaysCheck}
                                                                className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom1">Available for specific days</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom2" type="checkbox" checked={this.state.is_featured} onChange={this.handleFeatured} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom2">Featured</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom2" type="checkbox" checked={this.state.is_foodpanda} onChange={this.handleFoodPanda} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom2">IS FOOD PANDA</label>
                                                        </div>
                                                    </div>
                                                    {
                                                        !(this.state.is_half_n_half || this.state.is_full_pizza) ?
                                                            <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                                <div>
                                                                    <input name="cross_sell" id="checkboxCrossSell" type="checkbox" checked={this.state.cross_sell} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                                    <label htmlFor="checkboxCrossSell">Cross Sell</label>
                                                                </div>
                                                            </div>
                                                        :
                                                        null
                                                    }
                                                </div>
                                                <div className='row'>
                                                    <div className="col-lg-2 col-sm-4 col-4 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom3" type="checkbox" name="is_new" checked={this.state.is_new} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom3">New</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4 col-4 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom4" type="checkbox" name="is_veg" checked={this.state.is_veg} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom4">Vegetarian</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4 col-4 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom5" type="checkbox" name="is_hot" checked={this.state.is_hot} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom5">Hot</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4 col-4 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom6" type="checkbox" name="is_best" checked={this.state.is_best} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom6">Best Seller</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Ordering Channel <span className="text-danger">*</span></label>
                                                            <select name="channel" className="form-control text-capitalize mt-2" required data-msg="Please select Channel" onChange={this.handleInputChange}>
                                                                <option value="">Select Channel</option>
                                                                {channels &&
                                                                    channels.map((channel, index) => (
                                                                        <option key={index} value={channel.sales_channel_id}>{channel.channel_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                    {/* <div className="col-lg-2 col-sm-4 col-4 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom6" type="checkbox" name="is_vegan" checked={this.state.is_vegan} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom6">Vegan</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4 col-4 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom7" type="checkbox" name="is_glutenFree" checked={this.state.is_glutenFree} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom7">Gluten Free</label>
                                                        </div>
                                                    </div> */}
                                                </div>
                                                {hero_item &&
                                                    <div className='row'>
                                                        <div className="col-6 mt-2">
                                                            <div className="form-group">
                                                                <label className="form-control-label mb-3">Hero Item Image(Desktop) <span className="text-danger">*</span></label>
                                                                <input id="files" type="file" name="files" accept="image/*" className="form-control-file" onChange={this.heroItemImageSelectedHandler} />
                                                                <p style={{ color: "red", fontSize: "12px" }}>Image size should be less than 1 mb</p>
                                                            </div>
                                                        </div>
                                                        <div className="col-6 mt-2">
                                                            <div className="form-group">
                                                                <label className="form-control-label mb-3">Hero Item Image(Mobile)</label>
                                                                <input id="files" type="file" name="files" accept="image/*" className="form-control-file" onChange={this.heroItemMobileImageSelectedHandler} />
                                                                <p style={{ color: "red", fontSize: "12px" }}>Image size should be less than 1 mb</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                {
                                                    settime === 'customtime' &&
                                                    <div>
                                                        <div className="row mt-3">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-lable">Menu Item Timing</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <input id="itemstart" type="date" name="itemstart" required data-msg="Please enter starting time" className="input-material" onChange={this.handleInputChange} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <input id="itemclose" type="date" name="itemclose" required data-msg="Please enter closing time" className="input-material" onChange={this.handleInputChange} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                <div className="row">
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom3" type="radio" name="is_lsm" value="0" checked={is_lsm == '0'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom3">Available for all stores</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom4" type="radio" name="is_lsm" value="1" checked={is_lsm == '1'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom4">Available for specific stores</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom5" type="radio" name="is_lsm" value="2" checked={is_lsm == '2'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom5">Available for stores except</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Priority</label>
                                                            <input id="priority" type="text" name="priority" required data-msg="Please enter Priority" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    {
                                                        is_lsm == '1' &&
                                                        <div className="col" >
                                                            <div className="form-group">
                                                                <label className="form-control-label">Select Store <span className="text-danger">*</span></label>
                                                                <Select
                                                                    isMulti
                                                                    name="stores"
                                                                    options={this.props.stores}
                                                                    className="text-capitalize basic-multi-select mt-2"
                                                                    classNamePrefix="select"
                                                                    onChange={(e, i) => this.handleStoresInputChange(e, i)}
                                                                />
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <div className="row">
                                                    {
                                                        is_lsm == '2' &&
                                                        <div className="col" >
                                                            <div className="form-group">
                                                                <label className="form-control-label">Select Store <span className="text-danger">*</span></label>
                                                                <Select
                                                                    isMulti
                                                                    name="stores"
                                                                    options={this.props.stores}
                                                                    className="text-capitalize basic-multi-select mt-2"
                                                                    classNamePrefix="select"
                                                                    onChange={(e, i) => this.handleStoresInputChange(e, i)}
                                                                />
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                {specific_days &&
                                                    <div className='row'>
                                                        <div className="col">
                                                            <div className="form-group">
                                                                {
                                                                    daysTiming.map((x: any, i: any) => {
                                                                        let timePeriod = x.timePeriods;
                                                                        return (
                                                                            <div key={i} className='mb-2' style={{ borderBottom: "1px dotted grey" }}>
                                                                                <div className="row">
                                                                                    <div className="col-lg-8 col-md-8 col-8">
                                                                                        <div className="form-group">
                                                                                            <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                                            <Select
                                                                                                options={this.weeklyDays.filter(o1 => !daysTiming.some((o2: any) => o1.value === o2.day))}
                                                                                                className="text-capitalize basic-multi-select"
                                                                                                classNamePrefix="select"
                                                                                                value={{ value: x.value, label: x.label }}
                                                                                                onChange={(e) => this.handleWeeklyDaysInputChange(e, i)}
                                                                                            />
                                                                                        </div>
                                                                                    </div>
                                                                                    <div className="col-lg-4 col-md-4 col-4 mt-4 py-2">
                                                                                        {daysTiming.length !== 1 &&
                                                                                            <button className="btn btn-sm btn-outline-danger"
                                                                                                onClick={() => this.handleWeeklyDaysRemoveClick(i)}><i className="fa fa-trash"></i></button>}
                                                                                        {(daysTiming.length - 1 === i && daysTiming.length < 7) && <button className="btn btn-sm btn-primary ml-2" onClick={(e: any) => this.handleweeklyDaysAddClick(e, i)}><i className="fa fa-plus"></i></button>}
                                                                                    </div>
                                                                                </div>
                                                                                {timePeriod.map((t: any, pindex: any) => {
                                                                                    return (
                                                                                        <div className="row mb-3">
                                                                                            <div className="col-lg-3 col-md-3">
                                                                                            </div>
                                                                                            <div className="col-lg-3 col-md-3 col-6">
                                                                                                <div className="form-group">
                                                                                                    <label className="form-control-label mb-0">Open Time <span className="text-danger">*</span></label>
                                                                                                    <input id="open" type="time" name="open" value={t.open} className="input-material" onChange={(e) => this.handleWeeklyDaysInputChange(e, i, pindex)} />
                                                                                                </div>
                                                                                            </div>
                                                                                            <div className="col-lg-3 col-md-3 col-6">
                                                                                                <div className="form-group">
                                                                                                    <label className="form-control-label mb-0">Close Time <small className="text-danger">*</small></label>
                                                                                                    <input id="close" type="time" name="close" value={t.close} className="input-material" onChange={(e) => this.handleWeeklyDaysInputChange(e, i, pindex)} />
                                                                                                    {(timePeriod.length - 1 == pindex) && <small className="text-primary" style={{ cursor: 'pointer' }} onClick={(e: any) => this.handleweeklyDaysAddClick(e, i, pindex)}>+ Add hours</small>}
                                                                                                </div>
                                                                                            </div>
                                                                                            <div className="col-lg-3 col-md-3 col-12">
                                                                                                {timePeriod.length !== 1 &&
                                                                                                    <button className="btn btn-sm btn-outline-danger"
                                                                                                        onClick={() => this.handleWeeklyDaysRemoveClick(i, pindex)}><i className="fa fa-trash"></i></button>}
                                                                                            </div>
                                                                                        </div>
                                                                                    )
                                                                                })}
                                                                            </div>
                                                                        );
                                                                    })
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                }

                                                <div className="form-group d-flex justify-content-end mt-4">
                                                    {(this.props.itemLoad) ?
                                                        <button className='btn btn-primary d-flex justify-content-end align-item-center' disabled={this.props.itemLoad}><Loader type="TailSpin" color="white" height={30} width={30} /></button> :
                                                        <button className='btn btn-primary' disabled={!this.isMenuItemReady() || !this.state.flag} onClick={this.handleSaveBtnClick}>Save</button>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div>
        );
    }
}
const mapStateToProps = (state: any) => {
    return {
        groups: state.menu.groups,
        message: state.menu.message,
        states: state.setting.states,
        menus: state.menu.menus,
        stores: state.menu.storesoptions,
        channels: state.store.channels,
        duplicateItemPos: state.menu.duplicateItemPos,
        duplicateItemErp: state.menu.duplicateItemErp,
        orderingModes: state.menu.orderModes,
        variants: state.menu.variants,
        subGroups: state.menu.subGroups,
        itemLoad : state.menu.itemLoad
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        addItems: function (formData: any) {
            dispatch(addItems(formData));
        },
        checkDuplicatePosCode: (pos_code: any) => {
            dispatch(checkDuplicatePosCode(pos_code))
        },
        checkDuplicateErpId: (erp_id: any) => {
            dispatch(checkDuplicateErpId(erp_id))
        },
        groupsList: function () {
            dispatch(groupsList())
        },
        menusList: function () {
            dispatch(menusList())
        },
        variantsList: function () {
            dispatch(variantsList())
        },
        statesList: function () {
            dispatch(statesList())
        },
        storesList: () => {
            dispatch(storesListForMultiSelect())
        },
        OrderingModes: () => {
            dispatch(OrderingModes())
        },
        subGroupListByGroupId: function (id: any) {
            dispatch(subGroupListByGroupId(id))
        },
        channelList: function () {
            dispatch(channelList())
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(AddMenuItem);