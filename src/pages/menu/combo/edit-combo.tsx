import React, { Component } from 'react';
import { EditComboProps, EditComboState } from '../../../interfaces/menu'
import { connect } from 'react-redux'
import { editCombo, getCombo, menusList, groupedItemsList, channelList, getTax<PERSON><PERSON><PERSON>, logoutUser, statesList, groupsList, storesListForMultiSelect } from '../../../redux'
import Footer from '../../../components/footer/main';
import jwt from 'jsonwebtoken'
import Sidebar from '../../../components/sidebar';
import Topbar from '../../../components/topbar';
import { Link, Redirect } from 'react-router-dom';
import Select from "react-select";
import { secretKey } from '../../../secret';
import CheckChanges from '../../../components/confirmOnLeave'
import moment from 'moment';
import { comboChoiceItemsByGroupId, modGroupsList, OrderingModes, subGroupListByGroupId, variantsList } from '../../../redux/actions/menuAction';
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Loader from "react-loader-spinner";
import "react-loader-spinner/dist/loader/css/react-spinner-loader.css";
import { IMAGE_URL } from '../../../client-config';
toast.configure();
class EditCombo extends Component<EditComboProps, EditComboState> {
    weeklyDays: { value: string; label: string; }[]
    constructor(props: any) {
        super(props);
        this.weeklyDays = [
            { value: 'MONDAY', label: 'Mon' },
            { value: 'TUESDAY', label: 'Tue' },
            { value: 'WEDNESDAY', label: 'Wed' },
            { value: 'THURSDAY', label: 'Thu' },
            { value: 'FRIDAY', label: 'Fri' },
            { value: 'SATURDAY', label: 'Sat' },
            { value: 'SUNDAY', label: 'Sun' },
        ];
        this.state = {
            menu: "",
            group: "",
            subgroup: "",
            state: "",
            flag: false,
            comboname: "",
            erpid: "",
            poscode: "",
            channel: "",
            priority: "",
            combodesc: "",
            combocost: "",
            combosale: "",
            combomrp: "",
            images: null,
            // thumbnail_images:null,
            hero_image: null,
            hero_mobile_image: null,
            taxstatus: false,
            settime: 'alltime',
            mode: [],
            combostart: "",
            comboclose: "",
            errtaxdef: false,
            tax_percent: "",
            hero_item: false,
            topDeal: false,
            is_hide: false,
            is_voucher: false,
            is_foodpanda:false,
            is_blurry: false,
            is_free_delivery: false,
            comboUnique: "",
            is_lsm: '0',
            metaTitle: "",
            metaDesc: "",
            is_featured: false,
            altTag: "",
            storesJson: [],
            is_suggestive: false,
            specific_days: false,
            daysTiming: [
                {
                    day: "",
                    label: "",
                    timePeriods: [{
                        open: "",
                        close: ""
                    }]
                }
            ],
            comboChoices: [
                {
                    mod_groups: [],
                    crusts: [],
                    flavours: [],
                    topings: [],
                    condiments: [],
                    group_name: "",
                    priority: "",
                    size_pos_code: null,
                    recipe: "",
                    variant_id: "",
                    is_half_n_half: false,
                    if_full_pizza: false,
                }
            ],
            order_modes_price: []
        }
        this.handleSaveBtnClick = this.handleSaveBtnClick.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
        this.handleItemsByMenuId = this.handleItemsByMenuId.bind(this);
    }
    componentDidMount() {
        let id = this.props.match.params.id;
        this.props.menusList();
        this.props.groupsList();
        this.props.variantsList();
        this.props.getCombo(id);
        this.props.channelList();
        this.props.storesList();
        this.props.OrderingModes();
        this.props.modGroupsList();
        document.title = "DominosCMS | Combos"
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        // let { menu } = this.state;
        if (event.target.value == ' ') {
            event.target.value = event.target.value.replace(/\s/g, "");
        }
        if (event.target.name == 'group') {
            this.setState({
                group: event.target.value,
                subgroup: ""
            });
            this.props.subGroupListByGroupId(event.target.value)
        } else {
            this.setState({
                [event.target.name]: event.target.value
            });
        }
    }
    handleVariantPriceInputChange = (e: any, index: any) => {
        const { name, value } = e.target;
        const list: any = this.state.order_modes_price;
        list[index][name] = value;
        this.setState({ order_modes_price: list });
    };
    handleItemsByMenuId(event: { target: { name: any; value: any; }; }) {
        let select: any = document.getElementById('menu');
        if (this.state.menu !== "") {
            if (window.confirm("You cannot change the menu. If you clicks OK, then menu items corresponding to present combo will lost.")) {
                this.setState({
                    menu: event.target.value,
                    combocost: "",
                    combomrp: "",
                })
                this.props.groupedItemsList(event.target.value);
            } else {
                select.value = this.state.menu
            }
        } else {
            this.setState({
                "menu": event.target.value,
            });
            this.props.groupedItemsList(event.target.value);
        }
    }
    UNSAFE_componentWillReceiveProps(nextProps: any) {
        if (this.props.comboData.menu_id !== nextProps.comboData.menu_id) {  //this condition is used
            //to call statements only first time
            this.setState({ group: nextProps.comboData.group_id && nextProps.comboData.group_id.group_id, subgroup: nextProps.comboData.subgroup_id && nextProps.comboData.subgroup_id.id })
            this.props.groupedItemsList(nextProps.comboData.menu_id.menu_id);
        }
        let id = this.props.match.params.id;
        let orderModePrice: any[] = []
        this.setState({
            comboname: nextProps.comboData.combo_name,
            combodesc: nextProps.comboData.combo_description,
            menu: nextProps.comboData.menu_id && nextProps.comboData.menu_id.menu_id,
            combocost: nextProps.comboData.combo_cost_price,
            is_suggestive: nextProps.comboData.is_suggestive_deal,
            // comboUnique: nextProps.comboData.comboUnique,
            // combomrp: nextProps.comboData.combo_mrp_price,
            comboChoices: nextProps.comboChoices,
            erpid: nextProps.comboData.erp_id,
            flag: ((nextProps.comboData.images && nextProps.comboData.images.length > 0) && (nextProps.comboData.images[0].image_url)) ? true : false,
            poscode: nextProps.comboData.pos_code,
            priority: nextProps.comboData.priority,
            channel: nextProps.comboData.combo_channel_id && nextProps.comboData.combo_channel_id.sales_channel_id,
            hero_item: nextProps.comboData.hero_item == 1 ? true : false,
            topDeal: nextProps.comboData.topDeal == 1 ? true : false,
            is_hide: nextProps.comboData.is_hide == 1 ? true : false,
            is_voucher: nextProps.comboData.is_voucher == 1 ? true : false,
            is_foodpanda: nextProps.comboData.is_foodpanda == 1 ? true : false,
            is_free_delivery: nextProps.comboData.is_free_delivery == 1 ? true : false,
            is_blurry: nextProps.comboData.is_blurry == 1 ? true : false,
            is_featured: nextProps.comboData.is_featured == 1 ? true : false,
            specific_days: nextProps.comboData.serving_hours ? true : false,
            daysTiming: nextProps.comboData.serving_hours ? JSON.parse(nextProps.comboData.serving_hours) : this.state.daysTiming,
            storesJson: nextProps.comboData.stores_json ? JSON.parse(nextProps.comboData.stores_json) : this.state.storesJson,
            is_lsm: nextProps.comboData.is_lsm,
            altTag: nextProps.comboData.alt_text ? nextProps.comboData.alt_text : "",
            metaTitle: nextProps.comboData.meta_title ? nextProps.comboData.meta_title : "",
            metaDesc: nextProps.comboData.meta_description ? nextProps.comboData.meta_description : ""
        })
        let mode: any[] = nextProps.comboData.item_mode ? JSON.parse(nextProps.comboData.item_mode) : []
        const list: any[] = this.state.comboChoices;
        let orderingModes = this.props.orderingModes;
        const results = orderingModes.filter(({ label: id1 }) => mode.some(({ label: id2 }) => id2 === id1));
        if (nextProps.comboData.order_modes_price && nextProps.comboData.order_modes_price.length > 0) {
            this.setState({ order_modes_price: nextProps.comboData.order_modes_price })
        } else {
            results.forEach(element => {
                orderModePrice.push({
                    combo_id: id,
                    order_mode_id: element.value,
                    order_mode: element.label,
                    mrp: "0",
                    label: element.label,
                    value: element.value
                })
            });
            this.setState({ order_modes_price: orderModePrice })
        }
        this.setState({ mode: results })
        if (nextProps.comboData.combo_start_time !== null) {
            this.setState({
                "settime": "customtime",
                combostart: moment(nextProps.comboData.combo_start_time).local().format('YYYY-MM-DDTHH:mm'),
                comboclose: moment(nextProps.comboData.combo_close_time).local().format('YYYY-MM-DDTHH:mm'),
            })
        } else {
            this.setState({
                "settime": "alltime",
                combostart: "",
                comboclose: ""
            })
        }
    }
    blockInvalidChar = (e: any) => {
        var regex = new RegExp("^[a-zA-Z0-9]");
        let specialkey: any = e.keyCode;
        var key: any = String.fromCharCode(!e.charCode ? e.which : e.charCode);
        if (specialkey == 8 || specialkey == 9) {
            return true;
        } else {
            if (/\S/.test(e.target.value)) {
                // string is not empty and not just whitespace
                if (!regex.test(key)) {
                    if (specialkey == 32) {
                    } else {
                        e.preventDefault()
                    }
                }
            } else {
                ((!regex.test(key) || specialkey == 32)) && e.preventDefault()
            }
        }
    }

    blockSpecialCharacters = () => {
        var checkString = this.state.comboname;
        if (checkString != "") {
            if (/[%,_]/.test(checkString)) {
                alert("Your combo name has '%' or '_' sign. \nIt is not allowed.\nPlease remove it and try again.");
                return (false);
            } else {
                return true
            }
        }
    }
    fileSelectedHandler = (e: any) => {
        if (e.target.files.length > 0) {
            var size = 153600;

            if (e.target.files[0].size > size) {

                toast.error(" Combo image size too large. Upload image less then 150kb", { position: toast.POSITION.BOTTOM_RIGHT, hideProgressBar: false, autoClose: 3000 })

                this.setState({
                    flag: false
                })
            }
            else {
                this.setState({ images: e.target.files, flag: true })
            }
        }
    }
    // thumbnailFileSelectedHandler = (e: any) => {

    //     if (e.target.files.length > 0) {
    //         var size = 51200;

    //         if (e.target.files[0].size > size) {

    //           toast.error(" Combo image size too large. Upload image less then 50kb", { position: toast.POSITION.BOTTOM_RIGHT, hideProgressBar: false, autoClose: 3000 })

    //           this.setState({
    //             flag:false
    //           })
    //         }
    //         else{
    //             this.setState({ thumbnail_images: e.target.files,flag:true })
    //         }
    //     }
    // }
    heroItemImageSelectedHandler = (e: any) => {
        if (e.target.files.length > 0) {
            this.setState({ hero_image: e.target.files })
        }
    }
    heroItemMobileImageSelectedHandler = (e: any) => {
        if (e.target.files.length > 0) {
            this.setState({ hero_mobile_image: e.target.files })
        }
    }
    handleSpecificDaysCheck = (event: { target: { name: any; value: any; }; }) => {
        const { specific_days } = this.state;
        this.setState({ specific_days: !specific_days })
    }
    handleCheckBoxes = (event: { target: { name: any; value: any; }; }) => {
        const { hero_item, is_featured, topDeal, is_hide, is_voucher, is_foodpanda, is_blurry, is_suggestive, is_free_delivery } = this.state;
        const { comboData } = this.props;
        if (event.target.name == "hero_item") {
            this.setState({ hero_item: !hero_item })
        } else if (event.target.name == "topDeal") {
            if (comboData.topDealsCount == "4") {
                if (topDeal == false) {
                    alert("You have already 4 Top Deals!")
                } else {
                    this.setState({ topDeal: !topDeal })
                }
            } else {
                this.setState({ topDeal: !topDeal })
            }
        } else if (event.target.name == "is_featured") {
            this.setState({ is_featured: !is_featured })
        } else if (event.target.name == "is_hide") {
            this.setState({ is_hide: !is_hide })
        } else if (event.target.name == "is_voucher") {
            this.setState({ is_voucher: !is_voucher })
        } else if (event.target.name == "is_blurry") {
            this.setState({ is_blurry: !is_blurry })
        } else if (event.target.name == "is_suggestive") {
            this.setState({ is_suggestive: !is_suggestive })
        }
        else if (event.target.name == "is_foodpanda") {
            this.setState({ is_foodpanda: !is_foodpanda })
        }
        else if(event.target.name == "is_free_delivery") {
            this.setState({ is_free_delivery: !is_free_delivery })
        
        }
    }
    handleChangeRad = (event: { target: { name: any; value: any; } }) => {
        this.setState({ [event.target.name]: event.target.value })
    }
    checkMRPForAllModes = () => {
        let { order_modes_price } = this.state;
        var filtered = order_modes_price && order_modes_price.filter(function (el) {
            return (el.mrp == "");
        });
        if (filtered.length > 0) {
            return false;
        } else {
            return true
        }
    }
    isComboReady = () => {
        const { comboname, menu, group, subgroup, combocost, channel, combomrp, settime, mode, combostart, comboclose, is_lsm, storesJson, metaTitle, metaDesc } = this.state
        if(metaTitle == "" || metaDesc == "") {
            return false;
        }
        if (settime == 'customtime') {
            if (is_lsm == "0") {
                return (comboname !== "" && channel !== "" && menu !== "" && group !== "" && subgroup !== "" && combocost !== "" && combostart !== "" && comboclose !== "" && mode.length > 0 && this.checkMRPForAllModes());
            } else if (is_lsm == "1") {
                return (comboname !== "" && channel !== "" && menu !== "" && group !== "" && subgroup !== "" && combocost !== "" && combostart !== "" && comboclose !== "" && storesJson.length > 0 && mode.length > 0 && this.checkMRPForAllModes());
            } else if (is_lsm == "2") {
                return (comboname !== "" && channel !== "" && menu !== "" && group !== "" && subgroup !== "" && combocost !== "" && combostart !== "" && comboclose !== "" && storesJson.length > 0 && mode.length > 0 && this.checkMRPForAllModes());
            }
        } else {
            if (is_lsm == "0") {
                return (comboname !== "" && channel !== "" && menu !== "" && group !== "" && subgroup !== "" && combocost !== "" && mode.length > 0 && this.checkMRPForAllModes());
            } else if (is_lsm == "1") {
                return (comboname !== "" && channel !== "" && menu !== "" && group !== "" && subgroup !== "" && combocost !== "" && storesJson.length > 0 && mode.length > 0 && this.checkMRPForAllModes());
            } else if (is_lsm == "2") {
                return (comboname !== "" && channel !== "" && menu !== "" && group !== "" && subgroup !== "" && combocost !== "" && storesJson.length > 0 && mode.length > 0 && this.checkMRPForAllModes());
            }
        }
    }
    handleOrderModesInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            if (e.length < this.state.mode.length) {
                let tempArr = this.state.order_modes_price;
                let selectedItems = tempArr.filter(obj => e.find((s: any) => s.value === obj.value));
                this.setState({ order_modes_price: selectedItems })
            } else {
                let id = this.props.match.params.id;
                let mode_id = e[this.state.order_modes_price.length].value;
                let mode_label = e[this.state.order_modes_price.length].label;
                let tempArr = this.state.order_modes_price;
                let obj = {
                    combo_id: id,
                    order_mode_id: mode_id,
                    order_mode: mode_label,
                    mrp: "0",
                    label: mode_label,
                    value: mode_id
                }
                tempArr.push(obj);
                this.setState({ order_modes_price: tempArr })
            }
            this.setState({ mode: e });
        } else {
            this.setState({ order_modes_price: [] });
            this.setState({ mode: [] });
        }
    };
    handleStoresInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ storesJson: e });
        } else {
            this.setState({ storesJson: [] });
        }
    };
    handlePricingOrderModesInputChange = (e: any, indexing: any) => {
        if (e && e.length > 0) {
            if (e.length === this.state.order_modes_price.length + 1) {
                if (this.state.order_modes_price.length == 0) {
                    let id = this.props.match.params.id;
                    let mode_id = e[this.state.order_modes_price.length].value;
                    let mode_label = e[this.state.order_modes_price.length].label;
                    let tempArr = this.state.order_modes_price;
                    let obj = {
                        combo_id: id,
                        order_mode_id: mode_id,
                        order_mode: mode_label,
                        label: mode_label,
                        mrp: "0",
                        value: mode_id,
                    }
                    tempArr.push(obj);
                    this.setState({ order_modes_price: tempArr })
                } else {
                    if (this.state.order_modes_price[this.state.order_modes_price.length - 1].mrp > 0) {
                        let id = this.props.match.params.id;
                        let mode_id = e[this.state.order_modes_price.length].value;
                        let mode_label = e[this.state.order_modes_price.length].label;
                        let tempArr = this.state.order_modes_price;
                        let obj = {
                            combo_id: id,
                            order_mode_id: mode_id,
                            order_mode: mode_label,
                            mrp: "0",
                            label: mode_label,
                            value: mode_id
                        }
                        tempArr.push(obj);
                        this.setState({ order_modes_price: tempArr })
                    } else {
                        toast.error("Max retail price should not be 0,Please update", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    }
                }
            }
            else {
                let tempArr = this.state.order_modes_price;
                tempArr = tempArr.filter((item: any) => (item.order_mode_id != indexing.removedValue.value))
                this.setState({ order_modes_price: tempArr })
            }
        } else {
            this.setState({ order_modes_price: [] })
        }
    };
    handleComboChoiceInputChange = (e: any, index: any, choiceModGType?: any) => {

        if (e && e.target) {
            const { name, value } = e.target;
            if (name == "group_id") {
                this.props.comboChoiceItemsByGroupId(value, index)
            }
            if (name == "is_half_n_half") {
                const list: any = this.state.comboChoices;
                list[index]["is_full_pizza"] = false;
                list[index]["is_half_n_half"] = !list[index]["is_half_n_half"];
                list[index]["size_pos_code"] = null;
                list[index]["recipe"] = "";
                list[index]["variant_id"] = "";
                list[index]["crusts"] = [];
                list[index]["flavours"] = [];
                list[index]["topings"] = [];
                list[index]["condiments"] = [];
                list[index]["mod_groups"] = [];
                this.setState({ comboChoices: list });
            } else if (name == "is_full_pizza") {
                const list: any = this.state.comboChoices;
                list[index]["is_full_pizza"] = !list[index]["is_full_pizza"];
                list[index]["is_half_n_half"] = false;
                list[index]["size_pos_code"] = null;
                list[index]["recipe"] = "";
                list[index]["variant_id"] = "";
                list[index]["crusts"] = [];
                list[index]["flavours"] = [];
                list[index]["topings"] = [];
                list[index]["condiments"] = [];
                list[index]["mod_groups"] = [];
                this.setState({ comboChoices: list });
            } else {
                const list: any = this.state.comboChoices;
                list[index][name] = value;
                this.setState({ comboChoices: list });
            }
        } else {
            if (e && Array.isArray(e) && e.length > 0) {
                const list: any = this.state.comboChoices;
                if (list[index]["is_half_n_half"]) {
                    if (choiceModGType == "crusts") {
                        list[index]["crusts"] = e;
                    } else if (choiceModGType == "flavours") {
                        list[index]["flavours"] = e;
                    } else if (choiceModGType == "topings") {
                        list[index]["topings"] = e;
                    } else if (choiceModGType == "condiments") {
                        list[index]["condiments"] = e;
                    }
                } else if (list[index]["is_full_pizza"]) {
                    if (choiceModGType == "crusts") {
                        list[index]["crusts"] = e;
                    } else if (choiceModGType == "topings") {
                        list[index]["topings"] = e;
                    } else if (choiceModGType == "condiments") {
                        list[index]["condiments"] = e;
                    }
                } else {
                    list[index]["mod_groups"] = e;
                }
                this.setState({ comboChoices: list });
            } else {
                if (e && e.value) {
                    const list: any = this.state.comboChoices;
                    list[index]["menu_item_id"] = e.value;
                    list[index]["itemsize"] = JSON.parse(e.item_size);
                    list[index]["size"] = "";
                    this.setState({ comboChoices: list });
                } else {
                    const list: any = this.state.comboChoices;
                    if (choiceModGType == "crusts") {
                        list[index]["crusts"] = [];
                    } else if (choiceModGType == "flavours") {
                        list[index]["flavours"] = [];
                    } else if (choiceModGType == "topings") {
                        list[index]["topings"] = [];
                    } else if (choiceModGType == "condiments") {
                        list[index]["condiments"] = [];
                    } else if (choiceModGType == "modGroups") {
                        list[index]["mod_groups"] = [];
                    }
                }

            }
        }
    };
    handleweeklyDaysAddClick = (e: any, i: any, pindex?: any) => {
        const list: any = this.state.daysTiming;
        if (pindex >= 0) {
            if (list[i].timePeriods[list[i].timePeriods.length - 1].open == "" || list[i].timePeriods[list[i].timePeriods.length - 1].close == "") {
                alert("Please fill in selected day hours")
            } else {
                let timePeriods = {
                    open: "",
                    close: ""
                }
                list[i].timePeriods.push(timePeriods)
                this.setState({ daysTiming: list });
            }
        } else {
            if (list[i]["day"] == "" || list[i].timePeriods[list[i].timePeriods.length - 1].open == "" || list[i].timePeriods[list[i].timePeriods.length - 1].close == "") {
                alert("Please fill in selected day timing")
            } else {
                let timePeriods = {
                    open: "",
                    close: ""
                }
                const newlist: any = {
                    day: "",
                    timePeriods: []
                }
                newlist.timePeriods.push(timePeriods)
                const days: any = this.state.daysTiming.concat(newlist)
                this.setState({ daysTiming: days });
            }
        }
        e.preventDefault()
    };
    handleAddComboChoice = (i: any) => {
        const list: any = this.state.comboChoices;
        if (list[i]["is_half_n_half"]) {
            if (list[i]["group_name"] == "" || list[i]["crusts"].length == 0 || list[i]["flavours"].length == 0 || list[i]["topings"].length == 0) {
                alert("Please fill in current item")
            } else {
                const newlist: any = {
                    mod_groups: [],
                    crusts: [],
                    flavours: [],
                    topings: [],
                    condiments: [],
                    group_name: "",
                    variant_id: "",
                    priority: "",
                    size_pos_code: null,
                    recipe: "",
                    is_half_n_half: false,
                    is_full_pizza: false,
                }
                const comboChoice: any = this.state.comboChoices.concat(newlist)
                this.setState({ comboChoices: comboChoice });
            }
        } else if (list[i]["is_full_pizza"]) {
            if (list[i]["group_name"] == "" || list[i]["crusts"].length == 0 || list[i]["topings"].length == 0) {
                alert("Please fill in current item")
            } else {
                const newlist: any = {
                    mod_groups: [],
                    crusts: [],
                    flavours: [],
                    topings: [],
                    condiments: [],
                    group_name: "",
                    variant_id: "",
                    priority: "",
                    size_pos_code: null,
                    recipe: "",
                    is_half_n_half: false,
                    is_full_pizza: false,
                }
                const comboChoice: any = this.state.comboChoices.concat(newlist)
                this.setState({ comboChoices: comboChoice });
            }
        } else {
            if (list[i]["group_name"] == "" || list[i]["mod_groups"].length == 0) {
                alert("Please fill in current item")
            } else {
                const newlist: any = {
                    mod_groups: [],
                    crusts: [],
                    flavours: [],
                    topings: [],
                    condiments: [],
                    group_name: "",
                    variant_id: "",
                    priority: "",
                    size_pos_code: null,
                    recipe: "",
                    is_half_n_half: false,
                    is_full_pizza: false,
                }
                const comboChoice: any = this.state.comboChoices.concat(newlist)
                this.setState({ comboChoices: comboChoice });
            }
        }
    };
    handleRemoveComboChoice = (index: any) => {
        const list = this.state.comboChoices;
        list.splice(index, 1);
        this.setState({ comboChoices: list });
    };
    handleWeeklyDaysInputChange = (e: any, index: any, pindex?: any) => {
        if (e.target) {
            const { name, value } = e.target;
            const list: any = this.state.daysTiming;
            let timeP = list[index].timePeriods;
            timeP[pindex][name] = value;
            list[index].timePeriods = timeP;
            this.setState({ daysTiming: list });
        } else {
            console.log("e", e)
            const list: any = this.state.daysTiming;
            list[index]["day"] = e.value;
            list[index]["label"] = e.label;
            this.setState({ daysTiming: list });
        }
    };
    handleWeeklyDaysRemoveClick = (index: any, pindex?: any) => {
        const list = this.state.daysTiming;
        if (pindex >= 0) {
            let timePer = list[index].timePeriods;
            timePer.splice(pindex, 1);
            list[index].timePeriods = timePer;
            this.setState({ daysTiming: list });
        } else {
            list.splice(index, 1);
            this.setState({ daysTiming: list });
        }
    };
    handleSaveBtnClick = (event: any) => {
        let { comboname, erpid, poscode, is_featured, channel, priority, specific_days, daysTiming, is_hide, is_voucher,is_foodpanda, is_blurry, is_free_delivery, is_suggestive, mode, combodesc, combocost, group, subgroup, combomrp, hero_item, topDeal, comboChoices, is_lsm, storesJson, settime, combostart, comboclose, menu, metaTitle, metaDesc, altTag } = this.state;
        let id = this.props.match.params.id;
        const Days = JSON.stringify(daysTiming);
        const data: any = new FormData()
        let comboNamevalid = this.blockSpecialCharacters();
        if (comboNamevalid) {
            if (this.state.images) {
                for (var x = 0; x < this.state.images.length; x++) {
                    data.append('files', this.state.images[x])
                }
            }
            // if (this.state.thumbnail_images) {
            //     for (var x = 0; x < this.state.thumbnail_images.length; x++) {
            //         var file = this.state.thumbnail_images[0];
            //         var newFileName = file.name.split(".")[0] + `_thumbnail_${x}.` + file.name.split(".")[1];

            //         data.append('files', file, newFileName)

            //     }
            // }
            data.append('combo_name', comboname)
            if (erpid !== null && erpid !== "") {
                data.append('erp_id', erpid)
            } else {
                data.append('erp_id', null)
            }
            if (poscode !== null && poscode !== "") {
                data.append('pos_code', poscode)
            } else {
                data.append('pos_code', null)
            }
            data.append("hero_item", hero_item ? 1 : 0)
            data.append("topDeal", topDeal ? 1 : 0)
            data.append("is_hide", is_hide ? 1 : 0)
            data.append("is_voucher", is_voucher ? 1 : 0)
            data.append("is_foodpanda", is_foodpanda ? 1 : 0)
            data.append("is_blurry", is_blurry ? 1 : 0)
            data.append("is_free_delivery", is_free_delivery ? 1 : 0)
            data.append("is_suggestive_deal", is_suggestive ? 1 : 0)
            if (is_lsm == '0') {
                data.append('is_lsm', 0)
            } else if (is_lsm == '1') {
                data.append('is_lsm', 1)
                data.append('stores_json', JSON.stringify(storesJson))
            }
            else if (is_lsm == '2') {
                data.append('is_lsm', 2)
                data.append('stores_json', JSON.stringify(storesJson))
            }
            data.append('combo_description', combodesc)
            data.append('combo_channel_id', channel)
            data.append('combo_cost_price', combocost)
            // data.append('combo_sales_price', combosale)
            // data.append('combo_mrp_price', combomrp)
            // data.append('comboUnique', comboUnique)
            data.append('comboModePrice', JSON.stringify(this.state.order_modes_price))
            data.append('item_mode', JSON.stringify(mode))
            data.append('priority', priority)
            // data.append('combo_tax_configuration', taxstatus == false ? '0' : '1')
            data.append('menu_id', menu)
            data.append('group_id', group)
            data.append('subgroup_id', subgroup)
            data.append('meta_title', metaTitle)
            data.append("is_featured", is_featured ? 1 : 0)
            data.append('meta_description', metaDesc)
            data.append('alt_text', altTag)
            if (settime === 'customtime') {
                data.append('combo_start_time', moment(combostart).utc(false))
                data.append('combo_close_time', moment(comboclose).utc(false))
            } else if (settime === 'alltime') {
                data.append('combo_start_time', null)
                data.append('combo_close_time', null)
            }
            // if (!combojson[1]) {
            //     alert("Please select at least 2 items")
            // } else 
            data.append('combo_choices', JSON.stringify(comboChoices))
            if (specific_days) {
                if (daysTiming[daysTiming.length - 1]["day"] !== "" && daysTiming[daysTiming.length - 1].timePeriods[daysTiming[daysTiming.length - 1].timePeriods.length - 1].open !== "" && daysTiming[daysTiming.length - 1].timePeriods[daysTiming[daysTiming.length - 1].timePeriods.length - 1].close !== "") {
                    data.append('serving_hours', Days)
                    this.props.editCombo(id, data);
                } else {
                    alert("Please fill in selected day timing")
                }
            } else {
                this.props.editCombo(id, data);
            }
        }
    }
    IsJsonString = (str: any) => {
        try {
            JSON.parse(str);
        } catch (e) {
            return false;
        }
        return true;
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const { menus, groups, subGroups, channels, combooptions, comboData, addons, crusts, topings, flavours, condiments, variants } = this.props;
        const { settime, priority, combomrp, specific_days, combostart, daysTiming, topDeal, hero_item, mode, comboclose, combocost, comboChoices, combodesc, is_lsm, storesJson, subgroup, is_featured, is_hide, is_voucher,is_foodpanda,is_blurry, is_free_delivery } = this.state;
        const groupedOptions: any = [];
        let obj: any = ""
        for (const property in combooptions) {
            obj = {
                label: property,
                options: combooptions[property]
            }
            groupedOptions.push(obj)
        }
        let groupsByMenuID: any[] = groups.filter((obj: any) => {
            return obj.menu_id == this.state.menu;
        })
        const formatGroupLabel = (data: any) => (
            <div className="d-flex justify-content-between">
                <span>{data.label}</span>
                <span className="badge badge-secondary p-1">{data.options.length}</span>
            </div>
        );
        return (
            <div className="page">
                <CheckChanges path="/edit-combo" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Combo Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/combos" className="text-primary">Combos</Link></li>
                                <li className="breadcrumb-item active">Edit Combo</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col-lg-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Combo Name <span className="text-danger">*</span></label>
                                                            <input id="comboname" type="text" name="comboname" defaultValue={comboData.combo_name} required data-msg="Please enter Combo Name" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">ERP ID</label>
                                                            <input id="erpid" type="text" name="erpid" defaultValue={comboData.erp_id} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">POS Code</label>
                                                            <input id="poscode" type="text" name="poscode" defaultValue={comboData.pos_code} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Meta title <span className="text-danger">*</span></label>
                                                            <input id="metaTitle" type="text" name="metaTitle" value={this.state.metaTitle} required data-msg="Please enter Item Type" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Meta description <span className="text-danger">*</span></label>
                                                            <input id="metaDesc" type="text" name="metaDesc" value={this.state.metaDesc} required data-msg="Please enter Item Type" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-4" >
                                                        <div>
                                                            <input id="checkboxCustom6" type="checkbox" name="is_suggestive" checked={this.state.is_suggestive} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom6">Suggestive Deal</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    {/* <div className="col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Combo Unique Id <span className="text-danger">*</span></label>
                                                            <input id="comboUnique" type="text" name="comboUnique" defaultValue={comboData.comboUnique} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div> */}
                                                </div>
                                                <div className='row'>
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Menu <span className="text-danger">*</span></label>
                                                            <select id="menu" name="menu" className="form-control text-capitalize mt-2" required data-msg="Please select Menu" onChange={this.handleItemsByMenuId}>
                                                                <option value="">Select Menu</option>
                                                                {menus &&
                                                                    menus.map((menu, index) => (
                                                                        <option key={index} value={menu.menu_id} {...(comboData.menu_id && comboData.menu_id.menu_id === menu.menu_id) && { selected: true }}>{menu.brand_name} - {menu.store_type_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Group name <span className="text-danger">*</span></label>
                                                            <select name="group" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleInputChange}>
                                                                <option value="">Select Group</option>
                                                                {groupsByMenuID &&
                                                                    groupsByMenuID.map((group, index) => (
                                                                        group &&
                                                                        <option key={index} value={group.group_id} {...(comboData.group_id && comboData.group_id.group_id === group.group_id) && { selected: true }}>{group.group_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">SubGroups <span className="text-danger">*</span></label>
                                                            <select name="subgroup" className="form-control text-capitalize mt-2" required data-msg="Please select SubGroup" onChange={this.handleInputChange}>
                                                                <option value="">Select SubGroup</option>
                                                                {subGroups &&
                                                                    subGroups.map((group, index) => (
                                                                        group &&
                                                                        <option key={index} value={group.id} {...(comboData.subgroup_id && subgroup === group.id) && { selected: true }}>{group.sub_group_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <h4>Select Items For Combo Options</h4>
                                                        <div className="form-group">
                                                            {comboChoices.map((x: any, i: any) => {
                                                                // let obj = x.choicesItemOptions.find((items: any) => items.value == x.menu_item_id);
                                                                // if (obj) {
                                                                //     selectedoption = {
                                                                //         value: obj.value,
                                                                //         label: obj.label
                                                                //     }
                                                                // }
                                                                return (
                                                                    <>
                                                                        <div className='row justify-content-center'>
                                                                            <div className="col-6 mt-4" >
                                                                                <div className="form-group" >
                                                                                    <input id={`checkboxFull${i}`} type="checkbox" name="is_full_pizza" checked={x.is_full_pizza} onChange={e => this.handleComboChoiceInputChange(e, i)} className="checkbox-template" />
                                                                                    <label htmlFor={`checkboxFull${i}`}>Full Pizza</label>
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-6 mt-4" >
                                                                                <div className="form-group" >
                                                                                    <input id={`checkboxHalf${i}`} type="checkbox" name="is_half_n_half" checked={x.is_half_n_half} onChange={e => this.handleComboChoiceInputChange(e, i)} className="checkbox-template" />
                                                                                    <label htmlFor={`checkboxHalf${i}`}>HALF n HALF</label>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div key={i} className="row">
                                                                            <div className="col">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Priority</label>
                                                                                    <input id="priority" type="number" onWheel={(e: any) => e.target.blur()} name="priority" defaultValue={x.priority} onKeyDown={this.blockInvalidChar} required data-msg="Please enter Priority" className="input-material" onChange={e => this.handleComboChoiceInputChange(e, i)} />
                                                                                </div>
                                                                            </div>
                                                                            <div className='col'>
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Pos Code</label>
                                                                                    <input
                                                                                        name="size_pos_code"
                                                                                        type="text"
                                                                                        value={x.size_pos_code}
                                                                                        data-msg="Please enter pos code"
                                                                                        className="input-material"
                                                                                        onChange={e => this.handleComboChoiceInputChange(e, i)}
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                            {(x.is_half_n_half || x.is_full_pizza) &&
                                                                                <>{x.is_full_pizza && <div className='col-lg-3 col-sm-3 col-6'>
                                                                                    <div className="form-group">
                                                                                        <label className="form-control-label">Recipe</label>
                                                                                        <input
                                                                                            name="recipe"
                                                                                            type="text"
                                                                                            value={x.recipe}
                                                                                            data-msg="Please enter recipe"
                                                                                            className="input-material"
                                                                                            onChange={e => this.handleComboChoiceInputChange(e, i)} />
                                                                                    </div>
                                                                                </div>
                                                                                }<div className="col-lg-3 col-sm-3 col-6">
                                                                                        <div className="form-group">
                                                                                            <label className="form-control-label mb-3">Variant</label>
                                                                                            <select onChange={(e: any) => this.handleComboChoiceInputChange(e, i)} name="variant_id" className="form-control text-capitalize mt-2">
                                                                                                <option value="Select variant" selected disabled>Select variant</option>
                                                                                                {variants &&
                                                                                                    variants.map((variant: any, index: any) => (
                                                                                                        <option value={variant.value} {...(x.variant_id && x.variant_id.id === variant.value) && { selected: true }}>{variant.label}</option>
                                                                                                    ))}
                                                                                            </select>
                                                                                        </div>
                                                                                    </div></>
                                                                            }
                                                                        </div>
                                                                        <div className='row'>
                                                                            <div className="col">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Group Name <span className="text-danger">*</span></label>
                                                                                    <input
                                                                                        name="group_name"
                                                                                        type="text"
                                                                                        value={x.group_name}
                                                                                        data-msg="Please enter group name"
                                                                                        className="input-material"
                                                                                        onChange={e => this.handleComboChoiceInputChange(e, i)}
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div className='row'>
                                                                            {x.is_half_n_half ? <>
                                                                                <div className="col">
                                                                                    <div className="form-group">
                                                                                        <label className="form-control-label">Crusts <span className="text-danger">*</span></label>
                                                                                        <Select
                                                                                            isMulti
                                                                                            name="crusts"
                                                                                            options={crusts}
                                                                                            value={x.crusts}
                                                                                            className="text-capitalize basic-multi-select mt-2"
                                                                                            classNamePrefix="select"
                                                                                            onChange={(e) => this.handleComboChoiceInputChange(e, i, "crusts")}
                                                                                        />
                                                                                    </div>
                                                                                </div>
                                                                                <div className="col">
                                                                                    <div className="form-group">
                                                                                        <label className="form-control-label">Flavours <span className="text-danger">*</span></label>
                                                                                        <Select
                                                                                            isMulti
                                                                                            name="flavours"
                                                                                            options={flavours}
                                                                                            defaultValue={x.flavours}
                                                                                            className="text-capitalize basic-multi-select mt-2"
                                                                                            classNamePrefix="select"
                                                                                            onChange={(e) => this.handleComboChoiceInputChange(e, i, "flavours")}
                                                                                        />
                                                                                    </div>
                                                                                </div>
                                                                                <div className="col">
                                                                                    <div className="form-group">
                                                                                        <label className="form-control-label">Topings <span className="text-danger">*</span></label>
                                                                                        <Select
                                                                                            isMulti
                                                                                            name="topings"
                                                                                            options={topings}
                                                                                            defaultValue={x.topings}
                                                                                            className="text-capitalize basic-multi-select mt-2"
                                                                                            classNamePrefix="select"
                                                                                            onChange={(e) => this.handleComboChoiceInputChange(e, i, "topings")}
                                                                                        />
                                                                                    </div>
                                                                                </div>
                                                                                <div className="col">
                                                                                    <div className="form-group">
                                                                                        <label className="form-control-label">Condiments</label>
                                                                                        <Select
                                                                                            isMulti
                                                                                            name="condiments"
                                                                                            options={condiments}
                                                                                            defaultValue={x.condiments}
                                                                                            className="text-capitalize basic-multi-select mt-2"
                                                                                            classNamePrefix="select"
                                                                                            onChange={(e) => this.handleComboChoiceInputChange(e, i, "condiments")}
                                                                                        />
                                                                                    </div>
                                                                                </div></> : x.is_full_pizza ?
                                                                                <>
                                                                                    <div className="col">
                                                                                        <div className="form-group">
                                                                                            <label className="form-control-label">Crusts <span className="text-danger">*</span></label>
                                                                                            <Select
                                                                                                isMulti
                                                                                                name="crusts"
                                                                                                options={crusts}
                                                                                                defaultValue={x.crusts}
                                                                                                className="text-capitalize basic-multi-select mt-2"
                                                                                                classNamePrefix="select"
                                                                                                onChange={(e) => this.handleComboChoiceInputChange(e, i, "crusts")}
                                                                                            />
                                                                                        </div>
                                                                                    </div>
                                                                                    <div className="col">
                                                                                        <div className="form-group">
                                                                                            <label className="form-control-label">Topings <span className="text-danger">*</span></label>
                                                                                            <Select
                                                                                                isMulti
                                                                                                name="topings"
                                                                                                options={topings}
                                                                                                value={x.topings}
                                                                                                className="text-capitalize basic-multi-select mt-2"
                                                                                                classNamePrefix="select"
                                                                                                onChange={(e) => this.handleComboChoiceInputChange(e, i, "topings")}
                                                                                            />
                                                                                        </div>
                                                                                    </div>
                                                                                    <div className="col">
                                                                                        <div className="form-group">
                                                                                            <label className="form-control-label">Condiments</label>
                                                                                            <Select
                                                                                                isMulti
                                                                                                name="condiments"
                                                                                                options={condiments}
                                                                                                value={x.condiments}
                                                                                                className="text-capitalize basic-multi-select mt-2"
                                                                                                classNamePrefix="select"
                                                                                                onChange={(e) => this.handleComboChoiceInputChange(e, i, "condiments")}
                                                                                            />
                                                                                        </div>
                                                                                    </div></> :
                                                                                <div className="col">
                                                                                    <div className="form-group">
                                                                                        <label className="form-control-label">Modifier Group <span className="text-danger">*</span></label>
                                                                                        <Select
                                                                                            isMulti
                                                                                            name="groups"
                                                                                            options={addons}
                                                                                            value={x.mod_groups}
                                                                                            className="text-capitalize basic-multi-select mt-2"
                                                                                            classNamePrefix="select"
                                                                                            onChange={(e) => this.handleComboChoiceInputChange(e, i, "modGroups")}
                                                                                        />
                                                                                    </div>
                                                                                </div>
                                                                            }
                                                                        </div>
                                                                        <div className="col-12 text-right">
                                                                            {comboChoices.length !== 1 &&
                                                                                <button className="btn btn-sm btn-outline-danger"
                                                                                    onClick={() => this.handleRemoveComboChoice(i)}><i className="fa fa-trash"></i></button>}
                                                                            {comboChoices.length - 1 === i && <button className="btn btn-sm btn-primary ml-2" onClick={() => this.handleAddComboChoice(i)}><i className="fa fa-plus"></i></button>}
                                                                        </div>
                                                                    </>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="row">
                                                    <div className="col-lg-6 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Description</label>
                                                            <input id="combodesc" type="text" name="combodesc" value={combodesc} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-6 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Alt Tag</label>
                                                            <input id="altTag" type="text" name="altTag" defaultValue={comboData.alt_text} onChange={this.handleInputChange} className="input-material" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-lg-5 col-5 col-5 mb-3">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Ordering Channel <span className="text-danger">*</span></label>
                                                            <select name="channel" className="form-control text-capitalize mt-2" required data-msg="Please select Channel" onChange={this.handleInputChange}>
                                                                <option value="">Select Channel</option>
                                                                {channels &&
                                                                    channels.map((channel, index) => (
                                                                        <option key={index} value={channel.sales_channel_id} {...comboData.combo_channel_id && comboData.combo_channel_id.sales_channel_id === channel.sales_channel_id && { selected: true }}>{channel.channel_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-5 col-5 mb-3">
                                                        <div className="form-group">
                                                            <label className="form-control-label mb-3">Combo Image <span className="text-danger"> {(comboData.images && comboData.images.length) > 0 && `(${comboData.images.length} Combo Images)`}</span></label>
                                                            <input id="files" type="file" name="files" className="form-control-file" onChange={this.fileSelectedHandler} multiple />
                                                            <p style={{ color: "red", fontSize: "12px" }}>Image size should be less than 150 kb</p>
                                                            {/* <small className="form-text">You can also choose multiple images.</small> */}
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-2 col-2">
                                                        <img style={{ width: "100px" }} alt="No Image" src={`${IMAGE_URL}${(comboData.images && comboData.images.length > 0) && comboData.images[0].image_url}`} />
                                                    </div>
                                                    {/* <div className="col-lg-3 col-sm-4 col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label mb-3">Thumbnail Image <span className="text-danger"> {(comboData.images && comboData.images.length) > 0 && `(${comboData.images.length} Thumbnail Images)`}</span></label>
                                                            <input id="files" type="file" name="files" className="form-control-file" onChange={this.thumbnailFileSelectedHandler} multiple />
                                                            <p style={{ color: "red", fontSize: "12px" }}>Image size should be less than 50 kb</p>
                                                            <small className="form-text">You can also choose multiple images.</small>
                                                        </div>
                                                    </div> */}
                                                </div>
                                                <div className="row">
                                                    {/* <div className="col-lg-8" >
                                                        <div className="form-group">
                                                            <label className="form-control-label">Pricing By Ordering Modes <span className="text-danger">*</span></label>
                                                            <Select
                                                                isMulti
                                                                name="mode_price"
                                                                value={this.state.order_modes_price}
                                                                options={mode}
                                                                className="text-capitalize basic-multi-select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handlePricingOrderModesInputChange(e, i)}
                                                            />
                                                        </div>
                                                    </div> */}
                                                    <div className="col-md-6 col-12" >
                                                        <div className="form-group">
                                                            <label className="form-control-label">Ordering Mode <span className="text-danger">*</span></label>
                                                            <Select
                                                                isMulti
                                                                name="mode"
                                                                value={mode}
                                                                options={this.props.orderingModes}
                                                                className="text-capitalize basic-multi-select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handleOrderModesInputChange(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className="col-md-6 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Cost Price <span className="text-danger">*</span></label>
                                                            <input id="combocost" type="number" onWheel={(e: any) => e.target.blur()} name="combocost" min="1" onKeyDown={this.blockInvalidChar} value={combocost} required data-msg="Please enter Combo Cost" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    {this.state.order_modes_price.length > 0 && this.state.order_modes_price.map((channel: any, index: any) => (
                                                        <>
                                                            <div className="col-lg-4 col-sm-4 col-6">
                                                                <strong>{index + 1} -</strong>
                                                                <strong style={{ fontSize: '16px' }} className="ml-2">
                                                                    {channel.label}
                                                                </strong>
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Max Retail Price <small className="text-danger">*</small></label>
                                                                    <input
                                                                        name="mrp"
                                                                        type="number" onWheel={(e: any) => e.target.blur()}
                                                                        min="1"
                                                                        onKeyDown={this.blockInvalidChar}
                                                                        defaultValue={channel.mrp}
                                                                        value={channel.mrp}
                                                                        data-msg="Please enter mrp"
                                                                        className="input-material"
                                                                        onChange={e => this.handleVariantPriceInputChange(e, index)} />
                                                                </div>
                                                            </div>
                                                        </>
                                                    ))}
                                                </div>
                                                <div className='row mt-4'>
                                                    <div className="col-lg-2 col-sm-4" >
                                                        <div >
                                                            <input id="checkboxCustom1" type="checkbox" name="is_featured" checked={is_featured} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom1">Featured</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4">
                                                        <div >
                                                            <input id="checkboxCustom2" type="checkbox" name="topDeal" checked={topDeal} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom2">Top Deal</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4">
                                                        <div >
                                                            <input id="checkboxCustom3" type="checkbox" name="is_hide" checked={is_hide} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom3">Is Hidden</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4">
                                                        <div >
                                                            <input id="checkboxCustom4" type="checkbox" name="is_voucher" checked={is_voucher} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom4">Is Voucher</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4">
                                                        <div >
                                                            <input id="checkboxCustom4" type="checkbox" name="is_foodpanda" checked={is_foodpanda} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom4">Is Food Panda</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-2 col-sm-4">
                                                        <div >
                                                            <input id="checkboxCustom5" type="checkbox" name="is_blurry" checked={is_blurry} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom5">Is MidNight</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className='row mt-4'>
                                                    <div className="col-lg-2 col-sm-4" >
                                                        <div >
                                                            <input id="freeDeliveryCombo" type="checkbox" name="is_free_delivery" checked={is_free_delivery} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="freeDeliveryCombo">Free Delivery</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-lg-3 col-sm-4 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom1" type="radio" name="settime" value="alltime" checked={settime === 'alltime'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom1">Available for all time</label>
                                                        </div>
                                                    </div>
                                                
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom2" type="radio" name="settime" value="customtime" checked={settime === 'customtime'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom2">Available for specific time</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom0" type="checkbox" name="specific_days" checked={specific_days} onChange={this.handleSpecificDaysCheck} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom0">Available for specific days</label>
                                                        </div>
                                                    </div>
                                                    {/* <div className="col-lg-2 col-sm-4 col-4 mt-5" >
                                                        <div>
                                                            <input id="checkboxCustom1" type="checkbox" name="taxstatus" checked={hero_item} onChange={this.handleHeroItem} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom1">Hero Item</label>
                                                        </div>
                                                    </div> */}
                                                </div>
                                                {hero_item &&
                                                    <div className='row'>
                                                        <div className="col-6 mt-2">
                                                            <div className="form-group">
                                                                <label className="form-control-label mb-3">Hero Item Image(Desktop)</label>
                                                                <input id="files" type="file" name="files" accept="image/*" className="form-control-file" onChange={this.heroItemImageSelectedHandler} />
                                                                <p style={{ color: "red", fontSize: "12px" }}>Image size should be less than 1 mb</p>
                                                            </div>
                                                        </div>
                                                        <div className="col-6 mt-2">
                                                            <div className="form-group">
                                                                <label className="form-control-label mb-3">Hero Item Image(Mobile)</label>
                                                                <input id="files" type="file" name="files" accept="image/*" className="form-control-file" onChange={this.heroItemMobileImageSelectedHandler} />
                                                                <p style={{ color: "red", fontSize: "12px" }}>Image size should be less than 1 mb</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                {
                                                    settime == 'customtime' &&
                                                    <div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-lable">Combo Timing</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <input id="combostart" type="date" name="combostart" required value={combostart} data-msg="Please enter starting time" className="input-material" onChange={this.handleInputChange} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <input id="comboclose" type="date" name="comboclose" required value={comboclose} data-msg="Please enter closing time" className="input-material" onChange={this.handleInputChange} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                <div className="row">
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div >
                                                            <input id="radioCustom3" type="radio" name="is_lsm" value="0" checked={is_lsm == '0'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom3">Available for all stores</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div >
                                                            <input id="radioCustom4" type="radio" name="is_lsm" value="1" checked={is_lsm == '1'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom4">Available for specific stores</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6 mt-5" >
                                                        <div >
                                                            <input id="radioCustom5" type="radio" name="is_lsm" value="2" checked={is_lsm == '2'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom5">Available for stores except</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3 col-sm-3 col-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Priority</label>
                                                            <input id="priority" type="text" name="priority" value={priority} required data-msg="Please enter Priority" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    {
                                                        is_lsm == '1' &&
                                                        <div className="col" >
                                                            <div className="form-group">
                                                                <label className="form-control-label">Select Store <span className="text-danger">*</span></label>
                                                                <Select
                                                                    isMulti
                                                                    defaultValue={storesJson}
                                                                    name="stores"
                                                                    options={this.props.stores}
                                                                    className="text-capitalize basic-multi-select mt-2"
                                                                    classNamePrefix="select"
                                                                    onChange={(e, i) => this.handleStoresInputChange(e, i)}
                                                                />
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <div className="row">
                                                    {
                                                        is_lsm == '2' &&
                                                        <div className="col" >
                                                            <div className="form-group">
                                                                <label className="form-control-label">Select Store <span className="text-danger">*</span></label>
                                                                <Select
                                                                    isMulti
                                                                    defaultValue={storesJson}
                                                                    name="stores"
                                                                    options={this.props.stores}
                                                                    className="text-capitalize basic-multi-select mt-2"
                                                                    classNamePrefix="select"
                                                                    onChange={(e, i) => this.handleStoresInputChange(e, i)}
                                                                />
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                {specific_days &&
                                                    <div className='row'>
                                                        <div className="col">
                                                            <div className="form-group">
                                                                {
                                                                    daysTiming.map((x: any, i: any) => {
                                                                        let timePeriod = x.timePeriods;
                                                                        return (
                                                                            <div key={i} className='mb-2' style={{ borderBottom: "1px dotted grey" }}>
                                                                                <div className="row">
                                                                                    <div className="col-lg-8 col-md-8 col-8">
                                                                                        <div className="form-group">
                                                                                            <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                                            <Select
                                                                                                options={this.weeklyDays.filter(o1 => !daysTiming.some((o2: any) => o1.value === o2.day))}
                                                                                                className="text-capitalize basic-multi-select"
                                                                                                classNamePrefix="select"
                                                                                                value={{ value: x.value, label: x.label }}
                                                                                                onChange={(e) => this.handleWeeklyDaysInputChange(e, i)}
                                                                                            />
                                                                                        </div>
                                                                                    </div>
                                                                                    <div className="col-lg-4 col-md-4 col-4 mt-4 py-2">
                                                                                        {daysTiming.length !== 1 &&
                                                                                            <button className="btn btn-sm btn-outline-danger"
                                                                                                onClick={() => this.handleWeeklyDaysRemoveClick(i)}><i className="fa fa-trash"></i></button>}
                                                                                        {(daysTiming.length - 1 === i && daysTiming.length < 7) && <button className="btn btn-sm btn-primary ml-2" onClick={(e: any) => this.handleweeklyDaysAddClick(e, i)}><i className="fa fa-plus"></i></button>}
                                                                                    </div>
                                                                                </div>
                                                                                {timePeriod.map((t: any, pindex: any) => {
                                                                                    return (
                                                                                        <div className="row mb-3">
                                                                                            <div className="col-lg-3 col-md-3">
                                                                                            </div>
                                                                                            <div className="col-lg-3 col-md-3 col-6">
                                                                                                <div className="form-group">
                                                                                                    <label className="form-control-label mb-0">Open Time <span className="text-danger">*</span></label>
                                                                                                    <input id="open" type="time" name="open" value={t.open} className="input-material" onChange={(e) => this.handleWeeklyDaysInputChange(e, i, pindex)} />
                                                                                                </div>
                                                                                            </div>
                                                                                            <div className="col-lg-3 col-md-3 col-6">
                                                                                                <div className="form-group">
                                                                                                    <label className="form-control-label mb-0">Close Time <small className="text-danger">*</small></label>
                                                                                                    <input id="close" type="time" name="close" value={t.close} className="input-material" onChange={(e) => this.handleWeeklyDaysInputChange(e, i, pindex)} />
                                                                                                    {(timePeriod.length - 1 == pindex) && <small className="text-primary" style={{ cursor: 'pointer' }} onClick={(e: any) => this.handleweeklyDaysAddClick(e, i, pindex)}>+ Add hours</small>}
                                                                                                </div>
                                                                                            </div>
                                                                                            <div className="col-lg-3 col-md-3 col-12">
                                                                                                {timePeriod.length !== 1 &&
                                                                                                    <button className="btn btn-sm btn-outline-danger"
                                                                                                        onClick={() => this.handleWeeklyDaysRemoveClick(i, pindex)}><i className="fa fa-trash"></i></button>}
                                                                                            </div>
                                                                                        </div>
                                                                                    )
                                                                                })}
                                                                            </div>
                                                                        );
                                                                    })
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                }

                                                <div className="form-group d-flex justify-content-end mt-4">
                                                    {(this.props.comboLoad) ?
                                                        <button className='btn btn-primary d-flex justify-content-end align-item-center' disabled={this.props.comboLoad}><Loader type="TailSpin" color="white" height={30} width={30} /></button> :
                                                        <button onClick={this.handleSaveBtnClick} disabled={!this.isComboReady() || !this.state.flag} className="btn btn-primary">Update Combo</button>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div >
        );
    }
}
const mapStateToProps = (state: any) => {
    return {
        menus: state.menu.menus,
        channels: state.store.channels,
        combooptions: state.menu.combooptions,
        stores: state.menu.storesoptions,
        comboData: state.menu.comboData,
        comboChoices: state.menu.comboChoices,
        comboChoiceItems: state.menu.comboChoiceItems,
        choiceIndex: state.menu.choiceIndex,
        menuItems: state.menu.items,
        groups: state.menu.groups,
        addons: state.menu.addons,
        crusts: state.menu.crusts,
        topings: state.menu.topings,
        flavours: state.menu.flavours,
        condiments: state.menu.condiments,
        orderingModes: state.menu.orderModes,
        subGroups: state.menu.subGroups,
        variants: state.menu.variants,
        comboLoad: state.menu.comboLoad
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        editCombo: function (id: any, data: any) {
            dispatch(editCombo(id, data));
        },
        getCombo: function (id: number) {
            dispatch(getCombo(id));
        },
        channelList: function () {
            dispatch(channelList())
        },
        groupedItemsList: function (menu_id: any) {
            dispatch(groupedItemsList(menu_id))
        },
        menusList: function () {
            dispatch(menusList())
        },
        variantsList: function () {
            dispatch(variantsList())
        },
        storesList: () => {
            dispatch(storesListForMultiSelect())
        },
        comboChoiceItemsByGroupId: function (id: any, index: any) {
            dispatch(comboChoiceItemsByGroupId(id, index))
        },
        groupsList: function () {
            dispatch(groupsList())
        },
        logoutUser: function () {
            dispatch(logoutUser());
        },
        OrderingModes: () => {
            dispatch(OrderingModes())
        },
        subGroupListByGroupId: function (id: any) {
            dispatch(subGroupListByGroupId(id))
        },
        modGroupsList: function () {
            dispatch(modGroupsList())
        },
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(EditCombo);