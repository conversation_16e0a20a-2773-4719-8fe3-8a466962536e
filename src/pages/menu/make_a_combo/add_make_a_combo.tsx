import React, { Component } from 'react';
import { AddMakeAComboProps, AddMakeAComboState, AddMenuItemProps, AddMenuItemState } from '../../../interfaces/menu'
import { connect } from 'react-redux'
import Footer from '../../../components/footer/main';
import Sidebar from '../../../components/sidebar';
import Topbar from '../../../components/topbar';
import jwt from 'jsonwebtoken'
import { secretKey } from '../../../secret'
import { addItems, checkDuplicateErpId, checkDuplicatePosCode, groupsList, logoutUser, menusList, statesList, storesListForMultiSelect } from '../../../redux'
import { Link, Redirect } from 'react-router-dom';
import CheckChanges from '../../../components/confirmOnLeave'
import moment from 'moment';
import Select from 'react-select';
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { addMakeACombo, blockUnblockMakeACombo, getItemsByGroup, getVariantsByItem, OrderingModes } from '../../../redux/actions/menuAction';
toast.configure();
class AddMakeACombo extends Component<AddMakeAComboProps, AddMakeAComboState> {
    constructor(props: any) {
        super(props);
        this.state = {
            name: "",
            group_id: null,
            menu_id:null,
            type: "single",
            sizejson: [
                {
                    group_id: "",
                    menu_item_id: "",
                    variant_id:""
                }
            ]
        }
        this.handleSaveBtnClick = this.handleSaveBtnClick.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
    }
    componentDidMount() {
        this.props.menusList();
        this.props.groupsList();
        document.title = "DominosCMS | Make a Combo"
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        this.setState({
            [event.target.name]: event.target.value
        });
    }
    // handle click event of the Time Radio buttons
    handleChangeRad = (event: { target: { name: any; value: any; } }) => {
        this.setState({ [event.target.name]: event.target.value })
    }
    handleVariantInputChange = (e: any, index: any) => {
        const { name, value } = e.target;
        const list: any = this.state.sizejson;
        list[index][name] = value;
        this.setState({ sizejson: list });
    };
    // handle click event of the Remove button
    handleRemoveClick = (index: any) => {
        const list = this.state.sizejson;
        list.splice(index, 1);
        this.setState({ sizejson: list });
    };
    // handle click event of the Add button
    handleAddClick = (i: any) => {
        const list: any = this.state.sizejson;
            if (list[i]["group_id"] == "" || list[i]["menu_item_id"] == "" || (list[i]["variant_id"] == "")) {
                alert("Please fill all mandatory variant fields")
            } else {
                let obj:any = {
                    group_id:"",
                    menu_item_id:"",
                    variant_id:""
                }
                list.push(obj)
                this.setState({sizejson:list})
            } 
    };
    blockSpecialCharacters = () => {
        var checkString = this.state.name;
        if (checkString != "") {
            if (/[%]/.test(checkString)) {
                alert("Your item name has '%' sign. \nIt is not allowed.\nPlease remove it and try again.");
                return (false);
            } else {
                return true
            }
        }
    }
    handleSaveBtnClick = () => {
            let {sizejson,name,type,menu_id,group_id} = this.state
            let data = {
                name,
                menu_id,
                group_id,
                type
            }

            if (sizejson[sizejson.length - 1]["variant_id"] !== "" && sizejson[sizejson.length - 1]["group_id"] !== "" && sizejson[sizejson.length - 1]["menu_item_id"] !== '') {
                    Object.assign(data,{options:sizejson})
                    this.props.saveData(data);
            } else {
                alert("Please fill all mandatory variant fields")
            }
        }
    

    handleGroupChange = (e:any,index:any) => {
        const { label, value } = e;
        this.props.getItemsByGroup(value)
        const list: any = this.state.sizejson;
        list[index].group_id = value;
        this.setState({ sizejson: list });
    }

    handleItemsInputChange = (e:any,index:any) => {
        const { label, value } = e;
        this.props.getVariantsByItem(value)
        const list: any = this.state.sizejson;
        list[index].menu_item_id = value;
        this.setState({ sizejson: list });
    }

    handleVariantChange = (e:any,index:any) => {
        const { label, value } = e;
        const list: any = this.state.sizejson;
        list[index].variant_id = value;
        this.setState({ sizejson: list });
    }
    isMenuItemReady = () => {
        let {name,group_id,menu_id,type} = this.state
        return name != '' && group_id != null && menu_id != null
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        let msg;
        let messcolor;
        const { isInserted, message, groups, menus, items, variants } = this.props;
        const { sizejson } = this.state;
        if (!isInserted) {
            msg = message;
            messcolor = 'red'
        }
        return (
            <div className="page">
                <CheckChanges path="/add-item" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Make A Combo Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/make-a-combo" className="text-primary">Make A Combo</Link></li>
                                <li className="breadcrumb-item active">Add Make A Combo</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <strong><p className="text-center font-weight-light mb-4" style={{ color: messcolor }}>
                                                    {msg}
                                                </p></strong>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Make A Combo Name <span className="text-danger">*</span></label>
                                                            <input id="name" type="text" name="name" required data-msg="Please enter Item Name" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>


                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Type<span className="text-danger">*</span></label>
                                                            <select name="type" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleInputChange}>
                                                                <option value='single'>Single Select</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    {/* <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Description</label>
                                                            <input id="itemdesc" type="text" name="itemdesc" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div> */}
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Select Menu<span className="text-danger">*</span></label>
                                                            <select name="menu_id" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleInputChange}>
                                                                <option>Select Menu</option>
                                                                {menus &&
                                                                    menus.map((menu, index) => (
                                                                        menu &&
                                                                        <option key={index} value={menu.menu_id}>{menu.menu_name + "-" + menu.store_type_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Select Group<span className="text-danger">*</span></label>
                                                            <select name="group_id" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleInputChange}>
                                                                <option>Select Group</option>
                                                                {groups &&
                                                                    groups.map((group, index) => (
                                                                        group &&
                                                                        <option key={index} value={group.group_id}>{group.group_name + "-" + group.menu_name + "-" + group.store_type_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>

                                                    {/* <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Select Item <span className="text-danger">*</span></label>
                                                            <select name="group" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleItemsByGroupId}>
                                                                <option>Select Item</option>
                                                                {groups &&
                                                                    groups.map((group, index) => (
                                                                        group &&
                                                                        <option key={index} value={group.group_id}>{group.group_name + "-" + group.menu_name + "-" + group.store_type_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div> */}
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Make A Combo Options</label>
                                                            {sizejson.map((x: any, ind: any) => {
                                                                return (
                                                                    <div key={ind}>
                                                                        <div className="row">
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                    <div className="form-group">
                                                                                        <label className="form-control-label">Select Group <span className="text-danger">*</span></label>
                                                                                        <Select
                                                                                            className="basic-multi-select text-capitalize py-2"
                                                                                            options={groups}
                                                                                            // formatGroupLabel={formatGroupLabel}
                                                                                            onChange={(e,i) => this.handleGroupChange(e,ind)}
                                                                                            />
                                                                                    </div>
                                                                            </div>
                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                    <div className="form-group">
                                                                                        <label className="form-control-label">Select Item <span className="text-danger">*</span></label>
                                                                                        <Select
                                                                                            className="basic-multi-select text-capitalize py-2"
                                                                                            options={items}
                                                                                            // formatGroupLabel={formatGroupLabel}
                                                                                            onChange={e => this.handleItemsInputChange(e,ind)}
                                                                                            />
                                                                                    </div>
                                                                            </div>

                                                                            <div className="col-lg-3 col-sm-3 col-6">
                                                                                <div className="form-group">
                                                                                        <label className="form-control-label">Select Variant <span className="text-danger">*</span></label>
                                                                                        <Select
                                                                                            className="basic-multi-select text-capitalize py-2"
                                                                                            options={variants}
                                                                                            // formatGroupLabel={formatGroupLabel}
                                                                                            onChange={e => this.handleVariantChange(e, ind)}
                                                                                            />
                                                                                    </div>
                                                                            </div>

                                                                            {/* <div className="col-lg-12" >
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Pricing By Ordering Modes <span className="text-danger">*</span></label>
                                                                                    <Select
                                                                                        isMulti
                                                                                        name="mode"
                                                                                        options={this.state.mode}
                                                                                        value={x.order_modes_price}
                                                                                        className="text-capitalize basic-multi-select mt-2"
                                                                                        classNamePrefix="select"
                                                                                        onChange={(e, i) => this.handlePricingOrderModesInputChange(e, i, ind)}
                                                                                    />
                                                                                </div>
                                                                            </div>
 */}

                                                                            <div className="col-lg-3 col-sm-3 col-6 d-flex align-items-center">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label"> </label>
                                                                                    {sizejson.length !== 1 &&
                                                                                        <button className="btn btn-sm btn-outline-danger" onClick={() => this.handleRemoveClick(ind)}><i className="fa fa-trash"></i></button>}
                                                                                    {sizejson.length - 1 === ind && <button className="btn btn-sm btn-primary ml-2 float-right" onClick={() => this.handleAddClick(ind)}><i className="fa fa-plus"></i></button>}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>
                                                </div>
                                               


                                                <div className="form-group d-flex justify-content-end mt-4">
                                                    <button className='btn btn-primary' disabled={!this.isMenuItemReady()} onClick={this.handleSaveBtnClick}>Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div>
        );
    }
}
const mapStateToProps = (state: any) => {
    return {
        groups: state.menu.groups,
        message: state.menu.message,
        menus: state.menu.menus,
        isInserted: state.menu.isInserted,
        items:state.menu.itemsByGroup,
        variants:state.menu.variantsByItem
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        groupsList: function () {
            dispatch(groupsList())
        },
        menusList: function () {
            dispatch(menusList())
        },
        saveData:function (data:any) {
            dispatch(addMakeACombo(data))
        },
        getItemsByGroup:(groupid:number) => {
            dispatch(getItemsByGroup(groupid))
        },
        getVariantsByItem:(itemid:number) => {
            dispatch(getVariantsByItem(itemid))
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(AddMakeACombo);