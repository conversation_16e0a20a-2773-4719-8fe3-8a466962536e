import React, { Component } from 'react';
import { Link, Redirect } from 'react-router-dom'
import { EditEmpProps, EditEmpState } from '../../interfaces/employees'
import { connect } from 'react-redux'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import { logoutUser, getEmp, editEmp, empCoupons, empTypeList } from '../../redux'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import CheckChanges from '../../components/confirmOnLeave'
import Select from 'react-select';

class EditEmp extends Component<EditEmpProps, EditEmpState> {
    constructor(props: any) {
        super(props);
        this.state = {
            name: "",
            email_address: "",
            phone_number: "",
            company: "",
            limit: "",
            vip: false,
            empType: "",
            voucher_codes: []
        }
        this.handleSubmit = this.handleSubmit.bind(this);
    }
    componentDidMount() {
        let id = this.props.match.params.id;
        this.props.getEmp(id);
        this.props.empCoupons()
        this.props.empTypeList()
        document.title = "DominosCMS | Employees"
    }
    UNSAFE_componentWillReceiveProps(nextProps: any, nextState: any) {
        this.setState({
            name: nextProps.empData.name,
            email_address: nextProps.empData.email_address,
            phone_number: nextProps.empData.phone_number,
            limit: nextProps.empData.redemption_limit,
            vip: nextProps.empData.vip == 0 ? false : true,
            empType: nextProps.empData.employee_type_id && nextProps.empData.employee_type_id.id,
            voucher_codes: nextProps.empData.voucher_codes ? JSON.parse(nextProps.empData.voucher_codes) : [],
        })
    }
    handleInputChange = (event: { target: { name: any; value: any; }; }) => {
        if (event.target.value == ' ') {
            event.target.value = event.target.value.replace(/\s/g, "");
        }
        this.setState({
            [event.target.name]: event.target.value,
        });
    }
    validatePhone= (event: { target: { name: any; value: any; };})=> {
        if(event.target.value && event.target.value.length<12){
            this.setState({ [event.target.name]: event.target.value})
          }
          else if (event.target.value.length < this.state.phone_number.length) {
            this.setState({ [event.target.name]: event.target.value})
          }
       
    }
    isEmpReady = () => {
        const { name, email_address, phone_number, limit, empType, voucher_codes } = this.state;
        return (name !== "" && email_address !== "" && phone_number !== "" && limit !== "" && empType !== "" && voucher_codes.length > 0);
    }
    handleCouponsInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ voucher_codes: e });
        } else {
            this.setState({ voucher_codes: [] });
        }
    };
    handleEmpType = (e: any, index: any) => {
        if (e && e.value > 0) {
            this.setState({ empType: e.value });
        } else {
            this.setState({ empType: "" });
        }
    };
    handleSubmit = () => {
        let { name, email_address, phone_number, voucher_codes, limit, vip, empType } = this.state;
        let id = this.props.match.params.id;
        let data: any = {
            name: name,
            email_address: email_address,
            phone_number: phone_number,
            redemption_limit: limit,
            employee_type_id: empType,
            voucher_codes: JSON.stringify(voucher_codes)
        }
        if (vip) {
            data.vip = 1;
        } else {
            data.vip = 0;
        }
        this.props.editEmp(id, data);
    }
    handleVip = (event: { target: { name: any; value: any; }; }) => {
        const { vip } = this.state;
        this.setState({ vip: !vip })
    }
    blockInvalidChar = (e: any) => {
        ["@", "+", "-", "=", "/", "!", ">", "<", "%", "&", "*", "(", ")", "_", "{", "}", "[", "]", "|", ":", ";", "?", "#", "$", "^",].includes(e.key) && e.preventDefault();
    }
    render() {
        const { coupons, empTypes, empData } = this.props;
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        let { voucher_codes } = this.state;
        return (
            <div className="page">
                <CheckChanges path="/edit-brand" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Employees Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/employees" className="text-primary">Employees</Link></li>
                                <li className="breadcrumb-item active">Edit Employee</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Name <span className="text-danger">*</span></label>
                                                            <input id="name" type="text" name="name" defaultValue={empData.name} onKeyDown={this.blockInvalidChar} required data-msg="Please enter Name" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Email Address <span className="text-danger">*</span></label>
                                                            <input id="email_address" type="text" name="email_address" defaultValue={empData.email_address} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className='col'>
                                                        <div className="form-group">
                                                            <label className="form-control-label">Employee Type <span className="text-danger">*</span></label>
                                                            <Select
                                                                name="EmpType"
                                                                isClearable
                                                                options={empTypes}
                                                                value={empTypes.map((element: any) => {
                                                                    if (element.id == this.state.empType) {
                                                                        return { label: element.name, value: this.state.empType }
                                                                    }
                                                                })}
                                                                className="text-capitalize select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handleEmpType(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Phone Number <span className="text-danger">*</span></label>
                                                            <input id="phone_number" type="number" onWheel={(e:any) => e.target.blur()} maxLength={11} name="phone_number" value={this.state.phone_number} defaultValue={empData.phone_number} className="input-material" onChange={this.validatePhone} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Redemption Limit <span className="text-danger">*</span></label>
                                                            <input id="limit" type="number" onWheel={(e:any) => e.target.blur()} name="limit" defaultValue={empData.redemption_limit} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col my-2">
                                                        <div>
                                                            <input id="checkboxCustom3" type="checkbox" name="vip" checked={this.state.vip} onChange={this.handleVip} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom3">For Vip</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className='row'>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Select Vouchers <span className="text-danger">*</span></label>
                                                            <Select
                                                                isMulti
                                                                name="coupons"
                                                                options={coupons}
                                                                value={voucher_codes}
                                                                className="text-capitalize basic-multi-select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handleCouponsInputChange(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="form-group d-flex justify-content-end mt-4">
                                                    <button className='btn btn-primary' disabled={!this.isEmpReady()} onClick={this.handleSubmit} >Update Employee</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>

                </div>
            </div >
        );
    }
}
;
const mapStateToProps = (state: any) => {
    return {
        empData: state.employee.empData,
        coupons: state.employee.empCoupons,
        empTypes: state.employee.empTypes,
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: () => { dispatch(logoutUser()); },
        editEmp: (id: any, data: any) => { dispatch(editEmp(id, data)); },
        getEmp: (id: number) => { dispatch(getEmp(id)) },
        empCoupons: function () {
            dispatch(empCoupons())
        },
        empTypeList: function () {
            dispatch(empTypeList())
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(EditEmp);