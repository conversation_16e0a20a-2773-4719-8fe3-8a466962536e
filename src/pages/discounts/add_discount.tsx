import React, { Component } from 'react';
import { AddDiscountProps, AddDiscountState } from '../../interfaces/discounts'
import { connect } from 'react-redux'
import { brandsList, menusList, logoutUser, addDiscount, groupsListForMultiSelect, itemsListForMultiSelect } from '../../redux'
import { Link, Redirect } from 'react-router-dom'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import Select from 'react-select';
import CheckChanges from '../../components/confirmOnLeave'
import { storesList } from '../../redux/actions/reportAction';
import { toast } from 'react-toastify';
import { variantsList } from '../../redux/actions/menuAction';
class AddDiscount extends Component<AddDiscountProps, AddDiscountState> {
    orderModes: { value: string; label: string; }[];
    constructor(props: any) {
        super(props);
        this.orderModes = [
            { value: 'online', label: 'Online' },
            { value: 'mobile', label: 'Mobile' },
            { value: 'callcenter', label: 'Call Center' }
        ];
        this.state = {
            discountvalue: "",
            expiryDate: "",
            percent: "",
            mode: [],
            channel: "delivery",
            type: "menu",
            discountType: "value",
            pos_code: "",
            multiJson: [],
            type_id: "",
            multiStoreJson: [],
            is_lsm: '0',
            for_specific_variations: '0',
            variant_id: [],
        }
        this.handleSaveBtnClick = this.handleSaveBtnClick.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
        this.handleDiscountLevel = this.handleDiscountLevel.bind(this);
        this.handleDiscountType = this.handleDiscountType.bind(this);
    }
    componentDidMount() {
        document.title = "DominosCMS | Discounts"
        this.props.itemsListForMultiSelect();
        this.props.menusList();
        this.props.groupsList();
        this.props.storesList();
        this.props.variantsList();
    }
    handleInputChange(event: { target: { name: any; value: any; }; }) {
        this.setState({
            [event.target.name]: event.target.value,
        });
    }
    handleStoresInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ multiStoreJson: e });
        } else {
            this.setState({ multiStoreJson: [] });
        }
    }
    handleGroupsInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ multiJson: e });
        }
    };
    handleItemsInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ multiJson: e });
        }
    }
    handleOrderModesInputChange = (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ mode: e });
        } else {
            this.setState({ mode: [] });
        }
    };
    handleDiscountType(event: { target: { name: any; value: any; }; }) {
        this.setState({
            percent: "",
            discountvalue: "",
            [event.target.name]: event.target.value,
        });
    }

    isDiscountReady = () => {
        const { pos_code, expiryDate, mode, discountType, discountvalue, type, type_id, multiJson, percent, multiStoreJson, is_lsm, for_specific_variations, variant_id } = this.state;
        if (for_specific_variations == 1 && variant_id.length == 0) {
            return false
        }
        if (is_lsm == "0") {
            if (type == "menu") {
                return (pos_code !== "" && expiryDate !== "" && (mode && mode.length > 0) && type_id !== "" && discountType !== "" && (percent !== "" || discountvalue !== ""));
            } else if (type == "group" || type == "item") {
                return (pos_code !== "" && expiryDate !== "" && (mode && mode.length > 0) && (multiJson && multiJson.length > 0) && discountType !== "" && (percent !== "" || discountvalue !== ""));
            }
        } else if (is_lsm == "1" || is_lsm == "2") {
            if (type == "menu") {
                return (pos_code !== "" && expiryDate !== "" && (mode && mode.length > 0) && type_id !== "" && discountType !== "" && (percent !== "" || discountvalue !== "") && multiStoreJson.length > 0);
            } else if (type == "group" || type == "item") {
                return (pos_code !== "" && expiryDate !== "" && (mode && mode.length > 0) && (multiJson && multiJson.length > 0) && discountType !== "" && (percent !== "" || discountvalue !== "") && multiStoreJson.length > 0);
            }
        }
    }
    handleChangeRad = (event: { target: { name: any; value: any; } }) => {
        this.setState({ [event.target.name]: event.target.value })
    }
    handleVariantChange = async (e: any, index: any) => {
        if (e && e.length > 0) {
            this.setState({ variant_id: e });
        } else {
            this.setState({ variant_id: [] });
        }
    }
    handleSaveBtnClick = (history: any) => {
        let { discountvalue, expiryDate, mode, discountType, type, type_id, multiJson, percent, pos_code, multiStoreJson, is_lsm, for_specific_variations, variant_id } = this.state;
        let data: any = {
            discount_value: discountvalue,
            discount_type: discountType,
            expire_date: expiryDate,
            mode: JSON.stringify(mode),
            pos_code: pos_code,
            type: type,
            percent: percent
        }
        if (is_lsm == '0') {
            data.is_lsm = 0;
        } else if (is_lsm == '1') {
            data.is_lsm = 1;
            data.stores_json = JSON.stringify(multiStoreJson);
        } else if (is_lsm == '2') {
            data.is_lsm = 2;
            data.stores_json = JSON.stringify(multiStoreJson);
        }
        if (type == "menu") {
            data.type_id = type_id;
        } else if (type == "group" || type == "item") {
            data.items_json = JSON.stringify(multiJson);
        }
        if (for_specific_variations == '0') {
            data.for_specific_variations = 0;
        } else if (for_specific_variations == '1') {
            data.for_specific_variations = 1;
            if (variant_id.length == 0) {
                toast.error("Please select at least one variant to continue.")
                return;
            }
            data.variants = JSON.stringify(variant_id)
        }
        this.props.addDiscount(data, history);
    }

    handleDiscountLevel(event: { target: { name: any; value: any; }; }) {
        this.setState({
            type_id: "",
            [event.target.name]: event.target.value,
        });
    }
    blockInvalidChar = (e: any) =>
        ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const { stores, menus, items, variants } = this.props;
        const { is_lsm, for_specific_variations } = this.state;
        return (
            <div className="page">
                <CheckChanges path="/add-discount" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Compaigns Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/marketing/1" className="text-primary">Compaigns</Link></li>
                                <li className="breadcrumb-item active">Add Discount</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Discount Level <span className="text-danger">*</span></label>
                                                            <select name="type" className="form-control text-capitalize mt-2" required data-msg="Please select Type" onChange={this.handleDiscountLevel}>
                                                                <option value='menu'>Menu</option>
                                                                <option value='group'>Group</option>
                                                                <option value='item'>Menu Item</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    {this.state.type === "menu" &&
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Select Menu <span className="text-danger">*</span></label>
                                                                <select name="type_id" className="form-control text-capitalize mt-2" required data-msg="Please select Group" onChange={this.handleInputChange}>
                                                                    <option>Select Menu</option>
                                                                    {menus &&
                                                                        menus.map((menu: any, index: any) => (
                                                                            <option key={index} value={menu.menu_id}>{menu.menu_name}</option>
                                                                        ))
                                                                    }
                                                                </select>
                                                            </div>
                                                        </div>
                                                    }

                                                    {this.state.type === "group" &&
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Select Group <span className="text-danger">*</span></label>
                                                                <Select
                                                                    isMulti
                                                                    name="groups"
                                                                    options={this.props.groups}
                                                                    className="text-capitalize basic-multi-select mt-2"
                                                                    classNamePrefix="select"
                                                                    onChange={(e, i) => this.handleGroupsInputChange(e, i)}
                                                                />
                                                            </div>
                                                        </div>
                                                    }
                                                    {this.state.type === "item" &&
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Select Items <span className="text-danger">*</span></label>
                                                                <Select
                                                                    isMulti
                                                                    name="items"
                                                                    options={items}
                                                                    className="text-capitalize basic-multi-select mt-2"
                                                                    classNamePrefix="select"
                                                                    onChange={(e, i) => this.handleItemsInputChange(e, i)}
                                                                />
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <div className="row">
                                                    <div className="col-lg-4 col-md-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Pulse discount code <span className="text-danger">*</span></label>
                                                            <input id="pos_code" type="text" name="pos_code" value={this.state.pos_code} required data-msg="Please enter POS Code" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-md-4 col-12">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Discount Type <span className="text-danger">*</span></label>
                                                            <select name="discountType" className="form-control text-capitalize mt-2" required data-msg="Please select Type" onChange={this.handleDiscountType}>
                                                                <option value='value'>Value</option>
                                                                <option value='percentage'>Percentage</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    {this.state.discountType === "percentage" &&
                                                        <div className="col-lg-4 col-md-4 col-12">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Discount Percentage <span className="text-danger">*</span></label>
                                                                <input id="percent" type="text" name="percent" min="1" onKeyDown={this.blockInvalidChar} required data-msg="Please enter Discount Percentage" className="input-material" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                    }
                                                    {this.state.discountType === "value" &&
                                                        <div className="col-lg-4 col-md-4 col-12">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Discount value <span className="text-danger">*</span></label>
                                                                <input id="discountvalue" type="text" min="1" onKeyDown={this.blockInvalidChar} name="discountvalue" required data-msg="Please enter Discount Value" className="input-material" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <div className="row">
                                                    <div className="col-md-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Valid till <span className="text-danger">*</span></label>
                                                            <input id="expiryDate" type="date" name="expiryDate" required data-msg="Please enter Expire Date" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-md-6" >
                                                        <div className="form-group">
                                                            <label className="form-control-label">Ordering Mode <span className="text-danger">*</span></label>
                                                            <Select
                                                                isMulti
                                                                name="mode"
                                                                options={this.orderModes}
                                                                className="text-capitalize basic-multi-select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handleOrderModesInputChange(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-lg-6 col-sm-6 col-6 mt-5" >
                                                        <div >
                                                            <input readOnly id="radioCustom1" type="radio" name="for_specific_variations" value="0" checked={for_specific_variations == '0'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom1">Available for all variants</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-6 col-sm-6 col-6 mt-5" >
                                                        <div >
                                                            <input readOnly id="radioCustom2" type="radio" name="for_specific_variations" value="1" checked={for_specific_variations == '1'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom2">Available for specific variants</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {for_specific_variations == "1" &&
                                                    <div className="row">
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <div className="row">


                                                                    <div className="col-12 pt-3">
                                                                        <div className="form-group">
                                                                            <label className="form-control-label">Select Variant
                                                                                <span className="text-danger">*</span>
                                                                            </label>
                                                                            <Select
                                                                                isMulti
                                                                                options={variants}
                                                                                className="text-capitalize basic-multi-select mt-2"
                                                                                classNamePrefix="select"
                                                                                onChange={(e, i) => this.handleVariantChange(e, i)}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                }
                                                <div className="row">
                                                    <div className="col-lg-4 col-sm-4 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom3" type="radio" name="is_lsm" value="0" checked={is_lsm == '0'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom3">Available for all stores</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-sm-4 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom4" type="radio" name="is_lsm" value="1" checked={is_lsm == '1'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom4">Available for specific stores</label>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-4 col-sm-4 col-6 mt-5" >
                                                        <div>
                                                            <input id="radioCustom5" type="radio" name="is_lsm" value="2" checked={is_lsm == '2'} onChange={this.handleChangeRad} className="radio-template" />
                                                            <label htmlFor="radioCustom5">Available for stores except</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {(is_lsm == '1' || is_lsm == '2') && <div className="row">
                                                    <div className="col" >
                                                        <div className="form-group">
                                                            <label className="form-control-label">Select Store <span className="text-danger">*</span></label>
                                                            <Select
                                                                isMulti
                                                                name="stores"
                                                                options={stores}
                                                                className="text-capitalize basic-multi-select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handleStoresInputChange(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                }
                                                <div className="form-group d-flex justify-content-end mt-4">
                                                    <button className='btn btn-primary' disabled={!this.isDiscountReady()} onClick={() => this.handleSaveBtnClick(this.props.history)}>Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div >
        );
    }
}
const mapStateToProps = (state: any) => {
    return {
        brands: state.store.brands,
        menus: state.menu.menus,
        groups: state.menu.groupsptions,
        items: state.menu.allActiveItems,
        stores: state.report.stores,
        variants: state.menu.variants,
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: function () {
            dispatch(logoutUser());
        },
        addDiscount: function (data: any, history: any) {
            dispatch(addDiscount(data, history));
        },
        brandsList: function () {
            dispatch(brandsList())
        },
        menusList: function () {
            dispatch(menusList())
        },
        groupsList: function () {
            dispatch(groupsListForMultiSelect("campaign"))
        },
        itemsListForMultiSelect: function () {
            dispatch(itemsListForMultiSelect("campaign"))
        },
        storesList: () => {
            dispatch(storesList())
        },
        variantsList: function () {
            dispatch(variantsList())
        }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(AddDiscount);