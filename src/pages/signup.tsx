import React, { Component } from 'react';
import { Link } from 'react-router-dom'
import Footer from '../components/footer/login'

export default class Login extends Component<{}, {}> {
    constructor(readonly props: any) {
        super(props);
    }
    componentDidMount() {
        document.title = "DominosCMS | Signup"
    }
    render() {
        return (
            <div className="page login-page">
                <div className="container d-flex align-items-center">
                    <div className="form-holder has-shadow">
                        <div className="row">
                            {/* Logo & InhtmlFormation Panel */}
                            <div className="col-lg-6">
                                <div className="info d-flex align-items-center">
                                    <div className="content">
                                        <div className="logo">
                                            <h1 className="text-center">Castle Farm</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {/* htmlForm Panel  */}
                            <div className="col-lg-6 bg-white">
                                <div className="form d-flex align-items-center">
                                    <div className="content">
                                        <form method="get" className="form-validate">
                                            <div className="form-group">
                                                <input id="register-username" type="text" name="registerUsername" required data-msg="Please enter your username" className="input-material" />
                                                <label htmlFor="register-username" className="label-material">User Name</label>
                                            </div>
                                            <div className="form-group">
                                                <input id="register-email" type="email" name="registerEmail" required data-msg="Please enter your email" className="input-material" />
                                                <label htmlFor="register-email" className="label-material">Email Address</label>
                                            </div>
                                            <div className="form-group">
                                                <input id="register-password" type="password" name="registerPassword" required data-msg="Please enter your password" className="input-material" />
                                                <label htmlFor="register-password" className="label-material">Password</label>
                                            </div>
                                            <div className="form-group terms-conditions">
                                                <input id="register-agree" name="registerAgree" type="checkbox" required value="1" data-msg="Your agreement is required" className="checkbox-template" />
                                                <label htmlFor="register-agree">Agree the terms and policy</label>
                                            </div>
                                            <button id="register" type="submit" className="btn btn-primary">SignUp</button>
                                        </form>
                                        <small>Have already an account? </small><Link to="/" className="login">Login Here</Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }
};