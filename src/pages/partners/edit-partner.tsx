import React, { Component } from 'react';
import { Link, Redirect } from 'react-router-dom'
import { connect } from 'react-redux'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import { logoutUser,editPartner, getPartner } from '../../redux'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import CheckChanges from '../../components/confirmOnLeave'
import { EditPartnerProps, EditPartnerState } from '../../interfaces/partners';

class EditPartner extends Component<EditPartnerProps, EditPartnerState> {
    constructor(props: any) {
        super(props);
        this.state = {
            name: "",
            email_address: "",
            phone_number: "",
            company: "",
            vip: false
        }
        this.handleSubmit = this.handleSubmit.bind(this);
    }
    componentDidMount() {
        let id = this.props.match.params.id;
        this.props.getPartner(id);
        document.title = "DominosCMS | Employees"
    }
    UNSAFE_componentWillReceiveProps(nextProps: any, nextState: any) {
        this.setState({
            name: nextProps.partnerData.name,
            email_address: nextProps.partnerData.email_address,
            phone_number: nextProps.partnerData.phone_number,
            company: nextProps.partnerData.company,
            vip: nextProps.partnerData.vip == 0 ? false : true
        })
    }
    handleInputChange = (event: { target: { name: any; value: any; }; }) => {
        if (event.target.value == ' ') {
            event.target.value = event.target.value.replace(/\s/g, "");
        }
        this.setState({
            [event.target.name]: event.target.value,
        });
    }
    isEmpReady = () => {
        const { name, email_address, phone_number, company } = this.state;
        return (name !== "" && email_address !== "" && phone_number !== "" && company !== "");
    }
    handleSubmit = () => {
        let { name, email_address, phone_number, company, vip } = this.state;
        let id = this.props.match.params.id;
        let data: any = {
            name: name,
            email_address: email_address,
            phone_number: phone_number,
            company: company
        }
        if (vip) {
            data.vip = 1;
        } else {
            data.vip = 0;
        }
        this.props.editPartner(id, data);
    }
    handleVip = (event: { target: { name: any; value: any; }; }) => {
        const { vip } = this.state;
        this.setState({ vip: !vip })
    }
    blockInvalidChar = (e: any) => {
        var regex = new RegExp("[a-zA-Z]");
        let specialkey: any = e.keyCode;
        var key: any = String.fromCharCode(!e.charCode ? e.which : e.charCode);
        if (specialkey == 8 || specialkey == 9) {
            return true;
        } else {
            if (/\S/.test(e.target.value)) {
                // string is not empty and not just whitespace
                if (!regex.test(key)) {
                    if (specialkey == 32) {
                    } else {
                        e.preventDefault()
                    }
                }
            } else {
                ((!regex.test(key) || specialkey == 32)) && e.preventDefault()
            }
        }
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const { partnerData } = this.props;
        return (
            <div className="page">
                <CheckChanges path="/edit-brand" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Business Partner Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/partners" className="text-primary">Employees</Link></li>
                                <li className="breadcrumb-item active">Edit Business Partner</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Name <span className="text-danger">*</span></label>
                                                            <input id="name" type="text" name="name" defaultValue={partnerData.name} onKeyDown={this.blockInvalidChar} required data-msg="Please enter Name" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Email Address <span className="text-danger">*</span></label>
                                                            <input id="email_address" type="text" name="email_address" defaultValue={partnerData.email_address} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Phone Number <span className="text-danger">*</span></label>
                                                            <input id="phone_number" type="text" name="phone_number" defaultValue={partnerData.phone_number} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Company <span className="text-danger">*</span></label>
                                                            <input id="company" type="text" name="company" defaultValue={partnerData.company} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col my-2">
                                                        <div>
                                                            <input id="checkboxCustom3" type="checkbox" name="vip" checked={this.state.vip} onChange={this.handleVip} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom3">For Vip</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="form-group d-flex justify-content-end mt-4">
                                                    <button className='btn btn-primary' disabled={!this.isEmpReady()} onClick={this.handleSubmit} >Update Employee</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>

                </div>
            </div >
        );
    }
}
;
const mapStateToProps = (state: any) => {
    return {
        partnerData: state.partner.partnerData,
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: () => { dispatch(logoutUser()); },
        editPartner: (id: any, data: any) => { dispatch(editPartner(id, data)); },
        getPartner: (id: number) => { dispatch(getPartner(id)) }
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(EditPartner);