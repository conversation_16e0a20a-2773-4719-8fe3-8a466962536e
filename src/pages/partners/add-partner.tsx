import React, { Component } from 'react';
import { connect } from 'react-redux'
import { logoutUser, addPartner } from '../../redux'
import { Link, Redirect } from 'react-router-dom'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import CheckChanges from '../../components/confirmOnLeave'
import { AddPartnerProps, AddPartnerState } from '../../interfaces/partners';
class AddPartner extends Component<AddPartnerProps, AddPartnerState> {
    constructor(props: any) {
        super(props);
        this.state = {
            name: "",
            email_address: "",
            phone_number: "",
            company: "",
            vip: false
        }
        this.handleSaveBtnClick = this.handleSaveBtnClick.bind(this);
    }
    componentDidMount() {
        document.title = "DominosCMS | Business Partner"
    }
    handleInputChange= (event: { target: { name: any; value: any; }; })=> {
        if (event.target.value == ' ') {
            event.target.value = event.target.value.replace(/\s/g, "");
        }
        this.setState({
            [event.target.name]: event.target.value,
        });
    }
    isPartnerReady = () => {
        const { name, email_address, phone_number, company } = this.state;
        return (name !== "" && email_address !== "" && phone_number !== "" && company !== "");
    }
    blockInvalidChar = (e: any) => {
        var regex = new RegExp("[a-zA-Z]");
        let specialkey: any = e.keyCode;
        var key: any = String.fromCharCode(!e.charCode ? e.which : e.charCode);
        if (specialkey == 8 || specialkey == 9) {
            return true;
        } else {
            if (/\S/.test(e.target.value)) {
                // string is not empty and not just whitespace
                if (!regex.test(key)) {
                    if (specialkey == 32) {
                    } else {
                        e.preventDefault()
                    }
                }
            } else {
                ((!regex.test(key) || specialkey == 32)) && e.preventDefault()
            }
        }
    }
    handleVip = (event: { target: { name: any; value: any; }; }) => {
        const { vip } = this.state;
        this.setState({ vip: !vip })
    }
    handleSaveBtnClick = () => {
        let { name, email_address, phone_number, company, vip } = this.state;
        let data: any = {
            name: name,
            email_address: email_address,
            phone_number: phone_number,
            company: company
        }
        if (vip) {
            data.is_vip = 1;
        }
        this.props.addPartner(data);
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        return (
            <div className="page">
                <CheckChanges path="/add-brand" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/*  Page Header */}
                        <header className="page-header py-0">
                            <div className="container-fluid">
                                <div className="d-flex justify-content-between py-3">
                                    <h4 className="mt-2">Business Partner Management</h4>
                                </div>
                            </div>
                        </header>
                        {/* Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/partners" className="text-primary">Business Partners</Link></li>
                                <li className="breadcrumb-item active">Add Business Partner</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Name <span className="text-danger">*</span></label>
                                                            <input id="name" type="text" name="name" onKeyDown={this.blockInvalidChar} required data-msg="Please enter Name" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Email Address <span className="text-danger">*</span></label>
                                                            <input id="email_address" type="text" name="email_address" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Phone Number <span className="text-danger">*</span></label>
                                                            <input id="phone_number" type="text" name="phone_number" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Company <span className="text-danger">*</span></label>
                                                            <input id="company" type="text" name="company" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col my-2">
                                                        <div>
                                                            <input id="checkboxCustom3" type="checkbox" name="vip" checked={this.state.vip} onChange={this.handleVip} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom3">For Vip</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="form-group d-flex justify-content-end mt-4">
                                                    <button className='btn btn-primary' disabled={!this.isPartnerReady()} onClick={this.handleSaveBtnClick} >Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>
                </div>
            </div >
        );
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        logoutUser: () => { dispatch(logoutUser()); },
        addPartner: (data: any) => { dispatch(addPartner(data)); }
    }
}
export default connect(null, mapDispatchToProps)(AddPartner);