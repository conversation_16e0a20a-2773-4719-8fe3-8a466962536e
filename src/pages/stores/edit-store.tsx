import React, { Component } from 'react';
import { Link, Redirect } from 'react-router-dom'
import { EditStoreProps, EditStoreState } from '../../interfaces/store'
import { connect } from 'react-redux'
import jwt from 'jsonwebtoken'
import { secretKey } from '../../secret'
import Select from "react-select";
import { getStore, editStore, countryList, statesList, addStates, tradeZoneList, cityList, activeKmlZoneList, activeZoneList } from '../../redux'
import Topbar from '../../components/topbar'
import Sidebar from '../../components/sidebar'
import Footer from '../../components/footer/main'
import CheckChanges from '../../components/confirmOnLeave'
import Map from './map';
import { businessTypeList } from '../../redux/actions/settingsAction';
import { channelList, getBackupStores, getTradeAreas, storesList, storeTypeList } from '../../redux/actions/storeAction';
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
toast.configure();

class EditStore extends Component<EditStoreProps, EditStoreState> {
    constructor(props: any) {
        super(props);
        this.state = {
            store_id: "",
            storename: "",
            pulse_ip: "",
            storeemail: "",
            escalationemail: "",
            contact1: "",
            contact2: "",
            rgmContact: "",
            posno: "",
            brand: "",
            ntn: "",
            country: "",
            country_code: "",
            state: "",
            state_id: "",
            city_id: "",
            country_id: "",
            city: "",
            address: "",
            storeopen: "",
            storeclose: "",
            errormessage: "",
            expected_delivery_time: 0,
            kml: "",
            lat: 0,
            lng: 0,
            newlat: 0,
            newlng: 0,
            radius: 0,
            businessType: "",
            franchise_name: "",
            timeByDays: '',
            specialDaysTiming: '',
            branchCode: "",
            fp_branch_code: "",
            agg_restaurantId:"",
            aloha_branch_code: "",
            cloudKitchen: "Disabled",
            saleschannel: "",
            storetype: "",
            backup_trade_zone_id: "",
            trade_zone_id: "",
            backupStoreEnable: false,
            backupStore: [
                {
                    storeId: "",
                    backUpStoreId: "",
                    priority: ""
                }
            ],
            tradeAreas: [
                {
                    tradeAreaId: "",
                    tradeArea: "",
                    tradeZone: "",
                    expected_delivery_time: 0,
                    backup_store_id: ""
                }
            ],
            mondayTime: {
                day: 'Monday',
                opening: '',
                closing: ''
            },
            tuesdayTime: {
                day: 'Tuesday',
                opening: '',
                closing: ''
            },
            wednesdayTime: {
                day: 'Wednesday',
                opening: '',
                closing: ''
            },
            thursdayTime: {
                day: 'Thursday',
                opening: '',
                closing: ''
            },
            fridayTime: {
                day: 'Friday',
                opening: '',
                closing: ''
            },
            saturdayTime: {
                day: 'Saturday',
                opening: '',
                closing: ''
            },
            sundayTime: {
                day: 'Sunday',
                opening: '',
                closing: ''
            },
            //Special Days
            specialMondayTime: {
                day: 'Monday',
                opening: '',
                closing: ''
            },
            specialTuesdayTime: {
                day: 'Tuesday',
                opening: '',
                closing: ''
            },
            specialWednesdayTime: {
                day: 'Wednesday',
                opening: '',
                closing: ''
            },
            specialThursdayTime: {
                day: 'Thursday',
                opening: '',
                closing: ''
            },
            specialFridayTime: {
                day: 'Friday',
                opening: '',
                closing: ''
            },
            specialSaturdayTime: {
                day: 'Saturday',
                opening: '',
                closing: ''
            },
            specialSundayTime: {
                day: 'Sunday',
                opening: '',
                closing: ''
            }
        }
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleInputChange = this.handleInputChange.bind(this);
    }
    componentDidMount() {
        let id = this.props.match.params.id;
        this.props.getStore(id);
        this.props.countryList();
        this.props.statesList();
        this.props.businessTypeList();
        this.props.storesList();
        this.props.getBackupStores(id);
        this.props.activekmlZoneList();
        this.props.activeZoneList();
        this.props.getTradeAreas(id);
        this.props.channelsList();
        this.props.storeTypeList();
        this.props.citiesList();
        document.title = "DominosCMS | Stores"
    }
    UNSAFE_componentWillReceiveProps(nextProps: any) {
        if (nextProps && nextProps.storeData && nextProps.storeData.daysTiming) {
            var timings = JSON.parse(nextProps.storeData.daysTiming);
            if (timings.length > 0) {
                timings.forEach((element: any) => {
                    const timeObj = { 'day': element.day, 'opening': element.opening, 'closing': element.closing };
                    if (element.day == "Monday") {
                        this.setState({ mondayTime: timeObj })
                    } else if (element.day == "Tuesday") {
                        this.setState({ tuesdayTime: timeObj })
                    } else if (element.day == "Wednesday") {
                        this.setState({ wednesdayTime: timeObj })
                    } else if (element.day == "Thursday") {
                        this.setState({ thursdayTime: timeObj })
                    } else if (element.day == "Friday") {
                        this.setState({ fridayTime: timeObj })
                    } else if (element.day == "Saturday") {
                        this.setState({ saturdayTime: timeObj })
                    } else if (element.day == "Sunday") {
                        this.setState({ sundayTime: timeObj })
                    }
                });
            }
        }
        if (nextProps && nextProps.storeData && nextProps.storeData.special_daysTiming) {
            var special_daysTiming = JSON.parse(nextProps.storeData.special_daysTiming);
            if (special_daysTiming.length > 0) {
                special_daysTiming.forEach((element: any) => {
                    const timeObj = { 'day': element.day, 'opening': element.opening, 'closing': element.closing };
                    if (element.day == "Monday") {
                        this.setState({ specialMondayTime: timeObj })
                    } else if (element.day == "Tuesday") {
                        this.setState({ specialTuesdayTime: timeObj })
                    } else if (element.day == "Wednesday") {
                        this.setState({ specialWednesdayTime: timeObj })
                    } else if (element.day == "Thursday") {
                        this.setState({ specialThursdayTime: timeObj })
                    } else if (element.day == "Friday") {
                        this.setState({ specialFridayTime: timeObj })
                    } else if (element.day == "Saturday") {
                        this.setState({ specialSaturdayTime: timeObj })
                    } else if (element.day == "Sunday") {
                        this.setState({ specialSundayTime: timeObj })
                    }
                });
            }
        }

        var backupStores = nextProps && nextProps.backupStores;
        var tradeAreas = nextProps && nextProps.tradeAreas;
        if (backupStores.length > 0) {
            var listBackUp: any = [];
            backupStores.forEach((elementBackup: any) => {
                const newlist: any = { backUpStoreId: elementBackup.backupStoreId.store_id, priority: elementBackup.priority, storeId: "" }
                listBackUp.push(newlist);
                // const backupStr: any = this.state.backupStore.concat(newlist);
                // alert("backupStores Length" + backupStr);
                this.setState({ backupStore: listBackUp });
            });
        }
        if (tradeAreas.length > 0) {
            var listTradeArea: any = [];
            tradeAreas.forEach((elementArea: any) => {
                const newlist: any = { tradeAreaId: elementArea.id, tradeArea: elementArea.area_name, tradeZone: elementArea.delivery_zone_id, expected_delivery_time: elementArea.expected_delivery_time, backup_store_id: elementArea.backup_store_id }
                listTradeArea.push(newlist);
                this.setState({ tradeAreas: listTradeArea });
            });
        }
        this.setState({
            store_id: nextProps.storeData.store_id,
            pulse_ip: nextProps.storeData.pulse_ip,
            storename: nextProps.storeData.store_name,
            storeemail: nextProps.storeData.store_email,
            branchCode: nextProps.storeData.branch_code,
            fp_branch_code: nextProps.storeData.fp_branch_code,
            agg_restaurantId:nextProps.storeData.fpRestaurantId,
            escalationemail: nextProps.storeData.escalation_email,
            contact1: nextProps.storeData.contact1,
            contact2: nextProps.storeData.contact2,
            rgmContact: nextProps.storeData.contact3,
            posno: nextProps.storeData.pos_no,
            brand: nextProps.storeData.brand_name,
            city: nextProps.storeData.city,
            state_id: nextProps.storeData.state_id,
            ntn: nextProps.storeData.ntn_number,
            expected_delivery_time: nextProps.storeData.expected_delivery_time,
            country: nextProps.storeData.country_id,
            city_id: nextProps.storeData.city_id,
            address: nextProps.storeData.address,
            backup_trade_zone_id: nextProps.storeData.backup_zone_id,
            trade_zone_id: nextProps.storeData.trade_zone_id,
            radius: nextProps.storeData.trade_zone_coverage,
            kml: nextProps.storeData.trade_zone_shape,
            lat: nextProps.storeData.lat,
            lng: nextProps.storeData.lng,
            timeByDays: nextProps.storeData.isDays == 1 ? true : false,
            specialDaysTiming: nextProps.storeData.is_specialDays == 1 ? true : false,
            backupStoreEnable: nextProps.storeData.is_backup_store == 1 ? true : false,
            businessType: nextProps.storeData.business_type_id,
            franchise_name: nextProps.storeData.franchise_name ? nextProps.storeData.franchise_name : "",
            cloudKitchen: nextProps.storeData.cloudKitchen,
            saleschannel: nextProps.storeData.sales_channel_id,
            storetype: nextProps.storeData.store_type_id
        })
        // if (nextProps.storeData.aloha_branch_code) {
        //     this.setState({ aloha_branch_code: nextProps.storeData.aloha_branch_code });
        // }
        if (nextProps.storeData.store_open_time == '00:00:00') {
            this.setState({
                storeopen: "",
                storeclose: ""
            })
        } else {
            this.setState({
                storeopen: nextProps.storeData.store_open_time,
                storeclose: nextProps.storeData.store_close_time,
            })
        }
    }
    handleInputChange(event: { target: { name: any; value: any }; }) {
        this.setState({
            [event.target.name]: event.target.value
        });
    }
    isStoreReady = () => {
        const { trade_zone_id, saleschannel, pulse_ip, storename, storeemail, businessType, franchise_name, branchCode, fp_branch_code, aloha_branch_code, timeByDays, storeopen, storeclose, mondayTime, expected_delivery_time, tuesdayTime, wednesdayTime, thursdayTime, fridayTime, saturdayTime, sundayTime, state_id, city_id, tradeAreas, specialDaysTiming, specialMondayTime, specialTuesdayTime, specialWednesdayTime, specialThursdayTime, specialFridayTime, specialSaturdayTime, specialSundayTime } = this.state

        if (timeByDays) {
            if (specialDaysTiming) {
                return (trade_zone_id !== "" && pulse_ip !== "" && storename !== "" && city_id !== "" && businessType !== "" && (businessType == 1 ? franchise_name !== "" : true) && state_id !== "" && branchCode !== "" && storeemail !== "" && expected_delivery_time > 0 &&
                    mondayTime.opening !== "" && mondayTime.closing !== "" && tuesdayTime.opening !== "" && tuesdayTime.closing !== ""
                    && wednesdayTime.opening !== "" && wednesdayTime.closing !== "" && thursdayTime.opening !== "" && thursdayTime.closing !== ""
                    && fridayTime.opening !== "" && fridayTime.closing !== "" && saturdayTime.opening !== "" && saturdayTime.closing !== ""
                    && sundayTime.opening !== "" && sundayTime.closing !== "" &&
                    specialMondayTime.opening !== "" && specialMondayTime.closing !== "" && specialTuesdayTime.opening !== "" && specialTuesdayTime.closing !== ""
                    && specialWednesdayTime.opening !== "" && specialWednesdayTime.closing !== "" && specialThursdayTime.opening !== "" && specialThursdayTime.closing !== ""
                    && specialFridayTime.opening !== "" && specialFridayTime.closing !== "" && specialSaturdayTime.opening !== "" && specialSaturdayTime.closing !== ""
                    && specialSundayTime.opening !== "" && specialSundayTime.closing !== "" && tradeAreas[tradeAreas.length - 1].tradeArea !== "" && tradeAreas[tradeAreas.length - 1].tradeZone !== "");
            } else {
                return (trade_zone_id !== "" && pulse_ip !== "" && storename !== "" && city_id !== "" && businessType !== "" && (businessType == 1 ? franchise_name !== "" : true) && state_id !== "" && branchCode !== "" && storeemail !== "" && expected_delivery_time > 0 &&
                    mondayTime.opening !== "" && mondayTime.closing !== "" && tuesdayTime.opening !== "" && tuesdayTime.closing !== ""
                    && wednesdayTime.opening !== "" && wednesdayTime.closing !== "" && thursdayTime.opening !== "" && thursdayTime.closing !== ""
                    && fridayTime.opening !== "" && fridayTime.closing !== "" && saturdayTime.opening !== "" && saturdayTime.closing !== ""
                    && sundayTime.opening !== "" && sundayTime.closing !== "" && tradeAreas[tradeAreas.length - 1].tradeArea !== "" && tradeAreas[tradeAreas.length - 1].tradeZone !== "");
            }
        } else if (specialDaysTiming) {
            return (trade_zone_id !== "" && pulse_ip !== "" && storename !== "" && city_id !== "" && businessType !== "" && (businessType == 1 ? franchise_name !== "" : true) && state_id !== "" && branchCode !== "" && storeemail !== "" && expected_delivery_time > 0 &&
                specialMondayTime.opening !== "" && specialMondayTime.closing !== "" && specialTuesdayTime.opening !== "" && specialTuesdayTime.closing !== ""
                && specialWednesdayTime.opening !== "" && specialWednesdayTime.closing !== "" && specialThursdayTime.opening !== "" && specialThursdayTime.closing !== ""
                && specialFridayTime.opening !== "" && specialFridayTime.closing !== "" && specialSaturdayTime.opening !== "" && specialSaturdayTime.closing !== ""
                && specialSundayTime.opening !== "" && specialSundayTime.closing !== "" && tradeAreas[tradeAreas.length - 1].tradeArea !== "" && tradeAreas[tradeAreas.length - 1].tradeZone !== "");
        } else {
            // return (storename !== "" && storeemail !== "" && kml !== "" && kmlFile !== null && brand !== "" && saleschannel !== "" && storetype !== "" && storeopen !== "" && storeclose !== "");
            return (trade_zone_id !== "" && saleschannel !== null && pulse_ip !== "" && storename !== "" && city_id !== "" && businessType !== "" && (businessType == 1 ? franchise_name !== "" : true) && state_id !== "" && branchCode !== "" && storeemail !== "" && expected_delivery_time > 0 && storeopen !== "" && storeclose !== "" && tradeAreas[tradeAreas.length - 1].tradeArea !== "" && tradeAreas[tradeAreas.length - 1].tradeZone !== "");
        }
    }
    handleRadius = (radius: any) => {    //to Get the Radius from the map component and set in state
        this.setState({
            radius: radius
        })
    }
    handleMapData = (obj: any) => {    //to Get the values from the map component and set in state
        this.setState({
            address: obj.address,
            city: obj.city,
            // state: obj.state,
            country: obj.country,
            country_code: obj.country_code,
            lat: obj.lat,
            lng: obj.lng
        })
        let { countries, states } = this.props;
        let stateExist = false;
        // if (obj.state !== this.state.state) {
        countries.forEach((country) => {
            if (country.country_code === obj.country_code) {
                for (let i = 0; i < states.length; i++) {
                    if (country.country_id == states[i].country_id) {
                        if (country.country_name == states[i].state_name) {
                            stateExist = true;
                            this.setState({ country_id: country.country_id })
                            // this.setState({ state_id: states[i].state_id, country_id: country.country_id })
                            break;
                        } else if (obj.state == states[i].state_name) {
                            stateExist = true;
                            this.setState({ country_id: country.country_id })
                            // this.setState({ state_id: states[i].state_id, country_id: country.country_id })
                            break;
                        }
                    }
                }
                // if (stateExist === false) {
                //     this.setState({ state_id: "" })
                //     let stateObj = {
                //         state_name: obj.state,
                //         country_id: country.country_id,
                //         tax_percent: 0
                //     }
                //     this.props.addStates(stateObj, "store");
                // }
            }
        })
        // }
    }
    checkAreaNameDuplication = () => {
        const { tradeAreas } = this.state;
        var result: any = [];
        // To Remove all Duplicate objects
        var array: any = tradeAreas,
            seen = Object.create(null),
            result = array.filter((o: any) => {
                var key = ['tradeZone', 'tradeArea'].map(k => o[k]).join('|');
                if (!seen[key]) {
                    seen[key] = true;
                    return true;
                }
            });
        if (array.length !== result.length) {
            //check original array length after remove duplicates
            return true;
        } else {
            return false;
        }
    }
    blockInvalidChar = (e: any) =>
        ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

    handleSubmit = async (event: any) => {
        let daysTiming: any = [];
        let special_daysTiming: any = [];
        let { trade_zone_id, backupStoreEnable, backup_trade_zone_id, storename, pulse_ip, storeemail, escalationemail, ntn, contact1, contact2, rgmContact, city, posno, address, state_id, city_id, storeopen, storeclose, kml, lat, lng,
            businessType, timeByDays, specialDaysTiming, backupStore, branchCode, fp_branch_code, agg_restaurantId, mondayTime, expected_delivery_time, tuesdayTime, wednesdayTime, thursdayTime, fridayTime, saturdayTime, sundayTime, country_id, tradeAreas,
            franchise_name, specialMondayTime, specialTuesdayTime, specialWednesdayTime, specialThursdayTime, specialFridayTime, specialSaturdayTime, specialSundayTime } = this.state;
        let id = this.props.match.params.id;
        let { countries } = this.props;
        let DuplicationFlag: any = await this.checkAreaNameDuplication();
        if (DuplicationFlag) {
            toast.error("Duplicate areas found for one tradezone", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
        } else {
            let data: any = {
                store_name: storename,
                store_email: storeemail,
                pulse_ip: pulse_ip,
                trade_zone_id: trade_zone_id,
                escalation_email: escalationemail,
                contact1: contact1,
                contact2: contact2,
                contact3: rgmContact,
                pos_no: posno,
                branch_code: branchCode,
                city: city,
                expected_delivery_time: expected_delivery_time,
                ntn_number: ntn,
                address: address,
                lat: lat,
                lng: lng,
                business_type_id: businessType,
                backupStore: JSON.stringify(backupStore),
                tradeAreas: JSON.stringify(tradeAreas),
                sales_channel_id: this.state.saleschannel,
            }
            if (backup_trade_zone_id && backup_trade_zone_id!=="") {
                data.backup_zone_id = backup_trade_zone_id;
            } else {
                data.backup_zone_id = null;
            }
            if (backupStoreEnable) {
                data.is_backup_store = 1;
            } else {
                data.is_backup_store = 0;
            }
            if (timeByDays) {
                daysTiming.push(mondayTime);
                daysTiming.push(tuesdayTime);
                daysTiming.push(wednesdayTime);
                daysTiming.push(thursdayTime);
                daysTiming.push(fridayTime);
                daysTiming.push(saturdayTime);
                daysTiming.push(sundayTime);
                data.daysTiming = JSON.stringify(daysTiming)
            }
            if (timeByDays === true) {
                data.timeByDays = 1;
            } else {
                data.timeByDays = 0;
            }
            if (specialDaysTiming === true) {
                data.specialDaysTiming = 1;
            } else {
                data.specialDaysTiming = 0;
            }
            if (specialDaysTiming) {
                special_daysTiming.push(specialMondayTime);
                special_daysTiming.push(specialTuesdayTime);
                special_daysTiming.push(specialWednesdayTime);
                special_daysTiming.push(specialThursdayTime);
                special_daysTiming.push(specialFridayTime);
                special_daysTiming.push(specialSaturdayTime);
                special_daysTiming.push(specialSundayTime);
                data.special_daysTiming = JSON.stringify(special_daysTiming);
            }
            if (!timeByDays && !specialDaysTiming) {
                data.store_open_time = storeopen;
                data.store_close_time = storeclose;
                data.daysTiming = '';
                data.special_daysTiming = '';
            }
            if (country_id !== "") {
                data.country_id = country_id;
            }
            if (state_id !== "") {
                data.state_id = state_id;
            }
            if (city_id !== "") {
                data.city_id = city_id;
            }
            if (businessType == 1) {
                data.franchise_name = franchise_name;
            }
            if (fp_branch_code !== "") {
                data.fp_branch_code = fp_branch_code;
            }
            if (agg_restaurantId !== "") {
                data.fpRestaurantId = agg_restaurantId;
            }
            this.props.editStore(id, data);
        }
    }
    handleChannelChange = (e: any) => {
        const { value } = e.target
        this.setState({ saleschannel: value });
    }
    handleTimeByDays = (event: { target: { name: any; value: any; }; }) => {
        const { timeByDays, storeclose, storeopen, } = this.state;
        this.setState({ timeByDays: !timeByDays })
        if (storeclose && storeclose !== "") {
            const mondaytimeObj = { 'day': 'Monday', 'opening': storeopen, 'closing': storeclose };
            this.setState({ mondayTime: mondaytimeObj })
            const tuesdaytimeObj = { 'day': 'Tuesday', 'opening': storeopen, 'closing': storeclose };
            this.setState({ tuesdayTime: tuesdaytimeObj })
            const wedtimeObj = { 'day': 'Wednesday', 'opening': storeopen, 'closing': storeclose };
            this.setState({ wednesdayTime: wedtimeObj })
            const thursdaytimeObj = { 'day': 'Thursday', 'opening': storeopen, 'closing': storeclose };
            this.setState({ thursdayTime: thursdaytimeObj })
            const fridaytimeObj = { 'day': 'Friday', 'opening': storeopen, 'closing': storeclose };
            this.setState({ fridayTime: fridaytimeObj })
            const sattimeObj = { 'day': 'Saturday', 'opening': storeopen, 'closing': storeclose };
            this.setState({ saturdayTime: sattimeObj })
            const sundaytimeObj = { 'day': 'Sunday', 'opening': storeopen, 'closing': storeclose };
            this.setState({ sundayTime: sundaytimeObj })
        }
    }
    handleSpecialDayTimings = (event: { target: { name: any; value: any; }; }) => {
        const { specialDaysTiming, storeclose, storeopen } = this.state;
        this.setState({ specialDaysTiming: !specialDaysTiming })
        const mondaytimeObj = { 'day': 'Monday', 'opening': storeopen, 'closing': storeclose };
        this.setState({ specialMondayTime: mondaytimeObj })
        const tuesdaytimeObj = { 'day': 'Tuesday', 'opening': storeopen, 'closing': storeclose };
        this.setState({ specialTuesdayTime: tuesdaytimeObj })
        const wedtimeObj = { 'day': 'Wednesday', 'opening': storeopen, 'closing': storeclose };
        this.setState({ specialWednesdayTime: wedtimeObj })
        const thursdaytimeObj = { 'day': 'Thursday', 'opening': storeopen, 'closing': storeclose };
        this.setState({ specialThursdayTime: thursdaytimeObj })
        const fridaytimeObj = { 'day': 'Friday', 'opening': storeopen, 'closing': storeclose };
        this.setState({ specialFridayTime: fridaytimeObj })
        const sattimeObj = { 'day': 'Saturday', 'opening': storeopen, 'closing': storeclose };
        this.setState({ specialSaturdayTime: sattimeObj })
        const sundaytimeObj = { 'day': 'Sunday', 'opening': storeopen, 'closing': storeclose };
        this.setState({ specialSundayTime: sundaytimeObj })
    }
    dayOpeningTime = (e: any) => {
        const { name, value } = e.target;
        if (name == "mondayTime") {
            const list: any = this.state.mondayTime;
            list.opening = value;
            this.setState({ mondayTime: list });

        } else if (name == "tuesdayTime") {
            const list: any = this.state.tuesdayTime;
            list.opening = value;
            this.setState({ tuesdayTime: list });

        } else if (name == "wednesdayTime") {
            const list: any = this.state.wednesdayTime;
            list.opening = value;
            this.setState({ wednesdayTime: list });

        } else if (name == "thursdayTime") {
            const list: any = this.state.thursdayTime;
            list.opening = value;
            this.setState({ thursdayTime: list });

        } else if (name == "fridayTime") {
            const list: any = this.state.fridayTime;
            list.opening = value;
            this.setState({ fridayTime: list });

        } else if (name == "saturdayTime") {
            const list: any = this.state.saturdayTime;
            list.opening = value;
            this.setState({ saturdayTime: list });

        } else if (name == "sundayTime") {
            const list: any = this.state.sundayTime;
            list.opening = value;
            this.setState({ sundayTime: list });
        }
    };

    dayClosingTime = (e: any) => {
        const { name, value } = e.target;
        if (name == "mondayTime") {
            const list: any = this.state.mondayTime;
            list.closing = value;
            this.setState({ mondayTime: list });

        } else if (name == "tuesdayTime") {
            const list: any = this.state.tuesdayTime;
            list.closing = value;
            this.setState({ tuesdayTime: list });

        } else if (name == "wednesdayTime") {
            const list: any = this.state.wednesdayTime;
            list.closing = value;
            this.setState({ wednesdayTime: list });

        } else if (name == "thursdayTime") {
            const list: any = this.state.thursdayTime;
            list.closing = value;
            this.setState({ thursdayTime: list });

        } else if (name == "fridayTime") {
            const list: any = this.state.fridayTime;
            list.closing = value;
            this.setState({ fridayTime: list });

        } else if (name == "saturdayTime") {
            const list: any = this.state.saturdayTime;
            list.closing = value;
            this.setState({ saturdayTime: list });

        } else if (name == "sundayTime") {
            const list: any = this.state.sundayTime;
            list.closing = value;
            this.setState({ sundayTime: list });
        }
    };

    specialDayOpeningTime = (e: any) => {
        const { name, value } = e.target;
        if (name == "specialmondayTime") {
            const list: any = this.state.specialMondayTime;
            list.opening = value;
            this.setState({ specialMondayTime: list });

        } else if (name == "specialtuesdayTime") {
            const list: any = this.state.specialTuesdayTime;
            list.opening = value;
            this.setState({ specialTuesdayTime: list });

        } else if (name == "specialwednesdayTime") {
            const list: any = this.state.specialWednesdayTime;
            list.opening = value;
            this.setState({ specialWednesdayTime: list });

        } else if (name == "specialthursdayTime") {
            const list: any = this.state.specialThursdayTime;
            list.opening = value;
            this.setState({ specialThursdayTime: list });

        } else if (name == "specialfridayTime") {
            const list: any = this.state.specialFridayTime;
            list.opening = value;
            this.setState({ specialFridayTime: list });

        } else if (name == "specialsaturdayTime") {
            const list: any = this.state.specialSaturdayTime;
            list.opening = value;
            this.setState({ specialSaturdayTime: list });

        } else if (name == "specialsundayTime") {
            const list: any = this.state.specialSundayTime;
            list.opening = value;
            this.setState({ specialSundayTime: list });
        }
    };

    specialDayClosingTime = (e: any) => {
        const { name, value } = e.target;
        if (name == "specialmondayTime") {
            const list: any = this.state.specialMondayTime;
            list.closing = value;
            this.setState({ specialMondayTime: list });

        } else if (name == "specialtuesdayTime") {
            const list: any = this.state.specialTuesdayTime;
            list.closing = value;
            this.setState({ specialTuesdayTime: list });

        } else if (name == "specialwednesdayTime") {
            const list: any = this.state.specialWednesdayTime;
            list.closing = value;
            this.setState({ specialWednesdayTime: list });

        } else if (name == "specialthursdayTime") {
            const list: any = this.state.specialThursdayTime;
            list.closing = value;
            this.setState({ specialThursdayTime: list });

        } else if (name == "specialfridayTime") {
            const list: any = this.state.specialFridayTime;
            list.closing = value;
            this.setState({ specialFridayTime: list });

        } else if (name == "specialsaturdayTime") {
            const list: any = this.state.specialSaturdayTime;
            list.closing = value;
            this.setState({ specialSaturdayTime: list });

        } else if (name == "specialsundayTime") {
            const list: any = this.state.specialSundayTime;
            list.closing = value;
            this.setState({ specialSundayTime: list });
        }
    };
    handleBackupStoreInputChange = (e: any, index: any) => {
        if (e && e.target) {
            const { name, value } = e.target;
            const list: any = this.state.backupStore;
            list[index][name] = value;
            this.setState({ backupStore: list });
        } else {
            const list: any = this.state.backupStore;
            if (e && e.value > 0) {
                list[index]["backUpStoreId"] = e.value;
            } else {
                list[index]["backUpStoreId"] = "";
            }
            this.setState({ backupStore: list });
        }
    };
    handleTradeAreaInputChange = (e: any, index: any, type?: any) => {
        if (e && e.target) {
            const { name, value } = e.target;
            const list: any = this.state.tradeAreas;
            list[index][name] = value;
            this.setState({ tradeAreas: list });
        } else {
            const list: any = this.state.tradeAreas;
            if (type == "backup") {
                if (e && e.value > 0) {
                    list[index]["backup_store_id"] = e.value;
                } else {
                    list[index]["backup_store_id"] = "";
                }
            } else if (type == "tradezone") {
                if (e && e.value > 0) {
                    list[index]["tradeZone"] = e.value;
                } else {
                    list[index]["tradeZone"] = "";
                }
            }
            this.setState({ tradeAreas: list });
        }
    };

    handleAddBackupStore = (i: any) => {
        const list: any = this.state.backupStore;
        if (list[i]["backUpStoreId"] == "" || list[i]["priority"] == "") {
            alert("Please fill in current item")
        } else if (i == '1' || i > 1) {
            alert("Sorry you can select only 2 Backup Stores")
        } else {
            const newlist: any = { backUpStoreId: "", priority: "", storeId: "" }
            const backupStr: any = this.state.backupStore.concat(newlist)
            this.setState({ backupStore: backupStr });
        }
    };

    handleAddTradeArea = (i: any) => {
        const list: any = this.state.tradeAreas;
        if (list[i]["tradeZone"] == "" || list[i]["tradeArea"] == "") {
            alert("Please fill in current item")
        } else {
            const newlist: any = { tradeAreaId: "", tradeZone: "", tradeArea: "", expected_delivery_time: 0, backup_store_id: "" }
            const tradeArea: any = this.state.tradeAreas.concat(newlist)
            this.setState({ tradeAreas: tradeArea });
        }
    };
    handleRemoveBackupStore = (index: any) => {
        const list = this.state.backupStore;
        list.splice(index, 1);
        this.setState({ backupStore: list });
    };
    handleRemoveTradeArea = (index: any) => {
        const list = this.state.tradeAreas;
        list.splice(index, 1);
        this.setState({ tradeAreas: list });
    };
    handleBusinessType = (e: any, index: any) => {
        if (e && e.value > 0) {
            this.setState({ businessType: e.value });
        } else {
            this.setState({ businessType: "" });
        }
    };
    handleStates = (e: any, index: any) => {
        if (e && e.value > 0) {
            this.setState({ state_id: e.value });
        } else {
            this.setState({ state_id: "" });
        }
    };
    handleCity = (e: any, index: any) => {
        if (e && e.value > 0) {
            this.setState({ city_id: e.value });
        } else {
            this.setState({ city_id: "" });
        }
    };
    handleTradeZones = (e: any, index: any) => {
        if (e && e.value > 0) {
            this.setState({ trade_zone_id: e.value, kml: e.trade_zone_shape });
        } else {
            this.setState({ trade_zone_id: "", kml: "" });
        }
    };
    handleBackupTradeZones = (e: any, index: any) => {
        if (e && e.value > 0) {
            this.setState({ backup_trade_zone_id: e.value });
        } else {
            this.setState({ backup_trade_zone_id: "" });
        }
    };
    handleCheckBoxes = (event: { target: { name: any; value: any; }; }) => {
        const { backupStoreEnable } = this.state;
        if (event.target.name == "backupStoreEnable") {
            this.setState({ backupStoreEnable: !backupStoreEnable })
            let newlist: any = [{ backUpStoreId: "", priority: "", storeId: "" }]
            let upd_obj: any = this.state.tradeAreas.map((obj: any) => {
                obj.backup_store_id = '';
                return obj;
            })
            this.setState({ backupStore: newlist, tradeAreas: upd_obj });
        }
    }
    render() {
        if (localStorage.token) {
            jwt.verify(localStorage.token, secretKey, (err: any, decoded: any) => {
                if (err) {
                    this.props.logoutUser();
                }
            });
        } else {
            return <Redirect to="/" />
        }
        const { business_list, stores, states, cities, activeKmlZone, activeZones, channels, types } = this.props;
        const { trade_zone_id, backup_trade_zone_id, pulse_ip, franchise_name, storename, brand, storeopen, state_id, city_id, storeemail, branchCode, backupStoreEnable, fp_branch_code, agg_restaurantId, expected_delivery_time, escalationemail, posno, ntn, contact1, contact2, rgmContact, storeclose, errormessage, businessType, timeByDays, mondayTime, tuesdayTime, wednesdayTime, thursdayTime, fridayTime, saturdayTime, sundayTime, backupStore, tradeAreas, cloudKitchen, storetype, saleschannel, specialDaysTiming, specialMondayTime, specialTuesdayTime, specialWednesdayTime, specialThursdayTime, specialFridayTime, specialSaturdayTime, specialSundayTime } = this.state
        return (
            <div className="page">
                <CheckChanges path="/edit-store" />
                <Topbar />
                <div className="page-content d-flex align-items-stretch">
                    <Sidebar />
                    <div className="content-inner">
                        {/* <!-- Page Header--> */}
                        <header className="page-header pb-1">
                            <div className="container-fluid">
                                <div className="d-flex align-items-center justify-content-between">
                                    <h4>Stores Management</h4>
                                </div>
                            </div>
                        </header>
                        {/*  Breadcrumb */}
                        <div className="breadcrumb-holder container-fluid">
                            <ul className="breadcrumb">
                                <li className="breadcrumb-item"><Link to="/stores" className="text-primary">Stores</Link></li>
                                <li className="breadcrumb-item active">Edit Store</li>
                            </ul>
                        </div>
                        <section className="forms">
                            <div className="container-fluid">
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card">
                                            <div className="card-body">
                                                <div className="row">
                                                    <div className="col-md-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label ">Store Name <span className="text-danger">*</span></label>
                                                            <input id="storename" type="text" name="storename" defaultValue={storename} required data-msg="Please enter Store name" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-md-6">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Brand <span className="text-danger">*</span></label>
                                                            <input id="brandname" type="text" name="brandname" defaultValue={brand} disabled data-msg="Please enter Brand name" className="input-material" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className='row'>
                                                    <div {...(businessType == "1" ? { className: "col-md-6" } : { className: "col-12" })}>
                                                        <div className="form-group">
                                                            <label className="form-control-label">Business Type <span className="text-danger">*</span></label>
                                                            <Select
                                                                name="businessType"
                                                                isClearable
                                                                options={business_list}
                                                                className="text-capitalize select mt-2"
                                                                classNamePrefix="select"
                                                                value={business_list.map((element: any) => {
                                                                    if (element.id == businessType) {
                                                                        return { label: element.name, value: businessType }
                                                                    }
                                                                })}
                                                                onChange={(e, i) => this.handleBusinessType(e, i)}
                                                            />
                                                            {/* <select name="businessType" className="form-control text-capitalize mt-2" required data-msg="Please select type" onChange={this.handleInputChange}>
                                                                {business_list && business_list.length > 0 &&
                                                                    business_list.map((business: any, index: any) => (
                                                                        <option key={index} value={business.id} {...businessType === business.id && { selected: true }}>{business.name}</option>
                                                                    ))
                                                                }
                                                            </select> */}
                                                        </div>
                                                    </div>
                                                    {businessType == "1" &&
                                                        <div className="col-md-6">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Franchise Name <span className="text-danger">*</span></label>
                                                                <input id="franchisename" type="text" name="franchise_name" defaultValue={franchise_name} data-msg="Please enter franchise name" className="input-material" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <div className="row">
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Pulse Branch Code<span className="text-danger">*</span></label>
                                                            <input id="branchCode" type="text" name="branchCode" defaultValue={branchCode} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Pulse IP <span className="text-danger">*</span></label>
                                                            <input id="pulse_ip" type="text" name="pulse_ip" defaultValue={pulse_ip} required data-msg="Please enter ip address" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Aggregator Branch Code</label>
                                                            <input id="fp_branch_code" type="text" name="fp_branch_code" defaultValue={fp_branch_code} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    {/* <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">POS Branch Code <span className="text-danger">*</span></label>
                                                            <input id="aloha_branch_code" type="text" name="aloha_branch_code" defaultValue={aloha_branch_code} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div> */}
                                                </div>
                                                <div className="row">
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Aggregator Restaurant ID</label>
                                                            <input id="agg_restaurantId" type="text" name="agg_restaurantId" className="input-material" defaultValue={agg_restaurantId} onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Email Contact <span className="text-danger">*</span></label>
                                                            <input id="storeemail" type="email" name="storeemail" defaultValue={storeemail} required data-msg="Please enter Store Email" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col-md-4">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Email Escalation</label>
                                                            <input id="escalationemail" type="email" name="escalationemail" defaultValue={escalationemail} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Contact 1</label>
                                                            <input id="contact1" type="text" name="contact1" defaultValue={contact1} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Contact 2</label>
                                                            <input id="contact2" type="text" name="contact2" defaultValue={contact2} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">RGM Contact</label>
                                                            <input id="rgmContact" type="text" name="rgmContact" defaultValue={rgmContact} className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    {/* <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Cloud Kitchen <span className="text-danger">*</span></label>
                                                            <select name="cloudKitchen" value={cloudKitchen} className="form-control text-capitalize mt-2" disabled>
                                                                <option value="Disabled">Disabled</option>
                                                                <option value="Enabled">Enabled</option>
                                                            </select>
                                                        </div>
                                                    </div> */}
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Sale Channel <span className="text-danger">*</span></label>
                                                            <select name="saleschannel" value={saleschannel}  className="form-control text-capitalize mt-2" required onChange={this.handleChannelChange}>
                                                                {channels &&
                                                                    channels.map((channel, index) => (
                                                                        <option key={index} value={channel.sales_channel_id}>{channel.channel_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Pos no</label>
                                                                <input id="posno" type="text" name="posno" value={posno} className="input-material" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">NTN Number</label>
                                                            <input id="ntn" type="text" name="ntn" className="input-material" defaultValue={ntn} onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Store Type <span className="text-danger">*</span></label>
                                                            <select name="storetype" value={storetype} className="form-control text-capitalize mt-2" required disabled>
                                                                {types &&
                                                                    types.map((type, index) => (
                                                                        <option key={index} value={type.store_type_id}>{type.store_type_name}</option>
                                                                    ))
                                                                }
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">Expected Time <span className="text-danger">* (In minutes)</span></label>
                                                            <input id="expected_delivery_time" type="number" onWheel={(e: any) => e.target.blur()} value={expected_delivery_time} name="expected_delivery_time" required data-msg="Please enter expected time" className="input-material" onChange={this.handleInputChange} />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">States <span className="text-danger">*</span></label>
                                                            <Select
                                                                name="state_id"
                                                                isClearable
                                                                options={states}
                                                                className="text-capitalize select mt-2"
                                                                classNamePrefix="select"
                                                                value={states.map((element: any) => {
                                                                    if (element.state_id == state_id) {
                                                                        return { label: element.state_name, value: state_id }
                                                                    }
                                                                })}
                                                                onChange={(e, i) => this.handleStates(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div style={{ width: '100%' }}>
                                                    <div className="row">
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label htmlFor="">Latitude</label>
                                                                <input type="text" name="newlat" className="form-control" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label htmlFor="">Longitude</label>
                                                                <input type="text" name="newlng" className="form-control" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">City <span className="text-danger">*</span></label>
                                                                <Select
                                                                    name="city_id"
                                                                    isClearable
                                                                    options={cities}
                                                                    className="text-capitalize select"
                                                                    classNamePrefix="select"
                                                                    value={cities.map((element: any) => {
                                                                        if (element.id == city_id) {
                                                                            return { label: element.name, value: city_id }
                                                                        }
                                                                    })}
                                                                    onChange={(e, i) => this.handleCity(e, i)}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <Map
                                                        data={this.state}
                                                        center={{ lat: this.state.newlat, lng: this.state.newlng }}
                                                        kml={this.state.kml}
                                                        radius={parseInt(this.state.radius)}
                                                        onSetRadius={this.handleRadius}
                                                        onSetData={this.handleMapData}
                                                        google={this.props.google}
                                                        height='400px'
                                                        zoom={13}
                                                    />
                                                </div>
                                                <div className='row'>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">KML Zones <span className="text-danger">*</span></label>
                                                            <Select
                                                                name="tradeZone"
                                                                isClearable
                                                                value={activeKmlZone.map((element: any) => {
                                                                    if (element.trade_zone_id == trade_zone_id) {
                                                                        return { label: element.trade_zone_name, value: trade_zone_id }
                                                                    }
                                                                })}
                                                                options={activeKmlZone}
                                                                className="text-capitalize select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handleTradeZones(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            <label className="form-control-label">KML Backup Zone</label>
                                                            <Select
                                                                name="tradeZone"
                                                                isClearable
                                                                value={activeKmlZone.map((element: any) => {
                                                                    if (element.trade_zone_id == backup_trade_zone_id) {
                                                                        return { label: element.trade_zone_name, value: backup_trade_zone_id }
                                                                    }
                                                                })}
                                                                options={activeKmlZone}
                                                                className="text-capitalize select mt-2"
                                                                classNamePrefix="select"
                                                                onChange={(e, i) => this.handleBackupTradeZones(e, i)}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                {
                                                    (!timeByDays && !specialDaysTiming) &&
                                                    <div className="row">
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                <input id="storeopen" type="time" name="storeopen" defaultValue={storeopen} required data-msg="Please enter opening time" className="input-material" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                        <div className="col">
                                                            <div className="form-group">
                                                                <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                <input id="storeclose" type="time" name="storeclose" defaultValue={storeclose} required data-msg="Please enter closing time" className="input-material" onChange={this.handleInputChange} />
                                                            </div>
                                                        </div>
                                                    </div>
                                                }


                                                <div className="row">
                                                    <div className="col my-2">
                                                        <div>
                                                            <input id="checkboxCustom1" type="checkbox" name="timeByDays" checked={timeByDays == true && true} onChange={this.handleTimeByDays} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom1">Add Time by Week Days</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {timeByDays &&
                                                    <div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Monday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="mondayTime" type="time" name="mondayTime" defaultValue={storeopen ? storeopen : mondayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.dayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="mondayTime" type="time" name="mondayTime" defaultValue={storeclose ? storeclose : mondayTime.closing} required className="input-material" onChange={(e) => this.dayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Tuesday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="tuesdayTime" type="time" name="tuesdayTime" defaultValue={storeopen ? storeopen : tuesdayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.dayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="tuesdayTime" type="time" name="tuesdayTime" defaultValue={storeclose ? storeclose : tuesdayTime.closing} required className="input-material" onChange={(e) => this.dayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Wednesday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="wednesdayTime" type="time" name="wednesdayTime" defaultValue={storeopen ? storeopen : wednesdayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.dayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="wednesdayTime" type="time" name="wednesdayTime" defaultValue={storeclose ? storeclose : wednesdayTime.closing} required className="input-material" onChange={(e) => this.dayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Thursday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="thursdayTime" type="time" name="thursdayTime" defaultValue={storeopen ? storeopen : thursdayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.dayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="thursdayTime" type="time" name="thursdayTime" defaultValue={storeclose ? storeclose : thursdayTime.closing} required className="input-material" onChange={(e) => this.dayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Friday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="fridayTime" type="time" name="fridayTime" defaultValue={storeopen ? storeopen : fridayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.dayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="fridayTime" type="time" name="fridayTime" defaultValue={storeclose ? storeclose : fridayTime.closing} required className="input-material" onChange={(e) => this.dayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Saturday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="saturdayTime" type="time" name="saturdayTime" defaultValue={storeopen ? storeopen : saturdayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.dayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="saturdayTime" type="time" name="saturdayTime" defaultValue={storeclose ? storeclose : saturdayTime.closing} required className="input-material" onChange={(e) => this.dayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Sunday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="sundayTime" type="time" name="sundayTime" defaultValue={storeopen ? storeopen : sundayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.dayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="sundayTime" type="time" name="sundayTime" defaultValue={storeclose ? storeclose : sundayTime.closing} required className="input-material" onChange={(e) => this.dayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                <div className='row'>
                                                    <div className="col my-2">
                                                        <div>
                                                            <input id="checkboxCustom2" type="checkbox" name="specialDaysTiming" checked={specialDaysTiming == true && true} onChange={this.handleSpecialDayTimings} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom2">Add Time for Special Days</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {specialDaysTiming &&
                                                    <div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Monday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="specialmondayTime" type="time" name="specialmondayTime" defaultValue={storeopen ? storeopen : specialMondayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.specialDayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="specialmondayTime" type="time" name="specialmondayTime" defaultValue={storeclose ? storeclose : specialMondayTime.closing} required className="input-material" onChange={(e) => this.specialDayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Tuesday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="specialtuesdayTime" type="time" name="specialtuesdayTime" defaultValue={storeopen ? storeopen : specialTuesdayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.specialDayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="specialtuesdayTime" type="time" name="specialtuesdayTime" defaultValue={storeclose ? storeclose : specialTuesdayTime.closing} required className="input-material" onChange={(e) => this.specialDayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Wednesday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="specialwednesdayTime" type="time" name="specialwednesdayTime" defaultValue={storeopen ? storeopen : specialWednesdayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.specialDayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="specialwednesdayTime" type="time" name="specialwednesdayTime" defaultValue={storeclose ? storeclose : specialWednesdayTime.closing} required className="input-material" onChange={(e) => this.specialDayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Thursday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="specialthursdayTime" type="time" name="specialthursdayTime" defaultValue={storeopen ? storeopen : specialThursdayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.specialDayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="specialthursdayTime" type="time" name="specialthursdayTime" defaultValue={storeclose ? storeclose : specialThursdayTime.closing} required className="input-material" onChange={(e) => this.specialDayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Friday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="specialfridayTime" type="time" name="specialfridayTime" defaultValue={storeopen ? storeopen : specialFridayTime.opening} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.specialDayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="specialfridayTime" type="time" name="specialfridayTime" defaultValue={storeclose ? storeclose : specialFridayTime.closing} required className="input-material" onChange={(e) => this.specialDayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Saturday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="specialsaturdayTime" type="time" name="specialsaturdayTime" defaultValue={storeclose ? storeclose : specialSaturdayTime.closing} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.specialDayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="specialsaturdayTime" type="time" name="specialsaturdayTime" defaultValue={storeclose ? storeclose : specialSaturdayTime.closing} required className="input-material" onChange={(e) => this.specialDayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="row">
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Day <span className="text-danger">*</span></label>
                                                                    <input id="day" type="text" readOnly value={'Sunday'} name="day" required data-msg="Please enter Opening Time" className="input-material" />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Opening Time <span className="text-danger">*</span></label>
                                                                    <input id="specialsundayTime" type="time" name="specialsundayTime" defaultValue={storeclose ? storeclose : specialSundayTime.closing} required data-msg="Please enter Opening Time" className="input-material" onChange={(e) => this.specialDayOpeningTime(e)} />
                                                                </div>
                                                            </div>
                                                            <div className="col">
                                                                <div className="form-group">
                                                                    <label className="form-control-label">Closing Time <span className="text-danger">*</span></label>
                                                                    <input id="specialsundayTime" type="time" name="specialsundayTime" defaultValue={storeclose ? storeclose : specialSundayTime.closing} required className="input-material" onChange={(e) => this.specialDayClosingTime(e)} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                <div className="row">
                                                    <div className='col-12 my-2'>
                                                        <div>
                                                            <input id="checkboxCustom3" type="checkbox" name="backupStoreEnable" checked={backupStoreEnable == true && true} onChange={this.handleCheckBoxes} className="checkbox-template" />
                                                            <label htmlFor="checkboxCustom3">Backup Store</label>
                                                        </div>
                                                    </div>
                                                    {!backupStoreEnable &&
                                                        <div className="col">
                                                            <div className="form-group">
                                                                {backupStore.map((x: any, i: any) => {
                                                                    return (
                                                                        <div key={i} className="row">
                                                                            <div className="col">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Backup Store</label>
                                                                                    <Select
                                                                                        name="backUpStoreId"
                                                                                        isClearable
                                                                                        options={stores.filter(o1 => !backupStore.some((o2: any) => (o1.store_id === o2.backUpStoreId || (o1.is_backup_store==null || o1.is_backup_store==0))))}
                                                                                        className="text-capitalize select mt-2"
                                                                                        classNamePrefix="select"
                                                                                        value={stores.map((element: any) => {
                                                                                            if (element.store_id == x.backUpStoreId) {
                                                                                                return { label: element.store_name, value: element.store_id }
                                                                                            }
                                                                                        })}
                                                                                        onChange={e => this.handleBackupStoreInputChange(e, i)} />
                                                                                    {/* <select name="backUpStoreId" className="form-control text-capitalize mt-2" required data-msg="Please select Channel" onChange={e => this.handleBackupStoreInputChange(e, i)}>
                                                                                    <option value="">Backup Store</option>
                                                                                    {stores.length > 0 &&
                                                                                        stores.map((store: any, index: any) => (
                                                                                            store.store_id !== store_id &&
                                                                                            // <option key={index} value={store.store_id}>{store.store_name}</option>
                                                                                            <option key={index} value={store.store_id} {...x.backUpStoreId === store.store_id && { selected: true }}>{store.store_name}</option>
                                                                                        ))
                                                                                    }
                                                                                </select> */}
                                                                                </div>
                                                                            </div>
                                                                            {/* <div className="col">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Priority</label>
                                                                                    <input
                                                                                        name="priority"
                                                                                        type="number" onWheel={(e: any) => e.target.blur()}
                                                                                        value={x.priority}
                                                                                        min={0}
                                                                                        disabled={x.backUpStoreId == ""}
                                                                                        onKeyDown={this.blockInvalidChar}
                                                                                        data-msg="Please enter qunatity"
                                                                                        className="input-material"
                                                                                        onChange={e => this.handleBackupStoreInputChange(e, i)}
                                                                                    />
                                                                                </div>
                                                                            </div> */}
                                                                            {/* <div className="col py-4">
                                                                                {backupStore.length !== 1 &&
                                                                                    <button className="btn btn-sm btn-outline-danger"
                                                                                        onClick={() => this.handleRemoveBackupStore(i)}><i className="fa fa-trash"></i></button>}
                                                                                {backupStore.length - 1 === i && <button className="btn btn-sm btn-primary ml-2" onClick={() => this.handleAddBackupStore(i)}><i className="fa fa-plus"></i></button>}
                                                                            </div> */}
                                                                        </div>
                                                                    );
                                                                })}
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <hr />
                                                <div className="row" style={{ paddingTop: '1em' }}>
                                                    <div className="col">
                                                        <div className="form-group">
                                                            {tradeAreas.map((x: any, i: any) => {
                                                                return (
                                                                    <div key={i} className="row">
                                                                        <div className="col">
                                                                            <div className="form-group">
                                                                                <label className="form-control-label">CallCenter Zones <span className="text-danger">*</span></label>
                                                                                <Select
                                                                                    name="tradeZone"
                                                                                    isClearable
                                                                                    options={activeZones}
                                                                                    className="text-capitalize select mt-2"
                                                                                    classNamePrefix="select"
                                                                                    value={activeZones.map((element: any) => {
                                                                                        if (element.id == x.tradeZone) {
                                                                                            return { label: `${element.delivery_zone_name}(${element.city})`, value: element.id }
                                                                                        }
                                                                                    })}
                                                                                    onChange={e => this.handleTradeAreaInputChange(e, i, "tradezone")} />
                                                                            </div>
                                                                        </div>
                                                                        <div className="col">
                                                                            <div className="form-group">
                                                                                <label className="form-control-label">TradeArea name <span className="text-danger">*</span></label>
                                                                                <input id="tradeArea" type="text" name="tradeArea" value={x.tradeArea} required data-msg="Please enter TradeAreaName" className="input-material" onChange={e => this.handleTradeAreaInputChange(e, i)} />
                                                                            </div>
                                                                        </div>
                                                                        <div className="col">
                                                                            <div className="form-group">
                                                                                <label className="form-control-label">Expected Time <span className="text-danger" style={{ fontSize: 10 }}>(In minutes)</span></label>
                                                                                <input id="expected_delivery_time" type="number" onWheel={(e: any) => e.target.blur()} name="expected_delivery_time" value={x.expected_delivery_time} required data-msg="Please enter expected time" className="input-material" onChange={e => this.handleTradeAreaInputChange(e, i)} />
                                                                            </div>
                                                                        </div>
                                                                        {!backupStoreEnable &&
                                                                            <div className="col">
                                                                                <div className="form-group">
                                                                                    <label className="form-control-label">Backup Store</label>
                                                                                    <Select
                                                                                        name="backup_store_id"
                                                                                        isClearable
                                                                                        options={stores.filter(o1 => !tradeAreas.some((o2: any) => (o1.store_id === o2.backUpStoreId || (o1.is_backup_store==null || o1.is_backup_store==0))))}
                                                                                        className="text-capitalize select mt-2"
                                                                                        classNamePrefix="select"
                                                                                        value={stores.map((element: any) => {
                                                                                            if (element.store_id == x.backup_store_id) {
                                                                                                return { label: element.store_name, value: element.store_id }
                                                                                            }
                                                                                        })}
                                                                                        onChange={e => this.handleTradeAreaInputChange(e, i, "backup")} />
                                                                                </div>
                                                                            </div>
                                                                        }
                                                                        <div className="col py-4">
                                                                            {tradeAreas.length !== 1 &&
                                                                                <button className="btn btn-sm btn-outline-danger"
                                                                                    onClick={() => this.handleRemoveTradeArea(i)}><i className="fa fa-trash"></i></button>}
                                                                            {tradeAreas.length - 1 === i && <button className="btn btn-sm btn-primary ml-2" onClick={() => this.handleAddTradeArea(i)}><i className="fa fa-plus"></i></button>}
                                                                        </div>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="form-group float-right mt-3">
                                                    <button onClick={() => { this.props.history.push("/stores") }} className="btn btn-danger mr-2">Cancel</button>
                                                    <button onClick={this.handleSubmit} disabled={!this.isStoreReady()} className="btn btn-primary">Update Store</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <Footer />
                    </div>

                </div>
            </div >
        );
    }
};
const mapStateToProps = (state: any) => {
    return {
        storeData: state.store.storeData,
        countries: state.store.countries,
        channels: state.store.channels,
        states: state.setting.states,
        types: state.store.types,
        business_list: state.store.businessType,
        backupStores: state.store.getBackupStores,
        tradeAreas: state.store.getTradeAreas,
        stores: state.store.data,
        activeZones: state.tradezone.activeZones,
        activeKmlZone: state.tradezone.activeKmlZone,
        cities: state.city.cities
    }
}
const mapDispatchToProps = (dispatch: any) => {
    return {
        editStore: function (id: any, data: any) {
            dispatch(editStore(id, data));
        },
        addStates: function (data: any, type: any) {
            dispatch(addStates(data, type));
        },
        getStore: function (id: number) {
            dispatch(getStore(id));
        },
        countryList: function () {
            dispatch(countryList())
        },
        citiesList: () => {
            dispatch(cityList())
        },
        statesList: function () {
            dispatch(statesList("store"))
        },
        businessTypeList: function () {
            dispatch(businessTypeList())
        },
        getBackupStores: function (id: number) {
            dispatch(getBackupStores(id))
        },
        storesList: function () {
            dispatch(storesList())
        },
        activekmlZoneList: () => { dispatch(activeKmlZoneList()) },
        activeZoneList: () => { dispatch(activeZoneList()) },
        getTradeAreas: function (id: number) {
            dispatch(getTradeAreas(id))
        },
        channelsList: function () {
            dispatch(channelList())
        },
        storeTypeList: function () {
            dispatch(storeTypeList())
        },
    }
}
export default connect(mapStateToProps, mapDispatchToProps)(EditStore);