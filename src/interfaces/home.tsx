export interface HomeProps {
    counter: any,
    paymentData: any[],
    orderData: any[],
    leaderBoard: any[],
    topItems: any[],
    lineChart: any,
    pieChartSos: any,
    dougnutChartChannel: any,
    counter_pos: any,
    DougnutChartChannel: () => {},
    PieChartSOS: () => {},
    LineChartData: () => {},
    homeCounter: (days:any) => {},
    monthlyOrdersForLine: (days:any) => {},
    monthlySalesForBar: () => {},
    recentPayments: (days:any) => {},
    recentOrders: (days:any) => {},
    leaderBoardForGraph: (days:any) => {},
    TopItemsList: () => {},
    logoutAdmin: () => {},
    homeCounterPOS: (days:any) => {},
}