export interface PartnerProps {
    partnerList: () => {},
    logoutUser: () => {}
}
export interface AddPartnerState {
    [x: number]: any,
    name: any,
    email_address: any,
    phone_number: any,
    company: any,
    vip: any
}
export interface AddPartnerProps {
    addPartner: (data: any) => {},
    logoutUser: () => {}
}
export interface EditPartnerState {
    [x: number]: any,
    name: any,
    email_address: any,
    phone_number: any,
    company: any,
    vip: any
}
export interface EditPartnerProps {
    partnerData: any,
    match:any,
    editPartner: (id: any, data: any) => {},
    getPartner: (id: any) => {}
    logoutUser: () => {}
}