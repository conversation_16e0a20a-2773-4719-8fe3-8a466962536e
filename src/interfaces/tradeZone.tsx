export interface TradeZoneProps {
    data: any[],
    tradeZoneList: () => {},
    logoutUser: () => {}
}
export interface AddTradeZoneProps {
    tradezonename: string,
    city:any,
    cities: any[],
    handleZoneInput: (data: any) => {},
    citiesList: () => {}
    addTradeZone: (data: any) => {},
    logoutUser: () => {}
}
export interface EditTradeZoneProps {
    tradezonename: any,
    city:any,
    cities: any[],
    match: any,
    handleZoneInput: (data: any) => {},
    editTradeZone: (id: any, data: any) => {},
    getTradeZone: (id: any) => {}
    citiesList: () => {}
    logoutUser: () => {}
}

export interface KMLZoneProps {
    data: any[],
    tradeZoneList: () => {},
    logoutUser: () => {}
}
export interface AddKMLZoneProps {
    tradezonename: string,
    google:any
    handleZoneInput: (data: any) => {},
    addTradeZone: (data: any) => {},
    logoutUser: () => {}
}
export interface EditKMLZoneProps {
    tradezonename: any,
    match: any,
    google:any,
    handleZoneInput: (data: any) => {},
    editTradeZone: (id: any, data: any) => {},
    getTradeZone: (id: any) => {}
    logoutUser: () => {}
}

export interface TradeAreaProps {
    data: any[],
    tradeZoneList: () => {},
    logoutUser: () => {}
}
export interface AddTradeAreaProps {
    areaname: string,
    tradezone: any,
    tradezones: any[],
    store: any,
    stores: any[],
    handleTradeAreaInput: (data: any) => {},
    tradeZoneList: () => {}
    addTradeArea: (data: any) => {},
    logoutUser: () => {},
    storesList: () => {}
}
export interface EditTradeAreaProps {
    areaname: string,
    tradezone: any,
    tradezones: any[],
    match: any,
    store: any,
    stores: any[],
    handleTradeAreaInput: (data: any) => {},
    editTradeArea: (id: any, data: any) => {},
    getTradeArea: (id: any) => {}
    tradeZoneList: () => {}
    logoutUser: () => {},
    storesList: () => {}
}