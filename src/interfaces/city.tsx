export interface CityZoneProps {
    data: any[],
    CityZoneList: () => {},
    logoutUser: () => {}
}
export interface AddCityZoneProps {
    cityname: string,
    countryid:any,
    cities: any[],
    message: string,
    isInserted: any,
    countries: any,
    handleCityInput:(data:any)=>{},
    addCity: (data: any) => {},
    logoutUser: () => {}
    countryList: () => {}
}
export interface EditCityZoneProps {
    cityname: string,
    countryid:any,
    cities: any[],
    message: string,
    isUpdated: any,
    countries: any,
    match:any,
    cityObj:any
    handleCityInput:(data:any)=>{},
    editCity: (id: any, data: any) => {},
    getCity: (id: any,setCityState:any) => {}
    
    logoutUser: () => {}
    countryList: () => {}
}


