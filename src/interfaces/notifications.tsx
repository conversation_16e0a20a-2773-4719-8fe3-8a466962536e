
export interface AddPushProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,
    addNotification: (data: any) => {},logoutUser: () => {}
}
export interface AddPushState {
    [x: number]: any,
    title: string,
    description: any,
    expiryDate:any,
    error:any,
    is_schedule:any,
    scheduleDate:any
}
export interface EditPushProps {
     match: any,
    notificationData: any,
    editNotification: (id: any, data: any) => {},
    logoutUser: () => {},
    getNotification: (id: any) => {},
}
export interface EditPushState {
    [x: number]: any,
    title: string,
    description: any,
    expiryDate:any,
    error:any,
    is_schedule:any,
    scheduleDate:any
}