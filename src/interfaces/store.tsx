
export interface StoreProps {
    data: any[],
    itemStatusReasons: any[],
    storesList: () => {},
    statusChangeReasons: () => {},
    unresolvedordersList: (days:any) => {},
    blockunblockstore: (id: any, is_active: any,data:any) => {},
    logoutAdmin: () => {}
}
export interface AddStoreProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,
    center: any, google: any, zoom: any, height: any,history:any,
    brands: any[], states: any[],cities:any[],activeZones:any[],activeKmlZone:any[], channels: any[], types: any[], areas: any[], countries: any[], businessType: any[],
    stores: any[], addStore: (data: any) => {}, addStates: (data: any, type: any) => {}, logoutUser: () => {},
    brandsList: () => {}, channelsList: () => {}, storeTypeList: () => {}, areasList: () => {}, countryList: () => {},
    statesList: () => {}, businessTypeList: () => {}, storesList: () => {},activeZoneList:()=>{},activekmlZoneList:()=>{},citiesList:()=>{}
}
export interface AddStoreState {
    [x: number]: any,
    storename: string,
    storeemail: any,
    pulse_ip:any,
    escalationemail: any,
    contact1: any,
    contact2: any,
    rgmContact: any,
    posno: any,
    brand: any,
    ntn?: any,
    saleschannel: string,
    storetype: string,
    area: string,
    branchCode: string,
    fp_branch_code: any,
    agg_restaurantId:any,
    aloha_branch_code:any,
    backup_trade_zone_id: any,
    trade_zone_id: any,
    backupStoreEnable: any,
    state: any,
    state_id: any,
    city_id: "",
    country_id:any,
    specialDaysTiming:any,
    city: any,
    country: any,
    country_code: any,
    address: any,
    storeopen: any,
    storeclose: any,
    expected_delivery_time: number,
    kml?: any,
    newlat: any
    newlng: any,
    lat: any,
    lng: any,
    radius: any,
    cloudKitchen?: any,
    backupStore?: any,
    tradeAreas?:any,
    businessType?: any,
    franchise_name?:any
    timeByDays?: boolean,
    mondayTime?: any
    tuesdayTime?: any
    wednesdayTime?: any
    thursdayTime?: any
    fridayTime?: any
    saturdayTime?: any
    sundayTime?: any,
    stores?: any,
    specialMondayTime: any,
    specialTuesdayTime: any,
    specialWednesdayTime: any,
    specialThursdayTime: any,
    specialFridayTime: any,
    specialSaturdayTime: any,
    specialSundayTime: any,
}
export interface EditStoreProps {
    storeData: any,
    countries: any[],
    match: any,
    history:any,
    google: any,
    states: any[],
    cities:any[]
    activeZones:any[],
    activeKmlZone:any[],
    channels: any[], types: any[],
    business_list: any,
    stores: any[],
    backupStores: any,
    getStore: (id: any) => {},
    addStates: (data: any, type: any) => {},
    editStore: (id: any, data: any) => {},
    logoutUser: () => {},
    countryList: () => {},
    citiesList:()=>{}
    statesList: () => {},
    businessTypeList: () => {},
    storesList: () => {}
    getBackupStores: (id: any) => {}
    getTradeAreas: (id:any)=>{}
    activekmlZoneList:()=>{}
    activeZoneList:()=>{}
    channelsList: () => {}
    storeTypeList: () => {}
}
export interface EditStoreState {
    [x: number]: any,
    store_id?:any,
    kml?: any,
    storename: string,
    pulse_ip:any,
    storeemail: any,
    escalationemail: any,
    fp_branch_code: any,
    agg_restaurantId:any,
    aloha_branch_code:any,
    contact1: any,
    contact2: any,
    rgmContact: any,
    posno: any,
    ntn?: any,
    brand: string,
    state: any,
    state_id: any,
    city_id:any,
    country_id:any,
    branchCode: any,
    newlat: any,
    newlng: any,
    city: string,
    country: string,
    country_code: any,
    address: string,
    storeopen: any,
    storeclose: any,
    lat: any,
    lng: any,
    radius: any
    errormessage: any,
    expected_delivery_time: number,
    businessType?: any,
    franchise_name?:any;
    timeByDays?: any,
    specialDaysTiming?:any,
    mondayTime?: any
    tuesdayTime?: any
    wednesdayTime?: any
    thursdayTime?: any
    fridayTime?: any
    saturdayTime?: any
    sundayTime?: any,
    specialMondayTime: any,
    specialTuesdayTime: any,
    specialWednesdayTime: any,
    specialThursdayTime: any,
    specialFridayTime: any,
    specialSaturdayTime: any,
    specialSundayTime: any,
    backupStoreEnable:any
    backupStore?: any
    tradeAreas?:any
    cloudKitchen?: any
    saleschannel?: any,
    storetype?: any,
    trade_zone_id?:any,
    backup_trade_zone_id:any
}