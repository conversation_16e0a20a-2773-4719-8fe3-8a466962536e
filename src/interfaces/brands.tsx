export interface BrandProps {
    brands: any[],
    brandsList: () => {},
    logoutUser: () => {}
}
export interface AddBrandProps {
    brandname: string,
    branddetails: string,
    brandtype: string
    message: string,
    isInserted: any,
    handleBrandInput:(data:any)=>{},
    addBrand: (data: any) => {},
    logoutUser: () => {}
}
export interface EditBrandProps {
    brandname: string,
    branddetails: string,
    brandtype: string,
    message: string,
    isUpdated: any,
    brandData: any,
    match: any,
    normalFraudAttempts:any,
    onlineFraudAttempts:any,
    isAndriodLive:any,
    isIOSLive:any,
    isCashDisable:any,
    isCardDisable:any,
    isJazzCashDisable:any,
    handleBrandInput:(data:any)=>{},
    editBrand: (id: any, data: any) => {},
    getBrand: (id: any) => {}
    logoutUser: () => {}
}