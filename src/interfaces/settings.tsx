export interface SettingsProps {
    taxData: any,
    discounts: any[],
    states: any[],
    message: string,
    isSaved: any,
    is_app_live: any,
    is_ios_live: any,
    is_google_login: any,
    is_fb_login: any,
    is_apple_login: any,
    is_delete_button: any,
    is_cash_disable: any,
    is_card_disable: any,
    is_jazz_disable: any,
    is_dds_map_address:any,
    logoutUser: () => {},
    discountsList: () => {},
    saveTaxByState: (data: any) => {},
    saveDeliveryCharge: (data: any) => {},
    savePOSFee:(data:any)=>{},
    statesList: () => {}
}
export interface SettingsState {
    [x: number]: any,
    tax_value: any;
    country: any,
    deliveryCountry: any,
    posFeeCountry:any,
    state: any,
    deliveryState: any,
    posFeeState:any,
    delivery_fee: any,
    pos_service_fee:any,
    deliveryStore:any,
    deliveryChecks: any,
    deliveryStartTime: any,
    deliveryEndTime: any,
    movChannel: any,
    movAmount: any
}
export interface UserProps {
    users: any[],
    usersList: () => {},
    logoutUser: () => {}
}
export interface AddUserProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,
    roles: any[],
    countries: any[],
    stores: any[],
    userGroups: any[],
    logoutUser: () => {},
    rolesList: () => {},
    storesList: () => {},
    countryList: () => {},
    userGroupsList: () => {},
    addUser: (data: any) => {}
}
export interface AddUserState {
    [x: number]: any,
    firstname: string,
    lastname: string,
    email: string,
    password: string,
    city: string,
    country: string,
    address: string,
    roleId: any,
    storeId: any,
    groupId: any,
    isValidEmail: any
}
export interface EditUserProps {
    userData: any,
    match: any,
    roles: any[],
    countries: any[],
    stores: any[],
    userGroups: any[],
    rolesList: () => {},
    countryList: () => {},
    storesList: () => {},
    editUser: (id: any, data: any) => {},
    getUser: (id: any) => {}
    userGroupsList: () => {},
    logoutUser: () => {}
}
export interface EditUserState {
    [x: number]: any,
    firstname: string,
    lastname: string,
    email: string,
    city: string,
    country: string,
    address: string,
    roleId: any,
    storeId: any,
    groupId: any,
    isValidEmail: any
}
export interface UserGroupProps {
    userGroups: any[],
    userGroupsList: () => {},
    logoutUser: () => {}
}
export interface AddUserGroupProps {
    stores: any[],
    logoutUser: () => {},
    storesList: () => {},
    addUserGroup: (data: any) => {}
}
export interface AddUserGroupState {
    [x: number]: any,
    name: string,
    description: string,
    storesJson: any[]
}
export interface EditUserGroupProps {
    userGroupData: any,
    match: any,
    stores: any[],
    storesList: () => {},
    editUserGroup: (id: any, data: any) => {},
    getUserGroup: (id: any) => {}
    logoutUser: () => {}
}
export interface EditUserGroupState {
    [x: number]: any,
    name: string,
    description: string,
    storesJson: any[]
}
export interface RolesProps {
    roles: any[],
    rolesList: () => {},
    logoutUser: () => {},
    delRole: (id: any) => {}
}
export interface AddRoleProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,
    message: string,
    isInserted: any,
    addRole: (data: any) => {},
    logoutUser: () => {}
}
export interface AddRoleState {
    [x: number]: any,
    rolename: string,
    type: any
}
export interface EditRoleProps {
    message: string,
    isUpdated: any,
    roleData: any,
    match: any,
    editRole: (id: any, data: any) => {},
    getRole: (id: any) => {}
    logoutUser: () => {}
}
export interface EditRoleState {
    [x: number]: any,
    rolename: string,
    type: any
}
export interface StatesProps {
    states: any[],
    delete: (id: any) => {},
    statesList: () => {},
    logoutUser: () => {}
}
export interface AddStateProps {
    message: string,
    isInserted: any,
    countries: any[],
    countryList: () => {},
    logoutUser: () => {},
    addStates: (data: any, type: any) => {}
}
export interface AddStates {
    [x: number]: any,
    statename: any,
    country: any,
    tax_type: any,
    payment: any,
    timezone: any,
    // tax: any,
}
export interface EditStateProps {
    message: string,
    isUpdated: any,
    countries: any[],
    match: any,
    stateData: any,
    countryList: () => {},
    getState: (id: any) => {},
    logoutUser: () => {},
    editStates: (id: any, data: any) => {}
}
export interface EditStates {
    [x: number]: any,
    statename: any,
    country: any,
    tax_type: any,
    payment: any,
    timezone: any
}

export interface ReasonsProps {
    reasons: any[],
    reasonsList: () => {},
    logoutUser: () => {},
    delReason: (id: any) => {}
    blockUnblockReason: (id: any, is_active: any) => {}
}

export interface AddReasonProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,
    addReason: (data: any) => {},
    logoutUser: () => {}
}
export interface AddReasonState {
    [x: number]: any,
    reason: string,
    type: any
}
export interface EditReasonProps {
    reasonData: any,
    match: any,
    editReason: (id: any, data: any) => {},
    getReason: (id: any) => {}
    logoutUser: () => {}
}
export interface EditReasonState {
    [x: number]: any,
    reason: string,
    type: any
}
