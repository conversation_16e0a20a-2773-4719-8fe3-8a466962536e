export interface bulkState {
    rows: any, selectFile: any, customerArr: any[],
    employeesArr?: any[],partners: any[]
    isSelected: any,
    isLoad: any,
    file: any,
    [x: number]: any
}

export interface bulkProps {
    CustomerBulkUpload: () => {},
    AddressBulkUpload: () => {},
    disabled: any,
    isLoad: any,
    dupData: any,
    options: any,
    active: any
}

export interface AddressbulkState {
    rows: any, selectFile: any, customerArr: any[],
    isSelected: any,
    isLoad: any,
    file: any,
    [x: number]: any
}

export interface AddressbulkProps {
    CustomerBulkUpload: () => {},
    AddressBulkUpload: () => {},
    disabled: any,
    isLoad: any,
    dupData: any,
    options: any,
    active: any
}