
export interface OrderProps {
    orders: any[],
    filteredOrders: any[],
    emptyFilteredOrder: any,
    Items: any[],
    states: any[],
    stores: any[],
    buttonDisable: any,
    applyLoaderonDate:any,
    verifyCount: any,
    orderXML: any,
    unverifyCount: any,
    ordersList: (days: any,datetime?:any) => {},
    showorderItems: (id: any, orders: any) => {},
    getOrderXML: (id: any) => {},
    statesList: () => {},
    logoutAdmin: () => {}
}
export interface OutBoundContactsProps {
    data: any[]
    outboundContacts: () => {}
    updateContactStatus: (id: any, order: any, history: any) => {}
}

export interface FoodpandaOrderProps {
    orders: any[],
    reasonsList: any[],
    row:any
    statusList: any[],
    cancelReasons:any[],
    transferReasons:any[],
    ordersList: (obj:any,datetime?:any) => {},
    pageNumber:any
    orderXML:any
    pageSize:any
    selectedRow:any,
    selectedFoodPanda:any,
    aggregatorOrdersList: (obj:any,datetime?:any,page?:any) => {},
    searchOrdersByQuery: (obj:any,data:any) => {},
    filterOrdersList: (filteredArr: any,updateLoader?: any,closeTimer?:any,startTimer?:any) => {},
    complaintReasonsList: () => {},
    cancelOrdersReasons: () => {},
    orderTransferReasons: ()=>{},
    updateOrder: (id: any, order: any, history: any) => {},
    logoutUser:()=>{},
    orderStatusList: () => {},
    searchOrder:(orderId:any)=>{},
    searchOrderByfoodPandaId:(orderId:any)=>{},
    getOrderXML:(orderId:any)=>{},
    failedIntegOrderCount:any
    stores: any[],
    agentStats: any,
    orderDetail:any,
    getAgentStats: (days:any) => {},
    data: any
    allStores: () => {}
    // drivers: any[],
    // customer: any,
    // orderdata: any,
    // cart: any[],
    // firstname: any,
    // lastname: any,
    // loginname: any,
    // delivery_address:any,
    // email: any,
    // phone: any,
    // taxdata: any,
    // selectedStore: any,
    // isValidEmail:any,
    // customerDetail: (data: any) => {},
    // addCustomer: (data: any) => {},
    // handleAddressInput:(address:any)=>{},
    // handleCustomerInput: (event: any) => {},
    // validateEmail:(event:any)=>{},
    // orderDetail: (orderId: any) => {},
    getCart: () => {},
    saveCart: (cart: any) => {},
    // getStoreById: (store_id: any) => {},
    // getTaxValue: (state_id: any) => {},
    storesList: () => {},
    showorderItems: (id: any, orders: any) => {},
    // statesList: () => {},

    // showorderItems: (id: any) => {},
    // orderStatusList: () => {},
    // deliveryBoysList: () => {},
    // updateOrder: (id: any, order: any) => {},
    // logoutAdmin: () => {},
}

export interface FoodpandaOrderState {
    days: any,
    startDate:any
    endDate:any
    ordersArray:any
    foodPandaId:any
    validTimeFlag:any,
    orderId: any
    stores: any
    searchQuery:any
    debounceTimeout:any
    store_id: any
    storeLabel:any
    storeValue:any
    listType: any,
    loader: any
    searchType:any
    [x: number]: any
    refresh:any
}
