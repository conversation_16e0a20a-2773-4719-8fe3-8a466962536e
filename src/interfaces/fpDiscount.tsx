export interface AddFPDiscountProps {
    stores :any, menus:any, groups: any,items:any[],
    history:any,
    addDiscount: (data: any,history:any) => {}, itemsListForMultiSelect: () => {}, menusList : () => {},
    groupsList : () => {}, logoutUser: () => {},storesList:()=>{}
}
export interface AddFPDiscountState {
    [x: number]: any,
    fp_pos_code:any,
    fp_discount_name:any,

}

export interface EditFPDiscountProps {
    match: any,
    fpdiscountData: any,
    editDiscount: (id: any, data: any) => {},
    logoutUser: () => {}
    getDiscount: (id: any) => {},
}
export interface EditFPDiscountState {
    [x: number]: any,
    fp_pos_code:any,
    fp_discount_name:any,
}
export interface FPDiscountsProps {
    data: any,
    fpDiscountsList: () => {},
    logoutUser: () => {}
}