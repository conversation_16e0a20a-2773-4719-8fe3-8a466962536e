
export interface CustomerProps {
    data: any[],
    ordersdata: any[],
    customersList: (page: any) => {},
    searchCustomer:(data:any)=>{},
    feedbackList: () => {},
    deleteFeedback: (id: any) => {},
    orderHistoryByCusId: (id: any) => {},
    favoritesList: (id: any) => {},
    blockunblock: (id: any,phone:any, is_active: any) => {},
    logoutAdmin: () => {},
    CustomerBulkUpload: () => {},
    currentPage: any,
    pageCount: any,
    numOfRows: any
}
export interface EditCustomerProps {
    message: string,
    isUpdated: any,
    customer: any,
    match: any,
    getCustomer: (id: any) => {}
    editCustomer: (id: any, data: any) => {},
    logoutUser: () => {}
}
export interface EditCustomerState {
    [x: number]: any,
    firstname: string,
    lastname: string,
    email: string,
    phone: any,
    addresses: any[]
}
export interface NewsLetterEmailProps {
    data: any[],
    emailList: () => {}
}

export interface CustomerComplaintsProps {
    data: any[],
    getComplaints: () => {}
}
export interface FranchiseProps{
    data:any[],
    franchiseList:()=>{}
}
export interface AddComplaintProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,
    message: string,
    isInserted: any,
    stores: any[],
    storesList: () => {},
    addUser: (data: any) => {}
}
export interface AddComplaintStates {
    [x: number]: any,
    fullname: string,
    phone: any,
    email: string,
    dateOfOrder: any,
    receiptnumber: any,
    feedbackType: string,
    storeId: any,
    feedbackMessage: string,
    isValidEmail: any
}

export interface CustomerNPSProps {
    data: any[],
    getNPSFeedback: () => {}
}