export interface QuestionProps {
    data: any[],
    blockunblockQuestion: (id: any, is_active: any, question: any) => {}
    questionsList: () => {}
  }
  
  export interface AddQuestionsProps {
    history: any;
    logoutUser: () => {}
    addQuestion: (data: any) => {}
  }
  
  export interface AddQuestionState {
    [x: number]: any;
    question: any
    first_rating_label: string,
    last_rating_label: string,
    options:any[]
  }
  
  export interface EditQuestionProps {
    history: any;
    match: any;
    questionData: any
    logoutUser: () => {}
    getQuestion: (id: any) => {}
    editQuestion: (id: any, data: any) => {}
  }
  
  export interface EditQuestionState {
    [x: number]: any;
    question: any,
    first_rating_label: string,
    last_rating_label: string,
    options:any[]
    reRenderSeed: number;
  }