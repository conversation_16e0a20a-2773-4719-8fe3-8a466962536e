export interface PromoProps {
    data: any[],
    promolist: () => {},
    deletePromo: (id: any,history:any) => {}
    logoutUser: () => {},
}
export interface AddPromoProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,
    isInserted: any, message: any,menus:any[], addPromo: (data: any) => {},menusList: () => {},logoutUser: () => {}
}
export interface AddPromoState {
    [x: number]: any,
    promoname: string,
    promodesc: any,
    promodiscount:any,
    expiryDate:any,
    menu:any,
    image:any,
    error:any
}
export interface EditPromoProps {
    isUpdated: any, message: any, match: any,
    promoData: any,
    menus:any[],
    editPromo: (id: any, data: any) => {},
    menusList: () => {}
    logoutUser: () => {},
    getPromo: (id: any) => {},
}
export interface EditPromoState {
    [x: number]: any,
    promoname: string,
    promodesc: any,
    promodiscount:any,
    expiryDate:any,
    menu: any,
    image:any,
    error:any
}