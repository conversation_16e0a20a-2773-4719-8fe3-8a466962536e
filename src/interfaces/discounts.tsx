export interface AddDiscountProps {
    stores :any, menus:any, groups: any,items:any[],variants: any[],
    history:any,
    addDiscount: (data: any,history:any) => {}, itemsListForMultiSelect: () => {}, menusList : () => {},
    groupsList : () => {}, logoutUser: () => {},storesList:()=>{},variantsList:()=>{}
}
export interface AddDiscountState {
    [x: number]: any,
    discountvalue: any,
    expiryDate: any,
    pos_code:any,
    percent: any,
    mode: any[],
    channel: any,
    type: any,
    multiJson:any[]
    discountType: any,
    type_id: any,
    is_lsm:any,
    multiStoreJson:any[],
    for_specific_variations: any
    variant_id: any[]
}

export interface EditDiscountProps {
    match: any,
    discountData: any,
    stores :any, menus:any, groups: any,
    subgroups: any[], items:any[],
    variants: any[]
    editDiscount: (id: any, data: any) => {},
    itemsListForMultiSelect: () => {}, 
    menusList : () => {},
    groupsList : () => {},
    subgroupsList : () => {}
    logoutUser: () => {}
    storesList:()=>{}
    variantsList:()=>{}
    getDiscount: (id: any) => {},
}
export interface EditDiscountState {
    [x: number]: any,
    discountvalue: any,
    expiryDate: any,
    percent: any,
    mode: any[],
    pos_code:any
    channel: any,
    type: any,
    multiJson:any[],
    type_id: any
    discountType: any,
    is_lsm:any,
    multiStoreJson:any[]
    for_specific_variations: any
    variant_id: any[]
    is_crust: any,
    is_addon: any,
    is_toping: any,
    is_flavour: any,
}