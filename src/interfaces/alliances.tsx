export interface AlliancesProps {
    allianceList: () => {},
    logoutUser: () => {}
}
export interface AddAllianceState {
    [x: number]: any,
    name: any,
    image: any,
    html_description:any,
    editorState:any
}
export interface AddAllianceProps {
    addAlliance: (data: any) => {},
    logoutUser: () => {}
}
export interface EditAllianceState {
    [x: number]: any,
    name: any,
    image: any,
    html_description:any,
    editorState:any
}
export interface EditAllianceProps {
    allianceData: any,
    match:any,
    editAlliance: (id: any, data: any) => {},
    getAlliance: (id: any) => {}
    logoutUser: () => {}
}