export interface EmpProps {
    employeeList: (pageNumber: number, pageLimit: number) => {},
    logoutUser: () => {}
}
export interface AddEmpState {
    [x: number]: any,
    name: any,
    email_address: any,
    phone_number: any,
    limit: any,
    vip: any,
    empType: any,
    voucher_codes: any[]
}
export interface AddEmpProps {
    coupons: any[]
    empTypes: any[]
    addEmp: (data: any) => {},
    empCoupons: () => {}
    empTypeList: () => {}
    logoutUser: () => {}
}
export interface EditEmpState {
    [x: number]: any,
    name: any,
    email_address: any,
    phone_number: any,
    company: any,
    vip: any,
    limit:any
    empType: any,
    voucher_codes: any[]
}
export interface EditEmpProps {
    empData: any,
    match: any,
    coupons: any[]
    empTypes: any[]
    empCoupons: () => {}
    empTypeList: () => {}
    editEmp: (id: any, data: any) => {},
    getEmp: (id: any) => {}
    logoutUser: () => {}
}