export interface ReportsProps {
    msg: any,
    isLoad:any,
    logoutUser: () => {},
    dailySalesReport: (data: any) => {},
    customersReport: (data: any) => {},
    pmixReport: (data: any) => {},
    salesmixReport: (data: any) => {},
    channelmixReport: (data: any) => {},
    deviceinfoReports:(data:any)=>{},
    couponredemptionReport: (data: any) => {},
    profitReport: (data: any) => {},
    discountReport: (data: any) => {},
    sosReport: (data: any) => {},
    ridersReport: (data: any) => {},
    storesReport: (data: any) => {},
    cancellationReport: (data: any) => {},
    cancellationDtetailsReport: (data: any) => {},
    orderDetailsReport: (data: any) => {},
    riderDetailsReport: (data: any) => {},
    activityLogsReport: (data: any) => {},
    menuLogsReport: (data: any) => {},
    npsLogsReport: (data: any) => {},
    paymentLogsReport:(data:any)=>{}
    storesList: () => {},
    orderItemDetailsReport: (data: any) => {},
    voucherOrderDetailsReport:(data:any)=>{}
    aggrFailedOrdersReport:(data:any)=>{}
    aggrDataExportReport:()=>{}
    activity_logs: any,
    menu_logs: any,
    nps_logs: any,
    currentPage: any,
    pageCount: any,
    numOfRows: any,
    channelmixReports: any[],
    deviceInfoReports:any[]
    rider_details_report: any[],
    couponredemptionReports: any[],
    order_details_report: any[],
    voucher_logs_report:any
    smixReports: any[],
    cancel_smryReports: any[],
    cancel_detail_report: any[],
    dailyReports: any[],
    customerReports: any[],
    pmixReports: any[],
    profitReports: any[],
    discountReports: any[],
    sosReports: any[],
    payment_logs:any[]
    ridersReports: any[],
    storesReports: any[],
    stores: any,
    orderSystemIntgReport: (data: any) => {},
    system_intrgrated_orders: any,
    order_item_details_report:any[]
    aggregator_failed_orders:any[]
    aggr_data_export:any[]
    menuItems_extract:any
    combos_extract:any
    total_data_extract:any
}
export interface ReportsState {
    reportType: any, storeType: any, startDate: any,
    //storesJson: any[],
    endDate: any, [x: number]: any, showDate?: any,
    validTimeFlag: any,
    validTimeFlagFrom:any,
    validTimeFlagTo: any,
    allSales:any
    settings: any

}