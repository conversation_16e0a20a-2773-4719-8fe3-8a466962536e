export interface LoginState {
    [x: number]: any,
    email: string;
    password: any;
    lat:any;
    lng:any
}
export interface LoginProps {
    loginUser: (email: any, password: any,lat:any,lng:any) => {}
}
export interface ForgotPassState {
    [x: number]: any,
    email: string;
}
export interface ForgotPassProps {
    message: string,
    isReset: any,
    forgotPassword: (email: any) => {}
}
export interface EditProfileProps {
    message: string,
    isUpdated: any,
    userData: any,
    countries:any[],
    getProfile: () => {},
    countryList: () => {}
    changePassword: (newPass: any) => {}
    editProfile: (firstname: any, lastname: any, email: any, city: any, country: any, address: any) => {},
    logoutUser: () => {}
}
export interface EditProfileState {
    [x: number]: any,
    firstname: string,
    lastname: string,
    email: string,
    city: string,
    country: number,
    address: string,
    changePassMsg: string,
    newPass: string,
    confirmPass: string
}