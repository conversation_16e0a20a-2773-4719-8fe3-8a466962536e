export interface DriverProps {

}

export interface DriverState {

}

export interface AddDriverProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,
    message: string,
    isInserted: any,
    countries: any[],
    stores: any[],
    logoutUser: () => {},
    countryList: () => {},
    storesList: () => {},
    addDriver: (data: any) => {},

}

export interface AddDriverState {
    [x: number]: any,    
    firstname: any,
    lastname: any,
    email: any,
    password: any,
    city: any,
    cnic: any,
    phone: any,
    country: any,
    address: any,
    storeId: any,
    isValidEmail: any,
    //isValidPhone: any
}

export interface EditDriverProps {
    message: string,
    isUpdated: any,
    driverData: any,
    match: any,
    countries: any[],
    stores: any[],
    countryList: () => {},
    //storesList: () => {},
    editDriver: (id: any, data: any) => {},
    getDriver: (id: any) => {}
    logoutUser: () => {}
}
export interface EditDriverState {
    [x: number]: any,
    firstname: string,
    lastname: string,
    email: string,
    city: string,
    country: string,
    address: string,
    cnic: any,
    isValidEmail: any,
    phone: any
}
