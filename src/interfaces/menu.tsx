export interface MenuProps {
  data: any[];
  groups: any[];
  menusList: () => {};
  countryList: () => {},
  syncMenuWithFP: (id:any,store_ids:any,syncMenuWithFP:any) => {},
  publishMenu: (id: any, data: any) => {};
  groupsListByMenuId: (id: any) => {};
  logoutUser: () => {};
}
export interface AddMenuProps {
  onModalClose: any;
  onSave: any;
  columns: any;
  validateState: any;
  ignoreEditable: any;
  states: any[];
  countries: any[];
  isInserted: any;
  message: any;
  storetype: any[];
  brands: any[];
  addMenu: (data: any) => {};
  countryList: () => {},
  statesList: () => {},
  storeTypeList: () => {};
  brandsList: () => {};
  logoutUser: () => {};
}
export interface AddMenuState {
  [x: number]: any;
  menuname: string;
  storetype: any;
  brand: any;
  country: any;
  state: any;
}
export interface EditmenuProps {
  isUpdated: any;
  message: any;
  match: any;
  storetype: any[];
  states: any[];
  countries: any[];
  brands: any[];
  menuData: any;
  editMenu: (id: any, data: any) => {};
  brandsList: () => {};
  countryList: () => {},
  statesList: () => {},
  getMenu: (id: any) => {};
  logoutUser: () => {};
  storeTypeList: () => {};
}
export interface EditMenuState {
  [x: number]: any;
  menuname: string;
  storetype: any;
  brand: any;
  country: any;
  state: any;
}
export interface GroupProps {
  data: any[];
  groups: any[];
  subGroupListByGroupId: (id: any) => {};
  blockunblockgroup: (id: any, is_active: any) => {};
  groupsList: () => {};
  logoutUser: () => {};
}
export interface AddGroupProps {
  menus: any[];
  stores: any[];
  orderingModes: any,
  addGroup: (data: any) => {};
  storesList: () => {};
  menusList: () => {};
  OrderingModes: () => {};
  logoutUser: () => {};
}
export interface AddGroupState {
  [x: number]: any;
  groupname: string;
  menu: any;
  is_featured: any
  image: any;
  priority: any,
  mode: any[],
  is_lsm: any,
  storesJson: any,
  poscode: any,
  erpid: any,
  category_type: any
}
export interface EditGroupProps {
  match: any;
  menus: any[];
  stores: any[];
  groupData: any;
  orderingModes: any,
  editGroup: (id: any, data: any) => {};
  getGroup: (id: any) => {};
  storesList: () => {};
  logoutUser: () => {};
  OrderingModes: () => {};
  menusList: () => {};
}
export interface EditGroupState {
  [x: number]: any;
  groupname: string;
  menu: any;
  image: any;
  priority: any,
  poscode: any,
  erpid: any,
  is_lsm: any,
  is_featured: any
  storesJson: any,
  mode: any,
  category_type: any
}
export interface AddSubGroupProps {
  groups: any[];
  stores: any[];
  orderingModes: any,
  addSubGroup: (data: any) => {};
  storesList: () => {};
  groupsList: () => {};
  OrderingModes: () => {};
  logoutUser: () => {};
}
export interface AddSubGroupState {
  [x: number]: any;
  groupname: string;
  group: any;
  priority: any,
  fp_priority:any,
  mode: any[],
  is_lsm: any,
  storesJson: any,
  poscode: any,
  erpid: any
}
export interface EditSubGroupProps {
  match: any;
  groups: any[];
  stores: any[];
  subgroupData: any;
  orderingModes: any,
  editSubGroup: (id: any, data: any) => {};
  getSubGroup: (id: any) => {};
  storesList: () => {};
  logoutUser: () => {};
  OrderingModes: () => {};
  groupsList: () => {};
}
export interface EditSubGroupState {
  [x: number]: any;
  groupname: string;
  group: any;
  priority: any,
  fp_priority:any,
  poscode: any,
  erpid: any,
  is_lsm: any,
  storesJson: any,
  mode: any
}
export interface MenuItemProps {
  itemdata: any[];
  itemsList: () => {};
  statusChangeReasons: () => {}
  blockunblockitem: (id: any, is_active: any) => {};
  logoutUser: () => {};
}
export interface AddMenuItemProps {
  groups: any[];
  states: any[],
  menus: any[],
  stores: any[],
  duplicateItemPos: any,
  duplicateItemErp: any,
  orderingModes: any,
  variants: any[];
  subGroups: any[]
  channels:any[]
  itemLoad:any,
  subGroupListByGroupId: (group_id: any) => {}
  checkDuplicatePosCode: (pos_code: any) => {},
  checkDuplicateErpId: (erp_id: any) => {},
  storesList: () => {}
  addItems: (data: any) => {};
  groupsList: () => {};
  statesList: () => {};
  variantsList: () => {};
  menusList: () => {};
  channelList:()=>{}
  OrderingModes: () => {};
  logoutUser: () => {}
}

export interface AddMakeAComboProps {
  validateState: any;
  ignoreEditable: any;
  isInserted: any;
  message: any;
  groups: any[];
  menus: any[],
  items: any
  variants: any
  groupsList: () => {};
  menusList: () => {};
  logoutUser: () => {}
  saveData: (data: any) => {}
  getItemsByGroup: (groupid: number) => {},
  getVariantsByItem: (itemid: number) => {},
  blockUnblockMakeACombo: (id: number, data: any) => {}
}

export interface EditMakeAComboProps {
  validateState: any;
  ignoreEditable: any;
  isInserted: any;
  message: any;
  groups: any[];
  menus: any[],
  items: any
  variants: any
  data: any
  match: any
  groupsList: () => {};
  menusList: () => {};
  logoutUser: () => {}
  updateMakeACombo: (id: number, data: any) => {}
  getItemsByGroup: (groupid: number) => {},
  getVariantsByItem: (itemid: number) => {},
  blockUnblockMakeACombo: (id: number, data: any) => {}
  getMakeACombo: (id: number) => {}
}
export interface AddMenuItemState {
  [x: number]: any;
  sizejson: any;
  itemname: string;
  group: any;
  subgroup: any
  type: any;
  priority: any,
  is_best: any,
  channel:any,
  tax_percent: any;
  images: any;
  specific_days:any
  daysTiming: any
  hero_image: any,
  hero_mobile_image: any,
  hero_item: any,
  is_featured: any,
  is_foodpanda:any,
  recipe: any,
  settime: any;
  mode: any[];
  itemstart: any;
  itemclose: any;
  state: any;
  is_lsm: any,
  storesJson: any[],
  metaTitle: any,
  metaDesc: any,
  is_new: any,
  is_veg: any,
  is_hot: any,
  cross_sell: boolean,
  is_vegan: any,
  is_glutenFree: any,
  is_half_n_half: any
  is_full_pizza: any
  is_suggestive: any
  flag:any
}

export interface AddMakeAComboState {
  [x: number]: any;
  sizejson: any;
  name: string;
  group_id: any;
  type: any;
  menu_id: any,
}

export interface EditMakeAComboState {
  [x: number]: any;
  sizejson: any;
  name: string;
  group_id: any;
  type: any;
  menu_id: any,
  removeJson: any
}
export interface EditMenuItemProps {
  match: any;
  itemData: any;
  taxData: any;
  groups: any[];
  subGroups: any[],
  menus: any[],
  variants: any[]
  states: any[],
  stores: any[],
  channels:any[],
  duplicateItemPos: any,
  duplicateItemErp: any,
  orderingModes: any,
  itemLoad:any,
  subGroupListByGroupId: (group_id: any) => {}
  checkDuplicatePosCode: (pos_code: any) => {},
  checkDuplicateErpId: (erp_id: any) => {},
  storesList: () => {};
  statesList: () => {};
  menusList: () => {};
  getMenuItem: (id: any) => {};
  editItem: (id: any, data: any) => {};
  OrderingModes: () => {};
  groupsList: () => {};
  variantsList: () => {};
  logoutUser: () => {}
  channelList:()=>{}
}
export interface EditMenuItemState {
  [x: number]: any;
  sizejson: any[];
  itemname: string;
  erpid: any;
  poscode: any,
  group: any;
  subgroup: any;
  is_foodpanda:any;
  is_featured: any
  type: any;
  itemdesc: any;
  priority: any,
  images: any;
  hero_image: any,
  hero_mobile_image: any,
  hero_item: any,
  daysTiming:any
  flag:any
  settime: any;
  mode: any[];
  specific_days:any
  is_lsm: any;
  itemstart: any;
  itemclose: any;
  tax_percent: any,
  state: any,
  cross_sell: boolean,
  storesJson: any[],
  metaTitle: any,
  metaDesc: any,
  recipe: any,
  is_new: any,
  is_veg: any,
  is_hot: any,
  is_vegan: any,
  is_best: any
  is_glutenFree: any,
  is_half_n_half: any,
  is_full_pizza: any,
  is_suggestive:any
  channel:any
}
export interface ComboProps {
  data: any[];
  combosList: () => {};
  statusChangeReasons: () => {}
  blockunblockcombo: (id: any, is_active: any) => {};
  logoutUser: () => {};
}
export interface AddComboProps {
  history: any,
  menus: any[];
  combos: any[],
  states: any[];
  channels: any[];
  combooptions: any;
  comboChoiceItems: any[];
  choiceIndex: any,
  groups: any[],
  variants: any,
  subGroups: any[],
  addons: any[],
  crusts: any[],
  topings: any[]
  flavours: any[]
  condiments:any[]
  stores: any[],
  orderingModes: any[];
  comboLoad:any
  variantsList: () => {};
  subGroupListByGroupId: (group_id: any) => {}
  comboChoiceItemsByGroupId: (id: any, index: any) => {};
  storesList: () => {};
  addCombo: (data: any) => {};
  combosList: () => {};
  groupsList: () => {};
  menusList: () => {};
  channelList: () => {};
  logoutUser: () => {};
  groupedItemsList: (data: any) => {};
  OrderingModes: () => {};
  modGroupsList: () => {}
}
export interface AddComboState {
  [x: number]: any,
  channel: any,
  priority: any,
  comboname: string,
  is_featured: any
  erpid: any,
  poscode: any,
  combodesc: string,
  combocost: any,
  combosale: any,
  errtaxdef: any,
  tax_percent: any,
  hero_item: any,
  hero_image: any,
  specific_days:any
  hero_mobile_image: any,
  daysTiming:any
  topDeal: any,
  is_hide: any,
  is_voucher: any,
  is_foodpanda:any,
  is_blurry: any,
  combomrp: any,
  mode: any[],
  comboChoices: any[],
  taxstatus: any,
  settime: any,
  combostart: any,
  comboclose: any,
  images: any,
  flag:any
  menu: any,
  group: any,
  subgroup: any,
  state: any,
  is_lsm: any;
  storesJson: any[],
  comboUnique: any,
  order_modes_price: any[],
  metaTitle: any,
  metaDesc: any,
  altTag: any,
  is_suggestive:any
}
export interface EditComboProps {
  match: any;
  comboData: any;
  menus: any[];
  groups: any[],
  addons: any[],
  crusts: any[],
  variants:any
  topings: any[]
  flavours: any[]
  condiments:any[]
  subGroups: any[]
  channels: any[];
  states: any[]
  combooptions: any;
  comboChoices: any;
  taxData: any;
  stores: any[],
  orderingModes: any[];
  comboLoad:any
  variantsList: () => {};
  subGroupListByGroupId: (group_id: any) => {}
  comboChoiceItemsByGroupId: (id: any, index: any) => {};
  storesList: () => {};
  logoutUser: () => {};
  editCombo: (id: any, data: any) => {};
  menusList: () => {};
  channelList: () => {};
  groupsList(): () => {};
  groupedItemsList: (data: any) => {};
  getCombo: (id: any) => {};
  OrderingModes: () => {};
  modGroupsList: () => {}
}
export interface EditComboState {
  [x: number]: any;
  channel: any;
  priority: any,
  is_featured: any
  comboname: string;
  erpid: any;
  hero_item: any,
  topDeal: any,
  is_hide: any,
  is_voucher: any,
  is_foodpanda:any,
  is_blurry: any,
  comboUnique: any,
  poscode: any,
  combocost: any;
  combodesc: any,
  images: any;
  flag:any
  specific_days:any
  daysTiming:any
  hero_image: any,
  hero_mobile_image: any,
  combosale: any;
  combomrp: any;
  mode: any[],
  is_lsm: any;
  storesJson: any[]
  errtaxdef: any;
  tax_percent: any;
  taxstatus: any;
  settime: any;
  combostart: any;
  comboclose: any;
  menu: any;
  group: any,
  subgroup: any,
  state: any,
  comboChoices: any[],
  order_modes_price: any[],
  metaTitle: any,
  metaDesc: any,
  altTag: any,
  is_suggestive: any,
  is_free_delivery: any
}
export interface AttachSuggestiveComboProps {
  stores: any[];
  combos:any[];
  suggestiveCombos:any[],
  storesList: () => {};
  getOnlyCombos: () => {};
  getSuggCombos: () => {};
  updateSuggestiveDeals:(data:any)=>{}
  logoutUser: () => {};
}
export interface AttachSuggestiveComboState{
  storesJson: any[];
  selectedCombo:any,
  is_lsm:any
  [x: number]: any;
  selectedSuggComboJson:any[]
}
export interface EditSuggestiveComboProps {
  stores: any[];
  match: any,
  suggestiveData:any
  combos:any[];
  editSuggestive: (id: any, data: any) => {},
  suggestiveCombos:any[],
  storesList: () => {};
  getOnlyCombos: () => {};
  getSuggCombos: () => {};
  updateSuggestiveDeals:(data:any)=>{}
  getSuggestive: (id: any) => {},
  logoutUser: () => {};
}
export interface EditSuggestiveComboState{
  storesJson: any[];
  is_lsm:any
  selectedCombo:any,
  selectedSuggComboJson:any[]
  store_label:any,
  store_value:any
  [x: number]: any;
}
export interface SuggestiveComboProps {
  suggestiveComboList: () => {};
  logoutUser: () => {};
}
export interface ModGroupProps {
  data: any[];
  modifiers: any[];
  modGroupList: () => {};
  modifierListByModGroupId: (id: any) => {};
  blockunblockModifierGroup: (id: any, is_active: any, history: any) => {},
  logoutUser: () => {};
}
export interface AddModGroupProps {
  groups: any[];
  subgroups: any[];
  items: any[];
  menus: any[];
  logoutUser: () => {};
  groupsList: () => {};
  subgroupsList: () => {};
  itemsListForMultiSelect: () => {},
  addModGroup: (data: any) => {};
  menusList: () => {}; //menus: any[], groups: any[],
}
export interface AddModGroupState {
  [x: number]: any;
  groupJson: any[];
  subgroupJson: any[]
  modgroupname: string;
  modgroupdesc: string;
  poscode: any,
  erpid: any,
  modgroupnamehint:any
  type: any,
  level: any,
  itemsJson: any[];
  priority: any,
  min_quantity: any
  max_quantity: any,
  is_crust: any,
  is_addon: any,
  is_toping: any,
  is_flavour: any,
  is_condiment:any
}
export interface EditModGroupProps {
  match: any;
  modGroupData: any;
  groups: any[],
  subgroups: any[]
  items: any[],
  editModGroup: (id: any, data: any) => {};
  logoutUser: () => {};
  getModGroup: (id: any) => {};
  groupsList: () => {},
  subgroupsList: () => {}
  itemsListForMultiSelect: () => {},
}
export interface EditModGroupState {
  [x: number]: any;
  modgroupname: any;
  poscode: any,
  erpid: any,
  groupJson: any[],
  subgroupJson: any[],
  itemsJson: any[],
  modgroupdesc: any;
  type: any,
  modgroupnamehint: any
  level: any,
  priority: any,
  min_quantity: any;
  max_quantity: any;
  is_crust: any,
  is_addon: any,
  is_toping: any,
  is_flavour: any,
  is_condiment:any
}
export interface ModProps {
  data: any[];
  deleteModifier: (id: any) => {};
  modifierList: () => {};
  logoutAdmin: () => {};
}
export interface AddModProps {
  variants: any;
  groups: any[];
  items: any[];
  modgroups: any[];
  variantsList: () => {};
  logoutUser: () => {};
  modGroupsList: () => {};
  groupsList: () => {}
  addModifier: (data: any) => {};
  menusList: () => {}; //menus: any[], groups: any[],
  getItemsByGroup: (group_id: any) => {}
  getVariantsByItem: (item_id: any) => {}
}
export interface AddModState {
  [x: number]: any;
  modGroupJson: any;
  modifiername: string;
  modifierDescription?: string;
  erpid: any;
  poscode: any,
  priority: any,
  modifiertype: string;
  modifiercost: any;
  modifiersale: any;
  nutritional_info:any
  recipe?: any,
  min_quantity: any
  variant_id: any
  max_quantity: any
  images: any;
  for_specific_variations: any
}
export interface EditModProps {
  match: any;
  modData: any;
  modgroups: any[],
  variants: any[]
  groups: any[], items: any[],
  variantsList: () => {};
  modGroupsList: () => {},
  editModifier: (id: any, data: any) => {};
  logoutUser: () => {};
  getModifier: (id: any) => {}; //menus: any[], groups: any[],
  groupsList: () => {}
  getItemsByGroup: (id: any) => {};
  getVariantsByItem: (id: any) => {}
}
export interface EditModState {
  [x: number]: any;
  modGroupJson: any;
  for_specific_variations: any,
  variant_id: any
  modifiername: string;
  modifierDescription?: string;
  erpid: any;
  poscode: any,
  priority: any,
  modifiertype: string;
  nutritional_info:any
  modifiercost: any;
  modifiersale: any;
  images: any;
  min_quantity: any,
  max_quantity: any,
  recipe?:any
  // sizejson:any
}

export interface BannerProps {
  banners: any[],
  bannersList: () => {},
  logoutUser: () => {}
}
export interface AddBannerState {
  [x: number]: any;
  bannerDesktop: any,
  bannerMobile: any,
  banner_type: any,
  settime: any,
  itemstart: any,
  itemclose: any,
  is_lsm:any
  storesJson:any
  sync_type: any,
  sync_type_id: any,
  sync_type_label: any,
  specific_days: any,
  priority: any,
  daysTiming: any
}
export interface AddBannerProps {
  menuItemsForBanners: any[],
  combosForBanners: any[],
  stores: any[],
  handleBannerInput: (data: any) => {},
  addBanner: (data: any) => {},
  itemsListForBanners: () => {}
  logoutUser: () => {},
  storesList: () => {}
}
export interface EditBannerState {
  [x: number]: any;
  bannerDesktop: any,
  bannerMobile: any,
  banner_type: any,
  sync_type: any,
  sync_type_id: any,
  sync_type_label: any,
  settime: any,
  itemstart: any,
  is_lsm:any
  storesJson:any
  itemclose: any,
  specific_days: any,
  priority: any,
  daysTiming: any
}
export interface EditBannerProps {
  bannerData: any,
  match: any,
  menuItemsForBanners: any[],
  stores: any[],
  combosForBanners: any[],
  editBanner: (id: any, data: any) => {},
  getBanner: (id: any) => {},
  storesList: () => {};
  itemsListForBanners: () => {}
  logoutUser: () => {}
}

//Nutritional Info
export interface CategoryProps {
  data: any[],
  blockunblockcategory: (id: any, is_active: any, categoryName: any) => {}
  categoriesList: () => {}
}

export interface AddCategoryProps {
  history: any;
  logoutUser: () => {}
  addNutCategories: (data: any) => {}
}

export interface AddCategoryState {
  [x: number]: any;
  categoryName: any
}

export interface EditCategoryProps {
  history: any;
  match: any;
  nutCategoryData: any
  logoutUser: () => {}
  getNutCategory: (id: any) => {}
  editNutCategory: (id: any, data: any) => {}
}

export interface EditCategoryState {
  [x: number]: any;
  categoryName: any
}

export interface NutItemProps {
  data: any[],
  blockunblockcategory: (id: any, is_active: any, categoryName: any) => {}
  categoriesList: () => {}
}

export interface AddNutItemProps {
  history: any;
  items: any[];
  nutCategoriesForItems: any[],
  itemsListForMultiSelect: () => {},
  logoutUser: () => {}
  nutritionalCatList: () => {}
  addNutItem: (data: any) => {}
}

export interface AddNutItemState {
  [x: number]: any;
  itemName: any,
  nutCatId: any,
  is_variation: any,
  total_piece: any,
  cal_per_piece: any,
  total_cal: any,
  total_fat: any,
  trans_fat_acid: any,
  cholesterol: any,
  menuItem:any
  sat_fat: any,
  total_carbs: any,
  sugar: any,
  fibre: any,
  protein: any,
  sodium: any,
  sizejson: any
}

export interface EditNutItemProps {
  history: any;
  match: any;
  items: any[];
  nutCategoriesForItems: any[],
  nutritionalCatList: () => {}
  itemsListForMultiSelect: () => {},
  nutItemData: any
  logoutUser: () => {}
  getNutItem: (id: any) => {}
  editNutItem: (id: any, data: any) => {}
}

export interface EditNutItemState {
  [x: number]: any;
  itemName: any,
  nutCatId: any,
  nutCatlabel: any,
  is_variation: any,
  total_piece: any,
  menuItem:any
  cal_per_piece: any,
  total_cal: any,
  total_fat: any,
  trans_fat_acid: any,
  cholesterol: any,
  sat_fat: any,
  total_carbs: any,
  sugar: any,
  fibre: any,
  protein: any,
  sodium: any,
  sizejson: any
}