export interface CouponProps {
    data: any[],
    couponlist: () => {},
    deleteCoupon: (id: any) => {}
    activeInactiveCoupon: (id: any, is_archive: any) => {}
    logoutUser: () => {},
}
export interface AddCouponProps {
    onModalClose: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,searchCustomers:any[],
    brands :any, menus:any, groups: any,items:any[],stores:any[],
    isInserted: any, message: any, addCoupon: (data: any) => {}, brandsList: () => {}, menusList : () => {},
    itemsListForMultiSelect: () => {},
    storesList:()=>{},
    groupsList : () => {}, logoutUser: () => {},
    searchCustomerByPhone:(phone:any)=>{}
}
export interface AddCouponState {
    [x: number]: any,
    couponname: string,
    coupondesc: any,
    pos_code:any
    couponvalue: any,
    couponcode:any,
    expiryDate: any,
    startDate:any,
    percent: any,
    mode: any,
    channel: any,
    type: any,
    discountType: any,
    limit: any,
    type_id: any,
    multiJson: any[],
    multiStoreJson:any[],
    multiGroupJson: any[],
    freeDelivery:any,
    minamount:any,
    totalusagebycus:any,
    usageDuration: any,
    minorders:any,
    specificCustomer:any,
    phone:any,
    multiCustomerJson:any[],
    vipCustomer:any,
    is_nps:any,
    setType:any
}
export interface BulkCouponProps {
    history: any, onSave: any, columns: any, validateState: any, ignoreEditable: any,searchCustomers:any[],
    brands :any, menus:any, groups: any,items:any[],stores:any[],
    CouponsBulkUpload: (data: any,updateLoadFn:any) => {}, brandsList: () => {}, menusList : () => {},
    itemsListForMultiSelect: () => {},
    storesList:()=>{},
    groupsList : () => {}, logoutUser: () => {},
    searchCustomerByPhone:(phone:any)=>{}
}
export interface BulkCouponState {
    [x: number]: any,
    couponname: string,
    coupondesc: any,
    pos_code:any
    couponvalue: any,
    expiryDate: any,
    startDate:any,
    percent: any,
    mode: any,
    channel: any,
    type: any,
    discountType: any,
    limit: any,
    type_id: any,
    multiJson: any[],
    multiStoreJson:any[],
    multiGroupJson: any[],
    freeDelivery:any,
    minamount:any,
    totalusagebycus:any,
    usageDuration:any
    minorders:any,
    specificCustomer:any,
    phone:any,
    multiCustomerJson:any[],
    vipCustomer:any,
    is_nps:any
    isLoad:any,
    isSelected: any,
    setType:any,
    rows: any[],
    file:any,
    coupons:any[]
}
export interface EditCouponProps {
    isUpdated: any, message: any, match: any,
    couponData: any,
    brands :any, menus:any, groups: any,items:any[],stores:any[],searchCustomers:any[]
    editCoupon: (id: any, data: any) => {},
    itemsListForMultiSelect:()=>{},
    menusList : () => {},
    groupsList : () => {},
    logoutUser: () => {}
    getCoupon: (id: any) => {},
    storesList:()=>{},
    searchCustomerByPhone:(phone:any)=>{}
}
export interface EditCouponState {
    [x: number]: any,
    couponname: string,
    coupondesc: any,
    couponcode:any,
    pos_code:any
    couponvalue: any,
    expiryDate: any,
    startDate:any,
    percent: any,
    mode: any,
    channel: any,
    type: any,
    discountType: any,
    limit: any,
    type_id: any,
    multiJson: any[],
    multiStoreJson:any[],
    multiGroupJson: any[],
    freeDelivery:any,
    minamount:any,
    totalusagebycus:any,
    usageDuration: any,
    minorders:any,
    specificCustomer:any,
    phone:any,
    multiCustomerJson:any[],
    vipCustomer:any,
    is_nps:any,
    setType:any
}