import Api from '../../components/Api';
import { REPORTS_LOAD, D<PERSON><PERSON>Y_SALES_REPORTS, ACTIVITY_LOGS_REPORT, RIDER_DETAILS_REPORT, ORDER_DETAIL_REPORT, CANCEL_DETAIL_REPORT, <PERSON><PERSON><PERSON>_SMRY_REPORTS, STORES_REPORTS, RIDERS_REPORTS, CUSTOMERS_REPORTS, STORES_LIST, SOS_REPORTS, PMIX_REPORTS, SALES_MIX_REPORTS, CHANNEL_MIX_REPORTS, COUPON_REDEMPTION_REPORTS, PROFIT_REPORTS, DISCOUNT_REPORTS, SYSTEM_INTEGRATED_ORDERS, ORDER_ITEMS_DETAIL_REPORT, PAYMENT_LOGS, DEVICE_MIX_REPORTS, VOUCHER_ORDER_DETAIL_REPORT, AGGREGATOR_DATA_EXPORT, AGGREGATOR_FAILED_ORDERS, MENU_LOGS, NPS_LOGS } from './reportType';
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import moment from 'moment';
toast.configure();

export const dailySalesReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post('/reports/daily_sales', data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: DAILY_SALES_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                console.log(err);
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: DAILY_SALES_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

// export const sosReport = (data: any) => {
export const sosReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        // Api.post(`/reports/sos_report`, data, {
        Api.post(`/reports/sos_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    response.data.successResponse.forEach((element: any) => {
                        element.date_created = moment(element.date_created).local().format('YYYY-MM-DD,HH:mm').split(' ');
                    });
                    dispatch({
                        type: SOS_REPORTS,
                        payload: response.data.successResponse
                        // currentPage: response.data.currentPage,
                        // pageCount: response.data.pageCount,
                        // numOfRows: response.data.numOfRows,
                        // msg: ""
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                console.log(err);
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: SOS_REPORTS,
                        payload: []
                        // currentPage: 0,
                        // pageCount: 0,
                        // numOfRows: 0,
                        // msg: ""
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const storesList = () => {
    return function (dispatch: any) {
        let token: any = localStorage.getItem('token');
        Api.get('/reports/stores', {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: STORES_LIST,
                        payload: response.data.successResponse
                    })
                }
            }).catch(err => {
                if (err.response) {
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}
export const customersReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/customers`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: CUSTOMERS_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    console.log(err.response.data.message)
                    dispatch({
                        type: CUSTOMERS_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                } else {
                    alert(err.message)
                }
            });
    }
}
export const pmixReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/pmix_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: PMIX_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: PMIX_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                } else {
                    alert(err.message)
                }
            });
    }
}

export const salesmixReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/salesmix_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: SALES_MIX_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: SALES_MIX_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                } else {
                    alert(err.message)
                }
            });
    }
}

export const channelmixReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post('/reports/channel_sales', data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: CHANNEL_MIX_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: CHANNEL_MIX_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}
export const deviceinfoReports = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post('/reports/device_sales', data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: DEVICE_MIX_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: DEVICE_MIX_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const couponredemptionReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/coupon_redemption`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: COUPON_REDEMPTION_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: CHANNEL_MIX_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const profitReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/profit_margin`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: PROFIT_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: PROFIT_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const discountReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/discount_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: DISCOUNT_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: DISCOUNT_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const ridersReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/rider_efficiency_summary`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: RIDERS_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: RIDERS_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const riderDetailsReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/rider_efficiency_details`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: RIDER_DETAILS_REPORT,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: RIDER_DETAILS_REPORT,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const storesReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/store_efficiency_summary`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    console.log(response.data, "Stores respone")
                    dispatch({
                        type: STORES_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: STORES_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const cancellationReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/cancellation_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    console.log(response.data, "Stores respone")
                    dispatch({
                        type: CANCEL_SMRY_REPORTS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: CANCEL_SMRY_REPORTS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const cancellationDtetailsReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/cancellation_details_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: CANCEL_DETAIL_REPORT,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: CANCEL_DETAIL_REPORT,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const orderDetailsReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/order_details_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    response.data.successResponse.forEach((element: any) => {
                        if (element.confirmed_datetime !== '0000-00-00 00:00:00.000000') {
                            element.confirmed_datetime = moment(element.confirmed_datetime).local().format('YYYY-MM-DD HH:mm:ss');
                        } else {
                            element.confirmed_datetime = "";
                        }
                        if (element.pending_datetime !== '0000-00-00 00:00:00.000000') {
                            element.pending_datetime = moment(element.pending_datetime).local().format('YYYY-MM-DD HH:mm:ss');
                        } else {
                            element.pending_datetime = "";
                        }
                        if (element.kitchen_datetime !== '0000-00-00 00:00:00.000000') {
                            element.kitchen_datetime = moment(element.kitchen_datetime).local().format('YYYY-MM-DD HH:mm:ss');
                        } else {
                            element.kitchen_datetime = "";
                        }
                        if (element.ready_datetime !== '0000-00-00 00:00:00.000000') {
                            element.ready_datetime = moment(element.ready_datetime).local().format('YYYY-MM-DD HH:mm:ss');
                        } else {
                            element.ready_datetime = "";
                        }
                        if (element.dispatched_datetime !== '0000-00-00 00:00:00.000000') {
                            element.dispatched_datetime = moment(element.dispatched_datetime).local().format('YYYY-MM-DD HH:mm:ss');
                        } else {
                            element.dispatched_datetime = "";
                        }
                        if (element.delivered_datetime !== '0000-00-00 00:00:00.000000') {
                            element.delivered_datetime = moment(element.delivered_datetime).local().format('YYYY-MM-DD HH:mm:ss');
                        } else {
                            element.delivered_datetime = "";
                        }
                    })
                    dispatch({
                        type: ORDER_DETAIL_REPORT,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: ORDER_DETAIL_REPORT,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const orderSystemIntgReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/orderSystemIntgReport`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                console.log("SYSTEM_INTEGRATED_ORDERS", response.data);
                if (response.data.success) {

                    console.log(response.data.successResponse, "logs respone")
                    dispatch({
                        type: SYSTEM_INTEGRATED_ORDERS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: SYSTEM_INTEGRATED_ORDERS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}
export const activityLogsReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/activity_logs_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    console.log(response.data.successResponse, "logs respone")
                    dispatch({
                        type: ACTIVITY_LOGS_REPORT,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: ACTIVITY_LOGS_REPORT,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}
export const menuLogsReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/menu_logs_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: MENU_LOGS,
                        payload: response.data.successResponse
                    })
                    setTimeout(()=>{
                        dispatch({
                            type: REPORTS_LOAD,
                            isLoad: false
                        })
                    },1500)
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: MENU_LOGS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}
export const npsLogsReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/nps_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: NPS_LOGS,
                        payload: response.data.successResponse
                    })
                    setTimeout(()=>{
                        dispatch({
                            type: REPORTS_LOAD,
                            isLoad: false
                        })
                    },1500)
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: NPS_LOGS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}
export const paymentLogsReport = (data:any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/payment_logs_report`,data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    console.log(response.data.successResponse, "logs respone")
                    dispatch({
                        type: PAYMENT_LOGS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: PAYMENT_LOGS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const orderItemDetailsReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad:true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/order_item_details_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    response.data.successResponse.forEach((element:any) => {
                        if(element.OrderDateTime !== '0000-00-00 00:00:00.000000') {
                            element.OrderDateTime = moment(element.OrderDateTime).local().format('YYYY-MM-DD HH:mm:ss');
                        } else {
                            element.OrderDateTime = "";
                        }
                    })
                    dispatch({
                        type: ORDER_ITEMS_DETAIL_REPORT,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad:false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: ORDER_ITEMS_DETAIL_REPORT,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}
export const voucherOrderDetailsReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/voucher_logs_report`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    dispatch({
                        type: VOUCHER_ORDER_DETAIL_REPORT,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: VOUCHER_ORDER_DETAIL_REPORT,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const aggrFailedOrdersReport = (data: any) => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.post(`/reports/aggregatorFailedOrders`, data, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                console.log("AGGREGATOR_FAILED_ORDERS", response.data);
                if (response.data.success) {

                    console.log(response.data.successResponse, "logs respone")
                    dispatch({
                        type: AGGREGATOR_FAILED_ORDERS,
                        payload: response.data.successResponse
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: AGGREGATOR_FAILED_ORDERS,
                        payload: []
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const aggrDataExportReport = () => {
    return function (dispatch: any) {
        dispatch({
            type: REPORTS_LOAD,
            isLoad: true
        })
        let token: any = localStorage.getItem('token');
        Api.get(`/reports/aggregatorDataExtraction`, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response) => {
                if (response.data.success) {
                    console.log(response.data.successResponse, "logs respone")
                    dispatch({
                        type: AGGREGATOR_DATA_EXPORT,
                        payload: response.data.successResponse,
                        menuItems_extract: response.data.menuItems,
                        combos_extract: response.data.combos,
                        total_data_extract: response.data.length,
                        msg: ""
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                }
            }).catch(err => {
                if (err.response) {
                    toast.error("No data to report", { position: toast.POSITION.TOP_RIGHT, hideProgressBar: true, autoClose: 3000 });
                    dispatch({
                        type: AGGREGATOR_DATA_EXPORT,
                        payload: [],
                        msg: ""
                    })
                    dispatch({
                        type: REPORTS_LOAD,
                        isLoad: false
                    })
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}