import { PAYMENTORDER, PAYMENTS_LIST } from './paymentType'
import Api from '../../components/Api';
import { toast } from 'react-toastify';

export const paymentsList = (page:any, limit:any) => {
    return function (dispatch: any) {
        let token: any = localStorage.getItem('token');
        Api.get(`/admin/payments/${page}/${limit}`, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response:any) => {
                if (response.data.success) {
                    dispatch({
                        type: PAYMENTS_LIST,
                        payload: response.data.successResponse,
                        count: response.data.count
                    })
                }
            }).catch(err => {
                if (err.response) {
                    console.log(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const paymentByOrderId = (order_id: number) => {
    return function (dispatch: any) {
        let token: any = localStorage.getItem('token');
        Api.get(`/admin/payment/${order_id}`, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
            .then((response:any) => {
                if (response.data.success) {
                    dispatch({
                        type: PAYMENTORDER,
                        payload: [response.data.successResponse]
                    })
                }
            })
            .catch(err => {
                if (err.response) {
                    dispatch({
                        type: PAYMENTORDER,
                        payload: [
                            {
                                payment_id: '-',
                                order_id: '-',
                                cardOrderId: '-',
                                payment_method: '-',
                                store_name: '-',
                                branch_code: '-',
                                first_name: '-',
                                phone_number: '-',
                                payment_amount: '',
                                payment_status: '-',
                                date_modified: ''
                            }
                        ]
                    })
                    toast.error(err.response.data.message)
                } else {
                    alert(err.message)
                }
            });
    }
}

export const paymentPageReset = () => {
    return function (dispatch: any) {
        dispatch({
            type: PAYMENTORDER,
            payload: []
        })
    }
}