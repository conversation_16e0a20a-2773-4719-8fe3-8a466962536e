import { PAYMENTS_LIST } from '../actions/paymentType'
import { PAYMENTORDER } from '../actions/paymentType'
const initialState: any = {
    data: [] || "",
    count: 0,
    paymentByOrder: []
}

const paymentReducer = (state = initialState, action: any) => {
    switch (action.type) {
        case PAYMENTS_LIST: return {
            ...state,
            data: action.payload,
            count: action.count
        }
        case PAYMENTORDER: return {
            ...state,
            paymentByOrder: action.payload
        }
        default: return state;
    }
}
export default paymentReducer;