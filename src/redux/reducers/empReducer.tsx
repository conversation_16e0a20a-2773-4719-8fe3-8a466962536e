import { ACTIVE_EMP_COUPONS, EMP_LIST, GET_EMP,EMP_TYPE_LIST, VOUC_LIST, REDEEMED_LIST } from '../actions/empTypes'
const initialState: any = {
    employees: [] || "",
    empData: {},
    empCoupons: [] || "",
    empTypes: [] || "",
    empMeta: null,
    voucherList: [] || "",
    redeemedList: [] || ""

}

const empReducer = (state = initialState, action: any) => {
    switch (action.type) {
        case EMP_LIST: return {
            ...state,
            employees: action.payload?.data,
            empMeta: action.payload?.meta
        }
        case VOUC_LIST: return {
            ...state,
            voucherList: action.payload
        }
        case REDEEMED_LIST: return {
            ...state,
            redeemedList: action.payload
        }
        case GET_EMP: return {
            ...state,
            empData: action.payload
        }
        case ACTIVE_EMP_COUPONS: return {
            ...state,
            empCoupons: action.payload
        }
        case EMP_TYPE_LIST: return {
            ...state,
            empTypes: action.payload
        }
        default: return state;
    }
}
export default empReducer;