import { HANDLE_INPUT, BRANDS_LIST, GET_BRAND, HANDLE_CHECK } from '../actions/brandType'
const initialState: any = {
    brandname: "",
    branddetails: "",
    brandtype: "",
    brands: [] || "",
    message: "",
    onlineFraudAttempts: 0,
    normalFraudAttempts: 0,
    is_app_live: false,
    is_ios_live: false,
    is_google_login: false,
    is_fb_login: false,
    is_apple_login: false,
    is_delete_button: false,
    is_cash_disable: false,
    is_card_disable: false,
    is_jazz_disable: false,
    is_dds_map_address:false,
}

const brandReducer = (state = initialState, action: any) => {
    switch (action.type) {
        case HANDLE_INPUT: return {
            ...state,
            [action.input.name]: action.input.value
        }
        case HANDLE_CHECK: return {
            ...state,
            [action.input.name]: action.input.checked
        }
        case BRANDS_LIST: return {
            ...state,
            brands: action.payload
        }
        case GET_BRAND: return {
            ...state,
            brandname: action.payload.brand_name,
            branddetails: action.payload.brand_details,
            brandtype: action.payload.brand_type,
            onlineFraudAttempts: action.payload.online_fraud_attempts,
            normalFraudAttempts: action.payload.normal_fraud_attempts,
            is_app_live: action.payload.is_app_live,
            is_ios_live: action.payload.is_ios_live,
            is_google_login: action.payload.is_google_login,
            is_fb_login: action.payload.is_fb_login,
            is_apple_login: action.payload.is_apple_login,
            is_delete_button: action.payload.is_delete_button,
            is_cash_disable: action.payload.is_cash_disable,
            is_card_disable: action.payload.is_card_disable,
            is_jazz_disable: action.payload.is_jazz_disable,
            is_dds_map_address: action.payload.is_dds_map_address,
            is_google_maps_enable: action.payload.is_google_maps_enable
        }
        default: return state;
    }
}
export default brandReducer;