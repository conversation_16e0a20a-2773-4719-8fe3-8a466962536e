import React, { Component } from "react";
import { connect } from "react-redux";

import Select from "react-select";
import pin from "../../assets/images/domino_marker.png";
import { SimplexMap } from "@simplex/map";
import loader from "../../assets/images/domino_loader.gif";
import { RestaurantlocatorState } from "../../components/Interface/loginInterface";
import { getStores } from "../../redux";
import moment from "moment";
import { getCitiesWeb } from "../../redux/actions/cityAction";
import * as geolib from "geolib";
import GoogleMapComponent from '../GoogleMaps/googleMaps';
import api from "../../components/api";


interface GeoJSONPoint {
  latitude: number;
  longitude: number;
}

interface StoreLocatorState extends RestaurantlocatorState {
  brandDetails: any;
  isUserDragging: any
}

class StoreLocator extends Component<
  {
    google?: any;
    cities?: any;
    restuarants?: any[];
    onSearch: any;
    getStores: () => {};
    getCitiesWeb: () => {};
  },
  StoreLocatorState
> {
  constructor(props: any) {
    super(props);
    this.state = {
      id: "All",
      stores: [],
      selectedStore: null,
      zoom: 5,
      zoomChangeSeed: Math.random(),
      initialMapCenter: [],
      selectedStoresMarkers: [],
      brandDetails: null,
      isUserDragging: false,
    };
  }
  componentDidMount() {
    window.scrollTo(0, 0);
    this.props.getStores();
    this.props.getCitiesWeb();
    this.fetchBrandDetails();
  }

  fetchBrandDetails = async () => {
    try {
      const res: any = await api.get(`customer/brand-config/${Number(1)}`);
      if (res.data.success) {
        this.setState({ brandDetails: res.data.data });
      }
    } catch (error) {
      console.error("Error fetching brand details:", error);
    }
  };
  onSearch = (e: any) => {
    console.log("e.valuee.valuee.value", e.value);
    this.handleMapDragEnd();
    this.setState({ selectedStore: null }); // Close info modal when city changes

    let { restuarants, cities } = this.props;
    if (e.value !== "All") {
      let stores: any =
        restuarants && restuarants.filter((store) => e.value == store.city_id);
      const points: GeoJSONPoint[] = stores
        .map((item: any) => {
          return {
            latitude: item.lat,
            longitude: item.lng,
          };
        })
        .filter((obj: { latitude: number }) => obj.latitude > 0);
      const centerPoint = this.getCenter(points);
      centerPoint &&
        centerPoint.length > 0 &&
        this.setState({ initialMapCenter: centerPoint });
      let storesmarkers = stores.map((item: any) => {
        return {
          markerPopupName: item.store_name,
          coordinates: {
            lat: item.lat,
            lng: item.lng,
          },
        };
      });
      this.setState({
        stores: stores,
        id: e.value,
        zoom: 10,
        selectedStore: null,
        selectedStoresMarkers: storesmarkers,
      });
    } else {

      let stores: any = restuarants;
      const points: GeoJSONPoint[] =
        stores &&
        stores
          .map((item: any) => {
            return {
              latitude: item.lat,
              longitude: item.lng,
            };
          })
          .filter((obj: { latitude: number }) => obj.latitude > 0);
      const centerPoint = this.getCenter(points);
      centerPoint &&
        centerPoint.length > 0 &&
        this.setState({ initialMapCenter: centerPoint });
      let storesmarkers = stores.map((item: any) => {
        return {
          markerPopupName: item.store_name,
          coordinates: {
            lat: item.lat,
            lng: item.lng,
          },
        };
      });
      this.setState({
        stores: restuarants,
        id: e.value,
        zoom: 5,
        selectedStore: null,
        selectedStoresMarkers: storesmarkers,

      });
    }
  };
  // Find Center of given lat lngs
  getCenter = (points: GeoJSONPoint[]) => {
    if (points.length > 0) {
      const centerPoint = geolib.getCenterOfBounds(points);
      if (!centerPoint) return [];
      return [centerPoint.longitude, centerPoint.latitude];
    } else {
      return [];
    }
  };
  handleMapDragStart = () => {
    this.setState({ isUserDragging: true });
  };

  handleMapDragEnd = () => {
    setTimeout(() => {
      this.setState({ isUserDragging: false });
    }, 300);
  };
  render() {
    const { cities, restuarants } = this.props;
    let list: any = cities;
    let citiess: any = [{ label: "All", value: "All" }];
    list.map((city: any) => {
      citiess.push({ label: city.name, value: city.id });
    });

    const {
      stores,
      id,
      initialMapCenter,
      zoom,
      zoomChangeSeed,
      selectedStoresMarkers,
      selectedStore,
    } = this.state;
    const points: GeoJSONPoint[] | any =
      restuarants &&
      restuarants
        .map((item: any) => {
          return {
            latitude: item.lat,
            longitude: item.lng,
          };
        })
        .filter((obj: { latitude: number }) => obj.latitude > 0);
    const centerPoint: any = this.getCenter(points);
    let defaultMarkers =
      restuarants &&
      restuarants.map((item: any) => {
        return {
          markerPopupName: item.store_name,
          coordinates: {
            lat: item.lat,
            lng: item.lng,
          },
        };
      });
    let defaultInitialCenter = id == "All" ? centerPoint : [];
    let center = selectedStore
      ? [selectedStore.lng, selectedStore.lat]
      : defaultInitialCenter;
    defaultInitialCenter =
      id == "All" || selectedStore ? center : initialMapCenter;
    return (
      <div className="store-locator">
        <div className="container">
          <h3 className="text-center f-w-700 mb-4">Store Finder</h3>
        </div>
        <div className="container">
          <div className="row">
            <div className="col-lg-5">
              <div className="col-lg-12">
                <Select
                  isSearchable={true}
                  placeholder={"Select city"}
                  defaultValue={{ label: "All", value: "All" }}
                  onChange={(e) => this.onSearch(e)}
                  options={citiess}
                  styles={{
                    option: (provided, state) => ({
                      ...provided,
                      backgroundColor: state.isSelected
                        ? "rgb(0,105,145)"
                        : "inherit",
                      "&:hover": {
                        backgroundColor: state.isSelected
                          ? "rgb(0,105,145)"
                          : "rgb(222, 235, 255)",
                      },
                    }),
                  }}
                  menuPortalTarget={document.body}
                  // styles={{ menuPortal: base => ({ ...base, zIndex: 9999 }) }}
                  isLoading={citiess.length == 0 ? true : false}
                  // className="find-location"
                />
              </div>

              {/* {stores.length == 0 &&
                <h5 id="" className="text-center mt-3"> Select a city </h5>
              } */}
              <div className="stor-slection">
                {id == "All"
                  ? restuarants &&
                    restuarants.map((store: any, index: any) => (
                      <div className="location-area" key={index}>
                        <div className="row">
                          <div className="order-card w-100 mt-1 pbtm-sm-5">
                            <div
                              onClick={() => {
                                this.handleMapDragEnd();
                                setTimeout(() => {
                                  this.setState({
                                    selectedStore: store,
                                    zoom: 17,
                                  });
                                }, 100);
                              }}
                              className="col-lg-8 w-75"
                            >
                              <h4 className="f-w-700">{store.store_name}</h4>
                              <h3>{store.address}</h3>

                              <h5 className="store-timing f-w-700">
                                <span>
                                  {moment(store.store_open_time, [
                                    "hh:mm:ss",
                                  ]).format("hh:mm A")}{" "}
                                </span>
                                -
                                <span>
                                  {" "}
                                  {moment(store.store_close_time, [
                                    "hh:mm:ss",
                                  ]).format("hh:mm A")}
                                </span>
                              </h5>
                            </div>

                            <div
                              onClick={() =>
                                window.open(
                                  "https://maps.google.com?q=" +
                                    store.lat +
                                    "," +
                                    store.lng
                                )
                              }
                              className=" col-lg-1 click-errow errow-none"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Open Map"
                            >
                              <i className="ri-map-pin-fill f-md"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  : stores &&
                    stores.map((store: any, index: any) => (
                      <div className="location-area" key={index}>
                        <div className="row">
                          <div className="order-card w-100 mt-1 pbtm-sm-5">
                            <div
                              onClick={() => {
                                this.handleMapDragEnd();
                                this.setState({
                                  selectedStore: store,
                                  zoom: 17,
                                });
                              }}
                              className="col-lg-8 w-75"
                            >
                              <h4 className="f-w-700">{store.store_name}</h4>
                              <h3>{store.address}</h3>

                              <h5 className="store-timing f-w-700">
                                <span>
                                  {moment(store.store_open_time, [
                                    "hh:mm:ss",
                                  ]).format("hh:mm A")}{" "}
                                </span>
                                -
                                <span>
                                  {" "}
                                  {moment(store.store_close_time, [
                                    "hh:mm:ss",
                                  ]).format("hh:mm A")}
                                </span>
                              </h5>
                            </div>

                            <div
                              onClick={() =>
                                window.open(
                                  "https://maps.google.com?q=" +
                                    store.lat +
                                    "," +
                                    store.lng
                                )
                              }
                              className=" col-lg-1 click-errow errow-none"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Open Map"
                            >
                              <i className="ri-map-pin-fill f-md"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
              </div>
            </div>
            <div className="col-lg-7 store-map-item-remove">
              {this.state.brandDetails && this.state.brandDetails.is_google_maps_enable == "1" ? (
                <GoogleMapComponent
                  storesList={id == "All" ? restuarants : stores}
                  storeName=""
                  isAddressComp={false}
                  setCoordinatesString={() => { }}
                  setIsCoordinatesStringLoading={() => { }}
                  onSetData={() => { }}
                  showStoreMarkers={true}
                  isStoreLocator={true}
                  selectedStore={selectedStore}
                  storeLocatorZoom={
                    selectedStore
                      ? 17
                      : id == "All"
                        ? 5
                        : 10
                  }
                  onStoreSelect={(store: any) => {
                    this.setState({
                      isUserDragging: false,
                      selectedStore: store,
                      zoom: 17,
                    });
                  }}
                  mapCenter={
                    selectedStore
                      ? { lat: selectedStore.lat, lng: selectedStore.lng }
                      : id == "All"
                        ? { lat: 25.03528244630005, lng: 67.1405685813624 }
                        : { lat: initialMapCenter[1], lng: initialMapCenter[0] }
                  }
                  setCurrentLocation={() => { }}
                  onMapDragStart={this.handleMapDragStart}
                  onMapDragEnd={this.handleMapDragEnd}
                  isUserDragging={this.state.isUserDragging}
                />
              ) : (
                  <SimplexMap
                    UiSettings={{
                      showCurrentLocationBlip: false,
                      showGoToCurrentLocationButton: true,
                      primaryColor: "#ea002a",
                      markersSrc: {
                        pin: pin,
                      },
                      mapZoomLevel: zoom,
                      loadingLogo: loader,
                      defaultCoordinates: [67.1405685813624, 25.03528244630005],
                    }}
                    setCurrentMarkerCoordinates={(coordinates: number[]) => {
                      console.log("coordinates[0]", coordinates[0], coordinates[1]);
                    }}
                    setCurrentUserLocationCoordinates={(coordinates: number[]) =>
                      console.log("current user coords ->", coordinates)
                    }
                    setCoordinateString={(searchResult: any) => {
                      console.log("searchResult?.toString()", searchResult?.toString());
                    }}
                    setIsCoordinateStringLoading={(flag: boolean) =>
                      console.log("flag", flag)
                    }
                    popupOpts={{
                      allowClose: false,
                      offset: {
                        x: 0,
                        y: 0,
                      },
                    }}
                    markersToShow={{
                      markerSrc: pin,
                      markers: id == "All" ? defaultMarkers : selectedStoresMarkers,
                    }}
                    popupText={""}
                    initialMapCenter={defaultInitialCenter}
                    apiKey={process.env.REACT_APP_SM_KEY}
                    apiUrl={process.env.REACT_APP_MANARA_MAPS_API_URL}
                    tilesUrl={process.env.REACT_APP_MANARA_MAPS_TILES_URL}
                  />
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
}
const mapStateToProps = (state: any) => {
  return {
    restuarants: state.store.restuarants,
    cities: state.city.cities,
  };
};
const mapDispatchToProps = (dispatch: any) => {
  return {
    getStores: () => {
      dispatch(getStores());
    },
    getCitiesWeb: () => {
      dispatch(getCitiesWeb());
    },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(StoreLocator);
