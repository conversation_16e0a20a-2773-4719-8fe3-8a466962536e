import React, { useState, useCallback, useRef, useEffect } from "react";
import { GoogleMap, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, InfoWindow } from "@react-google-maps/api";
import "./index.css";
import loader from "../../assets/images/domino_loader.gif";
import pin from "../../assets/images/dominos_pin.png";
import storePin from "../../assets/images/domino_marker.png";
import { Location_icon } from "../../components/location_Icon";

interface IMapsProps {
  setSaveAddressSelect?: (val: boolean) => void;
  storesList?: any[];
  setStoreName?: (name: string) => void;
  storeName: string;
  setCoordinatesString: (val: string) => void;
  setIsCoordinatesStringLoading: (val: boolean) => void;
  onSetData: (obj: any) => void;
  setSearchString?: (val: string) => void;
  savedAddresses?: boolean;
  isAddressComp: boolean;
  mapCenter: any;
  setCurrentLocation: () => void;
  findPointedStore?: (lat: number, lng: number) => void;
  showStoreMarkers?: boolean;
  onStoreSelect?: (store: any) => void;
  isStoreLocator?: boolean;
  storeLocatorZoom?: number;
  onMapDragStart?: () => void;
  onMapDragEnd?: () => void;
  isUserDragging?: boolean;
  selectedStore?: any;
}

const GoogleMapComponent = ({
  setSaveAddressSelect,
  storesList = [],
  setStoreName,
  storeName,
  setCoordinatesString,
  setIsCoordinatesStringLoading,
  onSetData,
  isAddressComp,
  setSearchString,
  savedAddresses,
  mapCenter,
  setCurrentLocation,
  findPointedStore,
  showStoreMarkers = false,
  onStoreSelect,
  isStoreLocator = false,
  storeLocatorZoom = 17,
  onMapDragStart,
  onMapDragEnd,
  isUserDragging = false,
  selectedStore,
}: IMapsProps) => {
  const [infoVisible, setInfoVisible] = useState(true);
  const [zoom, setZoom] = useState(isStoreLocator ? storeLocatorZoom : 17);
  const [isDragging, setIsDragging] = useState(false);
  const [isUpdatingFromParent, setIsUpdatingFromParent] = useState(false);
  const [userHasDragged, setUserHasDragged] = useState(isUserDragging);
  const mapRef = useRef<google.maps.Map | null>(null);
  const lastUpdateRef = useRef<{ lat: number; lng: number } | null>(null);
  const containerStyle = {
    width: "100%",
    height: isStoreLocator ? "45.8rem" : "30rem",
  };

  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: process.env.REACT_APP_GOOGLE_MAPS_API_KEY || "",
    libraries: ["places"],
  });

  const mapOptions = {
    zoomControl: true,
    mapTypeControl: false,
    streetViewControl: false,
    fullscreenControl: false,
    clickableIcons: false,
  };

  const setSelectedLocation = (center: any, shouldUpdateParent = true) => {
    setIsCoordinatesStringLoading(true);
    const geocoder = new google.maps.Geocoder();
    geocoder.geocode(
      { location: center },
      (results: any, status) => {
        setIsCoordinatesStringLoading(false);
        if (status === "OK" && results[0]) {
          setCoordinatesString(results[0].formatted_address);
          if (setSearchString) setSearchString(results[0].formatted_address);
          if (shouldUpdateParent) {
            lastUpdateRef.current = { lat: center.lat, lng: center.lng };
            const obj = {
              lat: center.lat,
              lng: center.lng,
              address: results[0].formatted_address,
            };
            onSetData(obj);
          }
        } else {
          setCoordinatesString("404");
          if (setStoreName) setStoreName("No Store Found");
          if (setSearchString) setSearchString("");
          console.error("Geocoding failed:", status);
        }
      }
    );
  };

  const handleDragStart = useCallback(() => {
    setUserHasDragged(true);
    if (isStoreLocator) {
      setInfoVisible(false);
    }
    if (onMapDragStart) onMapDragStart();
  }, [onMapDragStart]);

  const handleDragEnd = useCallback(() => {
    if (mapRef.current && !isUpdatingFromParent) {
      const newCenter = mapRef.current.getCenter();
      const lat = newCenter?.lat();
      const lng = newCenter?.lng();
      if (lat && lng) {
        if (showStoreMarkers) {
          lastUpdateRef.current = { lat, lng };
          onSetData({ lat, lng });
        } else {
          if (findPointedStore) {
            findPointedStore(lat, lng);
          }
          setSelectedLocation({ lat, lng }, true);
        }
      }
    }
  }, [findPointedStore, isUpdatingFromParent, showStoreMarkers, onSetData, onMapDragEnd]);

  const handleZoomChanged = useCallback(() => {
     if (isStoreLocator) {
      setInfoVisible(false);
    }
    if (mapRef.current && !isDragging && !isUpdatingFromParent) {
      const newCenter = mapRef.current.getCenter();
      const lat = newCenter?.lat();
      const lng = newCenter?.lng();
      if (lat !== undefined && lng !== undefined) {
        if (showStoreMarkers) {
          lastUpdateRef.current = { lat, lng };
        } else {
          setSelectedLocation({ lat, lng }, false);
        }
      }
    }
  }, [isDragging, isUpdatingFromParent, showStoreMarkers]);

  const onLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
    if (mapCenter) {
      map.setCenter({ lat: Number(mapCenter.lat), lng: Number(mapCenter.lng) });
      if (isStoreLocator) {
        map.setZoom(storeLocatorZoom);
      } else {
        setSelectedLocation(mapCenter);
      }
    } else {
      setCurrentLocation();
    }
  }, [mapCenter, isStoreLocator, storeLocatorZoom, setCurrentLocation]);

  useEffect(() => {
    if (isStoreLocator && mapRef.current) {
      setZoom(storeLocatorZoom);
      mapRef.current.setZoom(storeLocatorZoom);
    }
  }, [isStoreLocator, storeLocatorZoom]);
  useEffect(() => {
    if (isStoreLocator && selectedStore) {
      setInfoVisible(true);
    } else if (isStoreLocator && !selectedStore) {
      setInfoVisible(false);
    }
  }, [isStoreLocator, selectedStore]);
  useEffect(() => {
    if (mapRef.current && mapCenter && !isUserDragging) {
      const lat = Number(mapCenter.lat);
      const lng = Number(mapCenter.lng);

      // Check if coordinates actually changed
      if (lastUpdateRef.current) {
        const threshold = showStoreMarkers ? 0.001 : 0.0001;
        if (Math.abs(lastUpdateRef.current.lat - lat) < threshold &&
          Math.abs(lastUpdateRef.current.lng - lng) < threshold) {
          return;
        }
      }

      if (typeof lat === 'number' && typeof lng === 'number') {
        setIsUpdatingFromParent(true);
        mapRef.current.panTo({ lat, lng });
        lastUpdateRef.current = { lat, lng };
        if (showStoreMarkers) {
          setUserHasDragged(false);
        }

        setTimeout(() => setIsUpdatingFromParent(false), 200);
      }
    }
  }, [mapCenter, isUserDragging, showStoreMarkers]);

  const handleMyLocation = () => {
    if (navigator.geolocation) {
      setIsCoordinatesStringLoading(true);
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          if (mapRef.current) {
            mapRef.current.panTo({ lat: latitude, lng: longitude });
            mapRef.current?.setOptions({ zoom: 17 });
            setSelectedLocation({ lat: latitude, lng: longitude });
            setZoom(17);
          }
        },
        (error) => {
          setIsCoordinatesStringLoading(false);
          alert("Unable to retrieve your location.");
        }
      );
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  };

  if (!isLoaded) {
    return (
      <div style={{ width: "100%", height: "300px", display: "flex", alignItems: "center", justifyContent: "center" }}>
        <img src={loader} alt="Loading map..." style={{ width: 80, height: 80 }} />
      </div>
    );
  }

  return (
    <div style={{ position: "relative" }}>
      <GoogleMap
        mapContainerStyle={containerStyle}
        zoom={zoom}
        onLoad={onLoad}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onZoomChanged={handleZoomChanged}
        options={mapOptions}
      >
        {showStoreMarkers && storesList.map((store: any, index: number) => (
          <Marker
            key={index}
            position={{ lat: Number(store.lat), lng: Number(store.lng) }}
            icon={{
              url: storePin,
              scaledSize: new google.maps.Size(40, 40),
            }}
            onClick={() => {
              setTimeout(() => {
                setInfoVisible(!infoVisible);
              }, 100);
              if (onStoreSelect) {
                onStoreSelect(store);
              }
            }}
          />
        ))}

        {showStoreMarkers && selectedStore && infoVisible && (
          <div
            className={isAddressComp ? "infoWindowContentMarkerForMyAddressesPage" : "infoWindowContentMarker"}
            style={{
              position: 'absolute',
              top: '43%',
              left: '50%',
              transform: 'translate(-50%, -100%)',
              zIndex: 1000,
              pointerEvents: 'auto'
            }}
            onClick={() => {
              setInfoVisible(!infoVisible);
              if (onStoreSelect) {
                onStoreSelect(null);
              }
            }}
          >
            <span>{selectedStore.store_name}</span>
          </div>
        )}

        {!showStoreMarkers && (
          <>
            <div className={isAddressComp ? "locateButton locateButtonContainerForEditAddress" : "locateButton locateButtonContainer"}>
              <button
                type="button"
                className={"btn btn-primary " + (isAddressComp ? "locateButtonContainerForEditAddress" : "locateButtonContainer")}
                onClick={handleMyLocation}
              >
                Locate me
                <span style={{ marginRight: "8px" }}>
                  <Location_icon />
                </span>
              </button>
            </div>
            {storeName && (
              <div className="storeNameBadge">
                <span>STORE: {storeName}</span>
              </div>
            )}
            <div
              className="pinIcon"
              onClick={() => setInfoVisible(!infoVisible)}
            >
              <img
                src={pin}
                alt="Center Marker"
                style={{ height: "40px" }}
              />
            </div>
            {infoVisible && (
              <div className={isAddressComp ? "infoWindowContentMarkerForMyAddressesPage" : "infoWindowContentMarker"}>
                <span>Is this your current location?</span>
              </div>
            )}
          </>
        )}
      </GoogleMap>
    </div>
  );
};

export default GoogleMapComponent;
