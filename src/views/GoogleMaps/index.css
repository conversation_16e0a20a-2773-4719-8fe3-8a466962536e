.pinIcon {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 1;
  cursor: pointer;
  transform: translate(-50%, -100%);
}

.address-suggestion {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 100;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  max-height: 250px;
  overflow-y: auto;
}



.suggestion-item {
  padding: 12px;
  display: grid;
  cursor: pointer;
  border-bottom: 1px solid #eaeaea;
}

.suggestion-item:hover {
  background-color: var(--blue);
}

.locateButton {
  position: absolute;
  top: -100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.storeNameBadge {
  padding: 0.5rem;
  position: absolute;
  top: 78px;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--maps-primary, #dc3545);
  border-radius: 0.375rem;
  color: white;
  width: auto;
  white-space: nowrap;
}

.infoWindowContentMarker {
  background-color: #000;
  color: white;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  font-family: Poppins, sans-serif;
  position: absolute;
  top: 113px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 12px;
  box-shadow: 0 3px 14px #0006;
  white-space: normal;
  width: 160px;
  text-align: center;
  word-wrap: break-word;
}

.infoWindowContentMarkerForMyAddressesPage {
  background-color: #000;
  color: white;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  font-family: Poppins, sans-serif;
  position: absolute;
  top: 124px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 12px;
  box-shadow: 0 3px 14px #0006;
  white-space: normal;
  width: 160px;
  text-align: center;
  word-wrap: break-word;
}

.infoWindowContentMarker::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #000;
}

.infoWindowContentMarkerForMyAddressesPage::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #000;
}

.locateButtonContainer {
  align-items: center;
  background-color: var(--blue);
  border-color: var(--blue);
  border-radius: 5px;
  color: #fff;
  flex-direction: row-reverse;
  font-size: 16px;
  top: 15px !important;
  display: flex;

}

.locateButtonContainer:hover {
  align-items: center;
  background-color: var(--blue);
  border-radius: 5px;
  color: #fff;
  flex-direction: row-reverse;
  font-size: 16px;
  border-color: var(--blue);
  display: flex;
}

.locateButtonContainerForEditAddress {
  align-items: center;
  background-color: var(--blue);
  border-color: var(--blue);
  border-radius: 5px;
  color: #fff;
  flex-direction: row-reverse;
  font-size: 16px;
  top: 77px !important;
  display: flex;

}

.locateButtonContainerForEditAddress:hover {
  align-items: center;
  background-color: var(--blue);
  border-radius: 5px;
  color: #fff;
  flex-direction: row-reverse;
  font-size: 16px;
  border-color: var(--blue);
  display: flex;
}

.searchBarContainer {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 8px;
}

.searchInput {
  border: none;
  outline: none;
  font-size: 16px;
  padding: 12px 40px 12px 16px;
  border-radius: 8px;
}

.searchInput:focus {
  box-shadow: none;
  border: none;
}

.searchClearBtn {
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  padding: 0;
  z-index: 101;
}

.searchIconBtn {
  background: none;
  border: none;
  padding: 8px;
  color: #007bff;
  font-size: 18px;
}
.gm-style .gm-style-iw-c {
  background-color: #000 !important;
  padding: 6px 10px !important;
  border-radius: 4px !important;
  max-width: none !important;
  overflow: hidden !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
}

/* Style the content inside */
.custom-infowindow {
  color: white;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  border-radius: 12px;
}

.gm-style .gm-style-iw-tc::after {
  background-color: black;
}