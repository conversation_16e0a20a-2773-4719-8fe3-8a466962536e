import { useEffect, useState } from 'react';
import LocationMap from './newLocation';
import { Form } from 'react-bootstrap';
import { BsFillPlusCircleFill } from "react-icons/bs";
import { connect } from 'react-redux';
import { getDeliveryStores } from '../../redux';
import { addAddressUser, addressesList } from '../../redux/actions/login';
import { toast } from 'react-toastify';
import moment from 'moment';
import pin from "../../assets/images/dominos_pin.png";
import mergn from 'mergn-webapp-sdk'
import useLocationEnabled from '../../hooks/useLocationEnabled';
import * as geolib from 'geolib';
import loader from '../../assets/images/domino_loader.gif'
import GoogleMapComponent from '../GoogleMaps/googleMaps';
import "./index.css"
import api from '../../components/api';

function DeliveryForm(props: any) {
  let token = localStorage.getItem("userToken")
  const [showAddresses, showSavedAddress] = useState(false)
  const [changeLocation, setChangeLocation] = useState(false)
  const [savedAddresses, setSaveAddressData] = useState<any>([])
  const [selectedAddress, setAddressData]: any = useState(null);
  const [storeName, setStoreName] = useState("");
  const [validTimeFlag, setValidTimeFlag] = useState(false);
  const [coordinatesString, setCoordinatesString] = useState<string>("");
  const [searchString, setSearchString] = useState("");
  const [placesSuggestion, setPlacesSuggestion] = useState([]);
  const [brandDetails, setBrandDetails] = useState<any>(null);
  // const [locationConfirmed, setLocationConfirmed] = useState<boolean>(false);
  // const [deliveryLat, setDeliveryLat] = useState(0);
  // const [deliveryLng, setDeliveryLng] = useState(0);
  const [isCoordinatesStringLoading, setIsCoordinatesStringLoading] =
    useState<boolean>(false);
  useEffect(() => {
    props.getDeliveryStores()
    props.addressesList()
    fetchBrandDetails()
    let windowObj = (window as any)
    windowObj.dataLayer.push({ desc: null });
    windowObj.dataLayer.push({
      event: 'screenview_location',
      desc: "Open Location Dialog"
    });
  }, [])
  useEffect(() => {
    if (props.addressData && props.addressData.length > 0) {
      let defaultAddress = props.addressData.filter((obj: any) => { return obj.is_default == 1 });
      if (defaultAddress && defaultAddress.length > 0) {
        setTimeout(() => {
          handleOnChange(defaultAddress[0])
          props.setSaveAddressSelect(true)
        }, 1000)
      }
      let index = props.addressData.findIndex((obj: any, index: any) => {
        return obj.is_default == 1
      });

      if (index !== -1) {
        const shiftedElement = props.addressData.splice(index, 1)[0];
        props.addressData.unshift(shiftedElement);
      }
    }
    props.setOrderTime("now")
    setSaveAddressData(props.addressData)
  }, [props.addressData])
  const handleMapData = (obj: any) => {
    // props.setSearchString(obj.address)
    props.setLat(obj.lat)
    props.setLng(obj.lng)
    setCoordinatesString(obj.address)
    let data: any = {
      lat: obj.lat,
      lng: obj.lng
    }
    findPointedStore(obj.lat, obj.lng);
    // setDeliveryLat(obj.lat)
    // setDeliveryLng(obj.lng)
    props.setPosition(data);
    setIsCoordinatesStringLoading(false)
  };
  const fetchBrandDetails = async () => {
    const res: any = await api.get(
      `customer/brand-config/${Number(1)}`
    );
    if (!res.data.success) {
      return;
    }
    setBrandDetails(res.data.data);
  }
  const findPointedStore = (lat: any, lng: any) => {
    let data = [];
    for (let i = 0; i < props.deliveryStoreList.length; i++) {
      if (props.deliveryStoreList[i].trade_zone_id.zone_json) {
        if (
          geolib.isPointInPolygon(
            {
              latitude: lat,
              longitude: lng,
            },
            JSON.parse(props.deliveryStoreList[i].trade_zone_id.zone_json),
          )
        ) {
          data.push(props.deliveryStoreList[i]);
        }
      }
    }
    if (data.length > 0) {
      setStoreName(data[0].store_name);
    } else if (props.deliveryStoreList && props.deliveryStoreList.length > 0) {
      setStoreName("");
    }
  };
  const deliveryOrderReady = () => {
    let data: any = [];
    for (let i = 0; i < props.deliveryStoreList.length; i++) {
      if (props.deliveryStoreList[i].trade_zone_id.zone_json) {
        if (geolib.isPointInPolygon({ latitude: props.lat, longitude: props.lng }, JSON.parse(props.deliveryStoreList[i].trade_zone_id.zone_json))) {
          data.push(props.deliveryStoreList[i]);
        }
      }
    }
    if (props.orderTime == "now") {
      if (props.saveAddressCheck) {
        return (data.length > 0 && props.searchString && props.searchString !== "" && props.lat !== "" && props.lng !== "" && props.orderTime !== "" && props.extra_details !== "" && props.street_name !== undefined && props.street_name !== "" && props.street_number !== undefined && props.street_number !== "" && props.place !== "")
      } else {
        return (data.length > 0 && props.searchString && props.searchString !== "" && props.lat !== "" && props.lng !== "" && props.orderTime !== "" && props.extra_details !== "" && props.street_name !== undefined && props.street_name !== "" && props.street_number !== undefined && props.street_number !== "")
      }
    } else {
      if (props.saveAddressCheck) {
        return (data.length > 0 && props.searchString && props.searchString !== "" && props.lat !== "" && props.lng !== "" && props.orderTime == "later" && props.extra_details !== "" && props.street_name !== undefined && props.street_name !== "" && props.street_number !== undefined && props.street_number !== "" && props.laterdatetime !== "" && validTimeFlag && props.place !== "")
      } else {
        return (data.length > 0 && props.searchString && props.searchString !== "" && props.lat !== "" && props.lng !== "" && props.orderTime == "later" && props.extra_details !== "" && props.street_name !== undefined && props.street_name !== "" && props.street_number !== undefined && props.street_number !== "" && props.laterdatetime !== "" && validTimeFlag)
      }
    }
  }
  const handlePlace = (e: any) => {
    props.setPlace(e.target.value)
  }

  const handleOnChange = (obj: any) => {
    setIsCoordinatesStringLoading(false);
    setAddressData(obj)
    props.setExtraDetails(obj.extra_details)
    props.setStreetName(obj.street_name)
    props.setStreetNumber(obj.street_number)
    props.setSearchString(obj.full_address)
    props.setLat(obj.lat)
    props.setLng(obj.lng)
    if (brandDetails && brandDetails.is_google_maps_enable == "0") {
      let data: any = {
      lat: Number(obj.lat),
      lng: Number(obj.lng),
      address: obj.full_address
    }
    handleMapData(data)
    props.setPosition(data);
    }
    let doc: any = document.getElementById('txtSearch');
    if (doc) {
      doc.value = obj.full_address;
    }
    setChangeLocation(false)
  }
  const handleValidTime = (event: { target: { name: any; value: any } }) => {
    props.setLaterDate(event.target.value)

    let validMinDateTime = new Date();
    validMinDateTime.setHours(validMinDateTime.getHours() + 1);
    let futureDate = moment(event.target.value).startOf('day')
    let currentDate = moment(new Date()).startOf('day');

    if (futureDate.isSame(currentDate)) {
      let futureDatetime = new Date(event.target.value)
      if (futureDatetime >= validMinDateTime) {
        setValidTimeFlag(true)
      } else {
        // toast.error("Time should only be visibile after an hour for this section ");
        setValidTimeFlag(false)
      }
    } else {
      setValidTimeFlag(true)
    }

  }
  const blockInvalidChar = (e: any) => {
    ["+", "/", "\\"].includes(e.key) && e.preventDefault();
  }

  const blockInvalidCharForName = (e: any) => {
    ["@", "+", "-", "=", "/", "!", ">", "<", "%", "&", "*", "(", ")", "_", "{", "}", "[", "]", "|", ":", ";", "?", "#", "$", "^", "\\"].includes(e.key) && e.preventDefault();
  }
  const setCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const center = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          const geocoder = new google.maps.Geocoder();
          geocoder.geocode(
            { location: center },
            (results: any, status) => {
              if (status === "OK" && results[0]) {
                const obj = {
                  lat: center.lat,
                  lng: center.lng,
                  address: results[0].formatted_address,
                };
                handleMapData(obj);
                props.setSearchString(results[0].formatted_address);
                props.setExtraDetails("");
                props.setStreetName("");
                props.setStreetNumber("");
                props.setPlace("");
                props.setOrderTime("now");
              } else {
                console.error("Geocoding failed:", status);
              }
            },
          );
        },
        (error) => {
        },
        { maximumAge: 20000, timeout: 20000, enableHighAccuracy: false },
      );
    } else {
      console.error("Geocoding failed:");
    }
  };

  const handLeChangeLocation = () => {
    if ("geolocation" in navigator) {
      mergn.recordEvent('Enable your location')
      let windowObj = (window as any)
      windowObj.dataLayer.push({ desc: null });
      windowObj.dataLayer.push({
        event: 'screenview_location',
        desc: "Open Location Dialog"
      });
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const obj = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          props.setPosition(obj);
          props.setExtraDetails("");
          props.setStreetName("");
          props.setStreetNumber("");
          let windowObj = (window as any)
          windowObj.dataLayer.push({ desc: null });
          windowObj.dataLayer.push({
            event: 'change_location_clicked',
            desc: "Allow location Access"
          });
        },
        (error) => {
          if (error.code === error.PERMISSION_DENIED) {
            let windowObj = (window as any)
            windowObj.dataLayer.push({ desc: null });
            windowObj.dataLayer.push({
              event: 'change_location_clicked',
              desc: "Deny location Access"
            });
          }
        }
      );
    }

    setTimeout(() => {
      setChangeLocation(prev => !prev);
    }, 50);
    setIsCoordinatesStringLoading(false);
  };

  // const confirmLocationReady = () => {
  // 	let data: any = [];
  // 	for (let i = 0; i < props.storesList.length; i++) {
  // 		if (props.storesList[i].trade_zone_id.zone_json) {
  // 			if (geolib.isPointInPolygon({ latitude: deliveryLat, longitude: deliveryLng }, JSON.parse(props.storesList[i].trade_zone_id.zone_json))) {
  // 				data.push(props.storesList[i]);
  // 			}
  // 		}
  // 	}
  // 	return (
  // 		data.length > 0 &&
  // 		props.position &&
  // 		props.position.lat !== "" &&
  // 		props.position.lng !== "" &&
  // 		props.searchString && props.searchString !== ""
  // 	);
  // };

  // const confirmLocationCheck = confirmLocationReady()

  // useEffect(()=> {
  //   setLocationConfirmed(false)
  // }, [deliveryLat, deliveryLng])

  const isLocationEnabled: boolean = useLocationEnabled();
  useEffect(() => {
    if (!isLocationEnabled) {
      toast.error("Kindly enable your current location from the browser ");
    }
  }, [isLocationEnabled]);

  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      if (searchString.length > 3 && searchString.length < 50) {
        api.get(`customer/autoComplete/${searchString}`)
          .then((res: any) => {
            setPlacesSuggestion(res.data.data);
          })
          .catch((err: any) => {
            alert("something went wrong!!!");
          });
      } else {
        setPlacesSuggestion([]);
      }
    }, 500);
    return () => clearTimeout(delayDebounce);
  }, [searchString]);

  const handleInputChange = (event: any) => {
    const queryString = event.target.value;
    setSearchString(queryString);
  };
  const onPlaceClick = (place: any) => {
    const geocoder = new google.maps.Geocoder();
    geocoder
      .geocode({ placeId: place.place_id })
      .then(async (response: any) => {
        if (response.results && response.results.length > 0) {
          const location = response.results[0].geometry.location;
          const obj = {
            lat: location.lat(),
            lng: location.lng(),
            address: place.description,
          };
          handleMapData(obj);
          setSearchString("");
          setPlacesSuggestion([]);
        } else {
          console.error("No results found");
        }
      })
      .catch((error: any) => {
        console.error("Geocode error:", error);
      });
  };
  const removeSearchString = () => {
    setSearchString("");
    setPlacesSuggestion([]);
  };
  let todayDateTime = new Date();
  todayDateTime.setSeconds(0, 0);
  let m = moment(todayDateTime)

  //Today date
  let momentDate = m.format()
  let momentUpdate = momentDate.split("+")[0]
  let today = momentUpdate
  //Max Date
  let maxMomentDate = m.add(1, "days")
  let maxMomentDateUpdate = maxMomentDate.format().split("+")[0]
  let maxDate = maxMomentDateUpdate
  return (
    <div className='form-delevery pb-0'>
      {
        (savedAddresses && savedAddresses.length > 0) && <h6 onClick={() => showSavedAddress(!showAddresses)} className='f-w-700 mb-0'>Saved Addresses
          {/* {!showAddresses ? <FaChevronDown style={{ fontSize: "15px" }}></FaChevronDown> : 
          <FaChevronDown style={{ fontSize: "15px" }}></FaChevronDown> 
          }  */}
        </h6>
      }
      {savedAddresses && savedAddresses.length > 0 &&
        <div className='address-saves'>
          {savedAddresses.map((obj: any, index: any) => {
            return (
              <div className='add-address-location cursor-pointer' onClick={() => {
                props.setSaveAddressSelect(true)
                handleOnChange(obj)
                window.scrollTo(0, 0)
              }}>
                <Form>
                  {['radio'].map((type) => (
                    <>
                      <div key={`inline-${type}-${index}`}>
                        <Form.Check
                          checked={selectedAddress && selectedAddress.address_id == obj.address_id}
                          label=""

                          name="address"
                          type="radio" aria-label={`addressradio ${index}`}
                          id={`addressinline-${type}-${index}`}
                          className="payment-radio font-weight-bold" />
                      </div>
                    </>
                  ))}

                </Form>
                <div className='save-address-content'>
                  <strong>{obj.place}&nbsp;{obj.is_default == 1 ? "(Default)" : ""}</strong>
                  <p className='delevery-check'>{`${obj.street_number}  ${obj.street_name}  ${obj.extra_details}`}</p>
                  <p className='delevery-check'>{obj.full_address}</p>
                </div>
              </div>
            );
          }
          )}
        </div>
      }
      {(token && savedAddresses.length > 0) &&
        <h6 onClick={() => {
          props.setSaveAddressSelect(false);
          handLeChangeLocation();
          setTimeout(() => {
            let doc = document as any;
            if (doc) {
              const btn: any = doc.querySelector(".go-to-location-wrapper")?.querySelector("button");
              if (btn && !btn?.disabled) btn?.click()
            }
          }, 500)
        }} className='f-w-500 p-1 text-center mb-0 mt-2 addAddress' style={{ cursor: "pointer", display: "flex", justifyContent: "center" }}>
          {!changeLocation ? <BsFillPlusCircleFill className='fontSize-28' /> :
            // <FaPlus style={{ fontSize: "18px", paddingRight: "5px" }}></FaPlus>
            <BsFillPlusCircleFill className='fontSize-28' />
          }
          <span style={{ fontSize: "16px", }}> New Address</span>
        </h6>
      }
      <div className="pb-3 mt-0">
        {(changeLocation || !token || savedAddresses.length == 0) && <div>

          <fieldset className="d-fieldset">
            <legend className="d-legend f-w-700 pb-2">Enter Full Address</legend>

            {brandDetails && brandDetails.is_google_maps_enable == "1" && <div className="form-group position-relative">
              <div className="d-flex align-items-center mb-2">
                <div className="flex-grow-1">
                  <input
                    className={`form-control ${searchString === "" ? "delivery-location-field" : ""}`}
                    type="text"
                    placeholder="Enter your full address"
                    value={searchString}
                    onChange={handleInputChange}
                  />
                  <span className="focus-border"></span>

                  {searchString?.length > 0 && (
                    <button
                      type="button"
                      className="btn  position-absolute"
                      style={{
                        right: '12px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        padding: 0,
                        zIndex: 101
                      }}
                      onClick={removeSearchString}
                    >
                      <i className="ri-close-fill text-danger" style={{ fontSize: "1.5rem" }}></i>
                    </button>
                  )}
                </div>

                {/* <button
                  type="button"
                  className="btn btn-primary ms-2"
                  onClick={setCurrentLocation}
                  style={{ backgroundColor: "rgb(0,105,145)" }}
                >
                  <i className="ri-focus-3-fill"></i>
                </button> */}
              </div>

              {searchString !== "" && placesSuggestion.length > 0 && (
                <div className="address-suggestion w-100">
                  <ul className="list-group mb-0">
                    {placesSuggestion.map((place: any, idx: number) => (
                      <li
                        key={idx}
                        onClick={() => onPlaceClick(place)}
                        className="list-group-item suggestion-item"
                        style={{ cursor: "pointer" }}
                      >
                        <span className="fw-bold">{place.structured_formatting.main_text}</span>
                        <br />
                        <small className="text-muted">{place.structured_formatting.secondary_text}</small>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>}
            {props.deliveryStoreList && props.deliveryStoreList.length > 0 ? (
              <div>
                {brandDetails && brandDetails.is_google_maps_enable == "0" ? <LocationMap setSaveAddressSelect={props.setSaveAddressSelect} storesList={props.deliveryStoreList} setStoreName={setStoreName} storeName={storeName} setCoordinatesString={setCoordinatesString} setIsCoordinatesStringLoading={setIsCoordinatesStringLoading} onSetData={handleMapData} setSearchString={props.setSearchString} savedAddresses={savedAddresses.length > 0 ? true : false} /> :
                  <GoogleMapComponent
                    setSaveAddressSelect={props.setSaveAddressSelect}
                    storesList={props.deliveryStoreList}
                    setStoreName={setStoreName}
                    storeName={storeName}
                    isAddressComp={false}
                    setCoordinatesString={setCoordinatesString}
                    setIsCoordinatesStringLoading={setIsCoordinatesStringLoading}
                    onSetData={handleMapData}
                    setSearchString={props.setSearchString}
                    savedAddresses={savedAddresses.length > 0}
                    mapCenter={props.position}
                    setCurrentLocation={setCurrentLocation}
                    findPointedStore={findPointedStore}
                  />}
              </div>
            ) : (
              <div className="d-flex justify-content-center align-items-center" style={{ height: "40vh" }}>
                <img alt="Dominos" style={{ width: "100px", height: "100px" }} src={loader} />
              </div>
            )}
          </fieldset>

          <div className='d-flex mx-2 my-3 align-items-center'>
            <div className="col-1">
              <img
                width={25}
                height={25}
                src={pin}
                id="address-marker"
                alt=""
              />
            </div>
            <div className="col-11">
              <h5 className='address-string'>
                {isCoordinatesStringLoading
                  ? "Loading..."
                  : isLocationEnabled
                    ? coordinatesString === "404" ? "Address not found" : coordinatesString
                    : coordinatesString !== ""
                      ? coordinatesString === "404" ? "Address not found" : coordinatesString
                      : "Kindly enable your location or use Search Location"}
              </h5>
            </div>
          </div>
          {/* {
            !locationConfirmed ? 
            <div>
              <button 
                className='confirm-location-btn' 
                disabled={!confirmLocationCheck}
                onClick={()=> {
                  setLocationConfirmed(true)
                }}
                style={{ cursor: confirmLocationCheck ? "pointer" : "no-drop", backgroundColor: confirmLocationCheck ? "rgb(0,105,145)" : "rgb(128, 128, 128)" }}
              >
                Confirm Location
              </button>
            </div> 
            : 
            null
          } */}
          {/* {
            locationConfirmed ? */}
          <div className='d-flex flex-col gap-2 my-3'>
            <div>
              <div className='col-12'>
                <input placeholder={"Home/ Apartment / Office / Plot number"} className="col-12 form-control custom-input" maxLength={50} style={{ margin: "auto" }} autoComplete="off" id="divSearch" value={props.extra_details} onKeyDown={blockInvalidChar}
                  onChange={(e) => {
                    var inputValue = e.target.value.trimStart()
                    e.target.value = inputValue
                    props.setExtraDetails(e.target.value)
                  }} />
              </div>
            </div>
            <div className='d-flex gap-2'>
              <div className='col'>
                <input placeholder={"Street / Building / Landmark"} className="col-6 form-control custom-input" style={{ margin: "auto" }} autoComplete="off" id="divSearch" value={props.street_name} onKeyDown={blockInvalidCharForName}
                  onChange={(e) => {
                    var inputValue = e.target.value.trimStart()
                    e.target.value = inputValue
                    props.setStreetName(e.target.value)
                  }} />
              </div>
              <div className='col'>
                <input placeholder={"Sector / Block / Phase"} className="col-6 form-control custom-input" style={{ margin: "auto" }} autoComplete="off" id="divSearch" value={props.street_number} onKeyDown={blockInvalidCharForName}
                  onChange={(e) => {
                    var inputValue = e.target.value.trimStart()
                    e.target.value = inputValue
                    props.setStreetNumber(e.target.value)
                  }} />
              </div>
            </div>
          </div>
          {/* :
            null
          } */}
          {token &&

            <><div className='mt-3'>
              {(props.saveAddressCheck) &&
                <div className="form-group mt-3 mb-2">

                  <select
                    value={props.place}
                    style={{ height: '3rem', paddingTop: '0rem', paddingBottom: '0rem', fontSize: '15px' }}
                    id="round"
                    className="form-control custom-input"
                    required
                    data-msg="Select Address Name"
                    onChange={handlePlace}
                  >
                    <option style={{ fontSize: "15px" }} value="">Select your address name</option>
                    <option selected={props.place == "Home"} style={{ fontSize: "15px" }} value="Home">Home</option>
                    <option selected={props.place == "Office"} style={{ fontSize: "15px" }} value="Office">Office</option>
                    <option selected={props.place == "Other"} style={{ fontSize: "15px" }} value="Other">Other</option>

                  </select>
                </div>
              }
            </div>
              <Form>
                {['radio'].map((type) => (
                  <div key={`inline-${type}`} className="mb-3 d-grid">
                    <Form.Check
                      inline
                      label="Save this address"
                      onClick={() => props.setSaveAddress(!props.saveAddressCheck)}
                      checked={props.saveAddressCheck}
                      type="checkbox" aria-label="radio 3"
                      id={`inline-${type}-55`}
                      className="payment-radio mt-2"
                    />

                  </div>
                ))}

              </Form>

            </>
          }
        </div>
        }

        <div className="mt-3 deliverLater">
          <>
            <h5 className='f-w-700'>When do you want your order ?</h5>
            <Form>
              {['radio'].map((type) => (
                <div key={`inline-${type}`} className="mb-3 d-grid">
                  <Form.Check
                    inline
                    label="Deliver Now"
                    onClick={() => props.setOrderTime("now")}
                    name="group2"
                    type="radio" aria-label="radio 3"
                    checked={props.orderTime == "now"}
                    id={`inline-${type}-3`}
                    className="payment-radio mt-2"
                  />
                  <Form.Check
                    inline
                    onClick={() => props.setOrderTime("later")}
                    label="Deliver Later"
                    name="group2"
                    type="radio" aria-label="radio 4"
                    checked={props.orderTime == "later"}
                    id={`inline-${type}-4`}
                    className="payment-radio"
                  />
                </div>
              ))}
              {props.orderTime == "later" && <Form.Group className="" controlId="test-date-input">
                <input id='round'
                  style={{ fontSize: "15px", color: "#000" }}
                  type="datetime-local"
                  min={today}
                  max={maxDate}
                  value={props.laterdatetime}
                  onChange={handleValidTime}
                  className="form-control custom-input datetime-input"
                  placeholder="dd/mm/yyyy, --:--"
                  required
                />
              </Form.Group>
              }
            </Form>
          </>
        </div>
        {/* <Link to="/menu" className='skip-for-menu'>Skip For Now</Link> */}
        {(props.addressLoad) ?
          <button disabled={props.addressLoad} className='login-btn start-btn index-btn' style={{ zIndex: 9999, cursor: !props.addressLoad ? "pointer" : "no-drop", backgroundColor: !props.addressLoad ? "rgb(0,105,145)" : "rgb(128, 128, 128)" }}>Start Order</button> :
          <button
            disabled={!deliveryOrderReady()}
            className='login-btn start-btn index-btn'
            style={{ zIndex: 9999, cursor: deliveryOrderReady() ? "pointer" : "no-drop", backgroundColor: deliveryOrderReady() ? "var(--blue)" : "rgb(128, 128, 128)" }}
            onClick={() => props.confirmLocation()}
          >
            Start Order
          </button>
        }

      </div>
    </div>
  )
}
const mapStateToProps = (state: any) => {
  return {
    addressData: state.login.addressData,
    addressLoad: state.login.addressLoad,
    deliveryStoreList: state.store.deliveryStoreList,
  };
};

const mapDispatchToProps = (dispatch: any) => {
  return {
    getDeliveryStores: () => {
      dispatch(getDeliveryStores())
    },
    addressesList: function () {
      dispatch(addressesList());
    },
    addAddressUser: (data: any) => {
      dispatch(addAddressUser(data))
    },
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(DeliveryForm)


