import {MigrationInterface, QueryRunner} from "typeorm";

export class addBrandsCol1753096783146 implements MigrationInterface {

 public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE brands 
        ADD COLUMN is_google_maps_enable INT DEFAULT 1 `); 
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE brands 
        DROP COLUMN is_google_maps_enable`);
  }
}
