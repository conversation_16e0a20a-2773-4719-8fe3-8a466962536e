import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  combo_timings,
  Days,
  User,
  Customer,
  Order,
  Payments,
  Images,
  Wishlist,
  Roles,
  Store,
  Brand,
  SalesChannel,
  StoreType,
  Country,
  Menu,
  Group,
  MenuItem,
  Combo,
  ModifierGroup,
  ModifierOption,
  Coupon,
  Promo,
  Customer_Feedback,
  Discounts,
  Trade_Zone,
  PushNotification,
  Hero_Item,
  Feedback,
  ItemStoreRelation,
  MenuItemVariants,
  ComboChoices,
  State,
  StoresMenu,
  ActivityLogs,
  Address,
  Franchise,
  UserGroups,
  GroupStores,
  City,
  OrderModes,
  OrderModesPrice,
  ItemModes,
  ModeStoreRelation,
  CouponStores,
  CouponUsageByCustomer,
  SubGroup,
  Banners,
  VariantRelations,
  ComboChoicesMod,
  SuggestiveBaseProducts,
  SuggestiveChildProducts,
  EmpCoupons,
  DiscountStores,
  StoreSpecificPrice,
  CouponCustomerAssign,
  MenuStoreRelation,
  menu_item_timings,
  DeliveryTimings,
  MenuLogs,
  Order_Aggregators,
  SurveryFeedback
} from "entities";
import { AdminService } from "./admin.service";
import { AdminController } from "./admin.controller";
import { BusinessType } from "entities/businessType.entity";
import { BackupStore } from "entities/backup_stores.entity";
import { StoresToken } from "entities/stores_token.entity";
import { TradeZoneModule } from "trade-zone/trade-zone.module";
import { CouponMenus } from "entities/coupon_menus.entity";
import { CouponGroups } from "entities/coupon_groups.entity";
import { MakeAComboChoices } from "entities/make_a_combo_choices.entity";
import { MakeACombo } from "entities/make_a_combo.entity";
import { OrderQuickCombo } from "entities/order_make_a_combo.entity";
import { variants } from "entities/variants.entity";
import { FPDiscounts } from "entities/fp_discounts.entity";
import { CustomerReview } from "entities/customer_review.entity";
import { WorkerService } from "./my-worker.service";
import { LoggingService } from "utils/loggingService.utils";
@Module({
  imports: [
    TypeOrmModule.forFeature([
      combo_timings,
      Order_Aggregators,
      CustomerReview,
      DeliveryTimings,
      menu_item_timings,
      Days,
      User,
      ActivityLogs,
      Images,
      Customer,
      Customer_Feedback,
      Order,
      Payments,
      ComboChoices,
      Wishlist,
      Discounts,
      Store,
      Trade_Zone,
      Roles,
      Brand,
      SalesChannel,
      StoreType,
      variants,
      Country,
      SurveryFeedback,
      Menu,
      MenuStoreRelation,
      Group,
      MenuItem,
      MenuItemVariants,
      Combo,
      FPDiscounts,
      ModifierGroup,
      ModifierOption,
      Coupon,
      Promo,
      PushNotification,
      Hero_Item,
      Feedback,
      Franchise,
      BusinessType,
      ItemStoreRelation,
      StoresMenu,
      State,
      BackupStore,
      StoresToken,
      Address,
      UserGroups,
      GroupStores,
      City,
      OrderModes,
      OrderModesPrice,
      ItemModes,
      ModeStoreRelation,
      CouponStores,
      CouponUsageByCustomer,
      CouponMenus,
      CouponGroups,
      MakeACombo,
      MakeAComboChoices,
      OrderQuickCombo,
      SubGroup,
      Banners,
      VariantRelations,
      ComboChoicesMod,
      SuggestiveBaseProducts,
      SuggestiveChildProducts,
      EmpCoupons,
      DiscountStores,
      StoreSpecificPrice,
      CouponCustomerAssign,
      MenuLogs
    ]),
    TradeZoneModule
  ],
  providers: [AdminService, WorkerService, LoggingService],
  exports: [AdminService, LoggingService],
  controllers: [AdminController]
})
export class AdminModule {}
