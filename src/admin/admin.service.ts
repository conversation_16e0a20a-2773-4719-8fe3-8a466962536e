/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import {
  Repository,
  getManager,
  In,
  MoreThanOrEqual,
  EntityManager
} from "typeorm";
//import { Pagination, PaginationOptionsInterface } from './../paginate';

import * as bcrypt from "bcryptjs";
// import { omit } from 'lodash';

import {
  User,
  Customer,
  Days,
  combo_timings,
  menu_item_timings,
  Order,
  Payments,
  Wishlist,
  Roles,
  Discounts,
  Store,
  Trade_Zone,
  Brand,
  Hero_Item,
  SalesChannel,
  StoreType,
  Country,
  Menu,
  Group,
  MenuItem,
  DeliveryTimings,
  Combo,
  Images,
  ModifierGroup,
  ModifierOption,
  Coupon,
  Promo,
  Customer_Feedback,
  PushNotification,
  Feedback,
  ItemStoreRelation,
  MenuItemVariants,
  ComboChoices,
  State,
  StoresMenu,
  ActivityLogs,
  Address,
  Franchise,
  UserGroups,
  GroupStores,
  City,
  OrderModesPrice,
  OrderModes,
  ItemModes,
  ModeStoreRelation,
  CouponStores,
  CouponUsageByCustomer,
  SubGroup,
  Banners,
  VariantRelations,
  ComboChoicesMod,
  SuggestiveBaseProducts,
  SuggestiveChildProducts,
  EmpCoupons,
  DiscountStores,
  StoreSpecificPrice,
  CouponCustomerAssign,
  MenuStoreRelation,
  MenuLogs,
  Order_Aggregators,
  SurveryFeedback
} from "entities";
import { BusinessType } from "entities/businessType.entity";
import { BackupStore } from "entities/backup_stores.entity";
import { StoresToken } from "entities/stores_token.entity";
import { CouponGroups } from "entities/coupon_groups.entity";
import { CouponMenus } from "entities/coupon_menus.entity";
import { MakeACombo } from "entities/make_a_combo.entity";
import { MakeAComboChoices } from "entities/make_a_combo_choices.entity";
import { OrderQuickCombo } from "entities/order_make_a_combo.entity";
import { variants } from "entities/variants.entity";
import { FPDiscounts } from "entities/fp_discounts.entity";
import { CustomerReview } from "entities/customer_review.entity";
import { EditProfileDto } from "flash/flash.dto";
import { USER_BLOCKED_STATUS_CODE } from "lib/enums";

const SALT_ROUNDS = 10;

@Injectable()
export class AdminService {
  constructor(
    private readonly entityManager: EntityManager,
    @InjectRepository(User) private userRep: Repository<User>,
    @InjectRepository(combo_timings)
    private combo_timeRep: Repository<combo_timings>,
    @InjectRepository(menu_item_timings)
    private menu_timeRep: Repository<menu_item_timings>,
    @InjectRepository(Days) private daysRep: Repository<Days>,
    @InjectRepository(PushNotification)
    private notificationRep: Repository<PushNotification>,
    @InjectRepository(Images) private ImgRep: Repository<Images>,
    @InjectRepository(Customer) private customerRep: Repository<Customer>,
    @InjectRepository(Address) private addressRep: Repository<Address>,
    @InjectRepository(Customer_Feedback)
    private customerFeedbackRep: Repository<Customer_Feedback>,
    @InjectRepository(Order) private orderRep: Repository<Order>,
    @InjectRepository(Wishlist) private wishlistRep: Repository<Wishlist>,
    @InjectRepository(Payments) private paymentRep: Repository<Payments>,
    @InjectRepository(Store) private storeRep: Repository<Store>,
    @InjectRepository(DeliveryTimings)
    private DeliveryTimingsRep: Repository<DeliveryTimings>,
    @InjectRepository(Trade_Zone) private tradezoneRep: Repository<Trade_Zone>,
    @InjectRepository(Menu) private menuRep: Repository<Menu>,
    @InjectRepository(MenuStoreRelation)
    private menuStoreRep: Repository<MenuStoreRelation>,
    @InjectRepository(Group) private groupRep: Repository<Group>,
    @InjectRepository(SubGroup) private subgroupRep: Repository<SubGroup>,
    @InjectRepository(MenuItem) private itemRep: Repository<MenuItem>,
    @InjectRepository(MenuItemVariants)
    private itemvariantRep: Repository<MenuItemVariants>,
    @InjectRepository(Combo) private comboRep: Repository<Combo>,
    @InjectRepository(SuggestiveBaseProducts)
    private suggestive_base_products: Repository<SuggestiveBaseProducts>,
    @InjectRepository(SuggestiveChildProducts)
    private suggestive_child_products: Repository<SuggestiveChildProducts>,
    @InjectRepository(ComboChoices)
    private comboChoiceRep: Repository<ComboChoices>,
    @InjectRepository(ComboChoicesMod)
    private comboChoiceModRep: Repository<ComboChoicesMod>,
    @InjectRepository(Hero_Item) private heroRep: Repository<Hero_Item>,
    @InjectRepository(ModifierGroup)
    private modGroupRep: Repository<ModifierGroup>,
    @InjectRepository(ModifierOption)
    private modRep: Repository<ModifierOption>,
    @InjectRepository(VariantRelations)
    private varRelRep: Repository<VariantRelations>,
    @InjectRepository(Coupon) private couponRep: Repository<Coupon>,
    @InjectRepository(Discounts) private discountRep: Repository<Discounts>,
    @InjectRepository(FPDiscounts)
    private fpdiscountRep: Repository<FPDiscounts>,
    @InjectRepository(DiscountStores)
    private storeDiscountRep: Repository<DiscountStores>,
    @InjectRepository(Promo) private promoRep: Repository<Promo>,
    @InjectRepository(Roles) private roleRep: Repository<Roles>,
    @InjectRepository(Brand) private brandRep: Repository<Brand>,
    @InjectRepository(SalesChannel)
    private saleschannelRep: Repository<SalesChannel>,
    @InjectRepository(variants) private variantsRep: Repository<variants>,
    @InjectRepository(StoreType) private storetypeRep: Repository<StoreType>,
    @InjectRepository(Country) private countryRep: Repository<Country>,
    @InjectRepository(Feedback) private feedbackRep: Repository<Feedback>,
    @InjectRepository(Franchise) private franchiseRep: Repository<Franchise>,
    @InjectRepository(BusinessType)
    private businessTypeRep: Repository<BusinessType>,
    @InjectRepository(ItemStoreRelation)
    private itemStoreRep: Repository<ItemStoreRelation>,
    @InjectRepository(BackupStore)
    private backupStoreRep: Repository<BackupStore>,
    @InjectRepository(StoresToken)
    private storeTokenRep: Repository<StoresToken>,
    @InjectRepository(ActivityLogs)
    private activitylogsRep: Repository<ActivityLogs>,
    @InjectRepository(State) private stateRep: Repository<State>,
    @InjectRepository(StoresMenu) private storeMenuRep: Repository<StoresMenu>,
    @InjectRepository(CouponStores)
    private storeCouponRep: Repository<CouponStores>,
    @InjectRepository(CouponMenus)
    private menuCouponRep: Repository<CouponMenus>,
    @InjectRepository(CouponGroups)
    private groupCouponRep: Repository<CouponGroups>,
    @InjectRepository(CouponUsageByCustomer)
    private customerCouponRep: Repository<CouponUsageByCustomer>,
    @InjectRepository(UserGroups) private userGroupsRep: Repository<UserGroups>,
    @InjectRepository(GroupStores)
    private groupStoreRep: Repository<GroupStores>,
    @InjectRepository(City) private cityRep: Repository<City>,
    @InjectRepository(OrderModesPrice)
    private orderModePriceRep: Repository<OrderModesPrice>,
    @InjectRepository(OrderModes) private orderModeRep: Repository<OrderModes>,
    @InjectRepository(ItemModes) private itemModesRep: Repository<ItemModes>,
    @InjectRepository(ModeStoreRelation)
    private modeStoreRep: Repository<ModeStoreRelation>,
    @InjectRepository(MakeACombo) private makeAComboRep: Repository<MakeACombo>,
    @InjectRepository(MakeAComboChoices)
    private makeAComboChoiceRep: Repository<MakeAComboChoices>,
    @InjectRepository(OrderQuickCombo)
    private orderQuickComboRep: Repository<OrderQuickCombo>,
    @InjectRepository(Banners) private bannersRep: Repository<Banners>,
    @InjectRepository(EmpCoupons) private empCouponRep: Repository<EmpCoupons>,
    @InjectRepository(StoreSpecificPrice)
    private storeSpecificPriceRep: Repository<StoreSpecificPrice>,
    @InjectRepository(CouponCustomerAssign)
    private couponCustomerAssignRep: Repository<CouponCustomerAssign>,
    @InjectRepository(CustomerReview)
    private customerReviewRep: Repository<CustomerReview>,
    @InjectRepository(MenuLogs) private menuLogs: Repository<MenuLogs>,
    @InjectRepository(Order_Aggregators)
    private orderAggregatorRep: Repository<Order_Aggregators>,
    @InjectRepository(SurveryFeedback)
    private surveyFeedbackRep: Repository<SurveryFeedback>
  ) {}

  async getFPJson(order_id: any): Promise<any> {
    return await this.orderAggregatorRep.findOne({
      select: ["order_json"],
      where: {
        order_id: order_id
      }
    });
  }
  async dataClean(): Promise<any> {
    await this.combo_timeRep.delete({});
    await this.menu_timeRep.delete({});
    await this.daysRep.delete({});
  }

  async removeComboTime(combo_id: any): Promise<any> {
    await this.combo_timeRep.delete({
      combo_id: combo_id
    });
  }

  async removeMenuTime(menu_id: any): Promise<any> {
    await this.menu_timeRep.delete({
      menu_item_id: menu_id
    });
  }

  async getCombosTime(): Promise<any> {
    return await this.combo_timeRep
      .createQueryBuilder("combo_timings")
      .select(
        "combo_timings.day_id, c.combo_id as value ,c.combo_name as label_combo"
      )
      .leftJoin("combo", "c", "combo_timings.combo_id = c.combo_id")
      .getRawMany();
  }

  async getItemsTime(): Promise<any> {
    return await this.menu_timeRep
      .createQueryBuilder("menu_item_timings")
      .select(
        "menu_item_timings.day_id, m.menu_item_id as value ,m.item_name as label_menu"
      )
      .leftJoin(
        "menu_item",
        "m",
        "menu_item_timings.menu_item_id = m.menu_item_id"
      )
      .getRawMany();
  }

  async getDays(): Promise<any> {
    return await this.daysRep.find();
  }

  async saveCronByMenuId(menu_id: number, menu: Menu): Promise<any> {
    return await this.menuRep.update(menu_id, menu);
  }
  async updateBadAttempts(userId: number, badAtt: number) {
    return await this.userRep
      .createQueryBuilder()
      .update(User)
      .set({ badAttempts: badAtt })
      .where("user_id = :userId", { userId })
      .execute();
  }
  async updateBadOTPAttempts(userId: number, badAtt: number) {
    return await this.userRep
      .createQueryBuilder()
      .update(User)
      .set({ auth_code_attempts: badAtt })
      .where("user_id = :userId", { userId })
      .execute();
  }
  async resetUserBadAttempts(userId: number) {
    return await this.userRep
      .createQueryBuilder()
      .update(User)
      .set({ badAttempts: 0, auth_code_attempts: 0 })
      .where("user_id = :userId", { userId })
      .execute();
  }
  async getProfileById(id: number) {
    return await this.userRep
      .createQueryBuilder("users")
      .select(
        "users.user_id,users.first_name,users.last_name,users.user_name,users.email_address,users.city,users.address,users.phone_number,users.status,users.country_id,users.role_id,users.user_group_id,a.country_name"
      )
      .innerJoin("country", "a", "users.country_id=a.country_id")
      .where("users.user_id= :user_id", { user_id: id })
      .getRawOne();
  }
  async getProfileByIdWithRoleInfo(id: number) {
    return await this.userRep
      .createQueryBuilder("users")
      .select([
        "users.user_id AS user_id",
        "users.first_name AS first_name",
        "users.last_name AS last_name",
        "users.user_name AS user_name",
        "users.email_address AS email_address",
        "users.city AS city",
        "users.address AS address",
        "users.phone_number AS phone_number",
        "users.status AS status",
        "users.country_id AS country_id",
        "users.role_id AS role_id",
        "users.user_group_id AS user_group_id",
        "a.country_name AS country_name",
        "r.role_name AS role"
      ])
      .innerJoin("country", "a", "users.country_id = a.country_id")
      .innerJoin("roles", "r", "users.role_id = r.role_id")
      .where("users.user_id = :user_id", { user_id: id })
      .getRawOne();
  }
  async editProfile(id: number, user: User): Promise<any> {
    return await this.userRep.update(id, user);
  }
  async changePassword(id: number, password: string) {
    const hashed = await bcrypt.hashSync(password, SALT_ROUNDS);
    return await this.userRep.update(id, { password: hashed });
  }

  async verifyUserFromDb(
    email_address: any,
    role_id: any,
    user_id: any,
    password: any
  ): Promise<any> {
    return await this.userRep
      .createQueryBuilder("users")
      .select("user_id")
      .where(
        "user_id= :user_id AND email_address=:email_address AND role_id=:role_id AND password=:password AND status=1",
        {
          user_id: user_id,
          email_address: email_address,
          role_id: role_id,
          password: password
        }
      )
      .getRawOne();
  }
  async getuserDataByEmail(email: string): Promise<any> {
    return await this.userRep
      .createQueryBuilder("users")
      .select("users.*,a.role_name,a.type as role_type")
      .innerJoin("roles", "a", "users.role_id=a.role_id")
      .where("users.email_address= :email_address", { email_address: email })
      .getRawOne();
  }
  async getuserDataByEmailAndStore(email: string): Promise<any> {
    return await this.userRep
      .createQueryBuilder("users")
      .select(
        "users.user_id,users.first_name,users.last_name,users.user_name,users.email_address,users.city,users.address,users.phone_number,users.status,users.country_id,users.role_id,users.user_group_id,a.role_name,a.type as role_type"
      )
      .innerJoin("roles", "a", "users.role_id=a.role_id")
      .innerJoin("group_stores", "b", "users.user_group_id=b.user_group_id")
      .where("users.email_address= :email_address AND users.status= :status", {
        email_address: email,
        status: 1
      })
      .getRawOne();
  }
  async getuserData(): Promise<any> {
    const data = await this.userRep.find();
    return data;
  }

  // Bulk Upload Customers
  async findDuplicateWithEmail(data): Promise<any> {
    return await this.customerRep.findOne({
      where: {
        email_address: data.email_address
      }
    });
  }
  async findDuplicateWithPhone(data): Promise<any> {
    return await this.customerRep.findOne({
      where: {
        phone_number: data.phone_number
      }
    });
  }
  async findDuplicateWithCouponCode(data): Promise<any> {
    return await this.couponRep.findOne({
      where: {
        coupon_code: data.coupon_code
      }
    });
  }
  async customerUpload(data: any) {
    return await this.customerRep.save(data);
  }
  //Customer Only Routes
  // async getCustomers(perPage: any, currentPage: any): Promise<any> {
  //     //const page = (currentPage - 1) * perPage;
  //     return await this.customerRep.find({
  //         order: { customer_id: 'DESC' },
  //         skip: currentPage,
  //         take: perPage,
  //     })
  // }

  // Get Customer Via Paging
  async getCustomers(page: any): Promise<any> {
    const perPage = 10;
    const skip = perPage * page - perPage;
    const data = await this.customerRep.find({
      order: { customer_id: "DESC" },
      skip: skip,
      take: perPage
    });
    const totalCount = await this.customerRep.count({});
    return {
      data,
      totalCount
    };
  }
  async customerBlockUnblock(customerId, isActive): Promise<any> {
    return await this.customerRep
      .createQueryBuilder()
      .update(Customer)
      .set({ is_active: isActive })
      .where("customer_id = :customer_id", { customer_id: customerId })
      .execute();
  }
  async getordersHistory(customer_id: any): Promise<any> {
    return await this.customerRep
      .createQueryBuilder("customers")
      .select(
        "a.order_id,a.order_grossprice,a.payment_method,customers.login_name,a.order_status_code,c.order_status_description,b.store_name,a.date_created"
      )
      .innerJoin("orders", "a", "customers.customer_id=a.customer_id")
      .innerJoin("stores", "b", "a.store_id=b.store_id")
      .innerJoin(
        "ref_order_status_codes",
        "c",
        "a.order_status_code=c.order_status_code"
      )
      .where("customers.customer_id= :customer_id", {
        customer_id: customer_id
      })
      .orderBy("a.order_id", "DESC")
      .limit(5)
      .getRawMany();
  }
  async editCustomer(customer_id: number, customer: any): Promise<any> {
    return await this.customerRep.update(customer_id, customer);
  }
  async getCustomerById(customer_id: number): Promise<any> {
    return await this.customerRep
      .createQueryBuilder("customers")
      .leftJoinAndSelect("customers.address_id", "addresses")
      .where("customers.customer_id= :customer_id", {
        customer_id: customer_id
      })
      .getOne();
  }
  async getCustomerByPhone(phone_number: number): Promise<any> {
    return await this.customerRep
      .createQueryBuilder("customers")
      .select(
        "customer_id,gender,first_name,last_name,login_name,email_address,customer_birthday,login_password,profile_pic,phone_number,is_active,is_guest,is_social_login,is_delete,date_created"
      )
      .where("customers.phone_number= :phone_number", {
        phone_number: phone_number
      })
      .getRawMany();
  }
  async getWishlistOfCustomer(customer_id: any): Promise<any> {
    return await this.wishlistRep
      .createQueryBuilder("wishlist")
      .select(
        "wishlist.wish_id,a.login_name,a.email_address,b.item_name,b.item_description,d.menu_name"
      )
      .innerJoin("customers", "a", "wishlist.customer_id=a.customer_id")
      .innerJoin("menu_item", "b", "wishlist.menu_item_id=b.menu_item_id")
      .innerJoin("groups", "c", "b.item_group_id=c.group_id")
      .innerJoin("menus", "d", "c.menu_id=d.menu_id")
      .where("wishlist.customer_id= :customer_id", { customer_id: customer_id })
      .getRawMany();
  }
  //Customer Feedback queries
  async getCustomerFeedback(): Promise<any> {
    return await this.customerFeedbackRep
      .createQueryBuilder("customer_feedback")
      .select(
        "customer_feedback.*,a.login_name,a.email_address,a.phone_number,b.item_name,d.menu_name"
      )
      .innerJoin(
        "customers",
        "a",
        "customer_feedback.customer_id=a.customer_id"
      )
      .innerJoin(
        "menu_item",
        "b",
        "customer_feedback.menu_item_id=b.menu_item_id"
      )
      .innerJoin("groups", "c", "b.item_group_id=c.group_id")
      .innerJoin("menus", "d", "c.menu_id=d.menu_id")
      .orderBy("customer_feedback.customer_feedback_id", "DESC")
      .getRawMany();
  }
  async delFeedback(feedback_id: number) {
    return this.customerFeedbackRep.delete(feedback_id);
  }
  ///Payment Queries
  async getStores(user_group_id: any) {
    let stores = await this.entityManager.query(
      `SELECT b.store_id FROM group_stores b WHERE b.user_group_id=${user_group_id}`
    );
    let stores_ids = Array.prototype.map
      .call(stores, function(item) {
        return item.store_id;
      })
      .join(",");
    return stores_ids;
  }
  async getPayments(store_ids: any, page: number, limit: number): Promise<any> {
    return await this.paymentRep
      .createQueryBuilder("payments")
      .select(
        "payments.payment_id,payments.payment_amount,payments.payment_status,payments.payment_method,payments.date_created,payments.order_id,payments.store_id,a.store_name,a.branch_code,b.cardOrderId,c.first_name,c.phone_number"
      )
      .innerJoin("stores", "a", "payments.store_id=a.store_id")
      .innerJoin("orders", "b", "payments.order_id=b.order_id")
      .innerJoin("customers", "c", "b.customer_id=c.customer_id")
      .where(`payments.store_id IN (${store_ids})`)
      .limit(limit)
      .offset((page - 1) * limit)
      .orderBy("payments.payment_id", "DESC")
      .getRawMany();
  }
  async getPaymentsCount(store_ids: any): Promise<any> {
    return await this.paymentRep
      .createQueryBuilder("payments")
      .select("COUNT(payments.payment_id) as total")
      .where(`payments.store_id IN (${store_ids})`)
      .getRawOne();
  }
  async getPaymentById(order_id: number): Promise<any> {
    return await this.paymentRep
      .createQueryBuilder("payments")
      .where("payments.order_id= :order_id", { order_id: order_id })
      .getOne();
  }
  async getCustomerPayment(order_id: number): Promise<any> {
    return await this.paymentRep
      .createQueryBuilder("payments")
      .select(
        "payments.payment_id,payments.payment_amount,payments.payment_status,payments.payment_method,payments.date_created,payments.order_id,payments.store_id,a.store_name,a.branch_code,b.cardOrderId,c.first_name,c.phone_number"
      )
      .innerJoin("stores", "a", "payments.store_id=a.store_id")
      .innerJoin("orders", "b", "payments.order_id=b.order_id")
      .innerJoin("customers", "c", "b.customer_id=c.customer_id")
      .where(`payments.order_id= ${order_id}`)
      .getRawOne();
  }
  async updatePayment(payment_id: number, data: any): Promise<any> {
    return await this.paymentRep.update(payment_id, data);
  }
  ///Home Queries
  async recentOrders(interval: any, store_ids: any): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select(
        "orders.order_id,orders.payment_method,orders.order_grossprice,a.login_name,a.phone_number,b.order_status_code,b.order_status_description"
      )
      .innerJoin("customers", "a", "orders.customer_id=a.customer_id")
      .innerJoin(
        "ref_order_status_codes",
        "b",
        "orders.order_status_code=b.order_status_code"
      )
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        `DATE(orders.date_created)>= DATE_SUB(CURDATE(),INTERVAL ${interval} DAY)`
      )
      .orderBy("orders.order_id", "DESC")
      .limit(5)
      .getRawMany();
  }
  async lastSevenDaysOfDataLineChart(store_ids: any): Promise<any> {
    var querystring = `SELECT date_created, Count(order_id) AS completeOrders,
        COALESCE(SUM(order_grossprice),0) AS totalSales FROM orders WHERE store_id IN (${store_ids}) AND (order_status_code=5 OR order_status_code=6) AND DATE(date_created)>= DATE_SUB(CURDATE(),INTERVAL 6 DAY) GROUP BY DATE(date_created)`;
    return await getManager().query(querystring);
  }
  async lastSevenDaysOfDataPieChartSOS(store_ids: any): Promise<any> {
    var querystring = `SELECT date_created, delivery_time_json FROM orders WHERE store_id IN (${store_ids}) AND order_status_code=6 AND DATE(date_created) >= DATE_SUB(CURDATE(),INTERVAL 6 DAY)`;
    return await getManager().query(querystring);
  }
  async lastSevenDaysOrderDataForDougnutChartChannel(
    store_ids: any
  ): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select(
        "order_id,order_grossprice,order_channel,device_info,store_id,order_status_code,delivery_time_json,date_created"
      )
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        `DATE(orders.date_created)>= DATE_SUB(CURDATE(),INTERVAL 6 DAY)`
      )
      .getRawMany();
  }

  async recentOrdersWithDateTime(interval: any, store_ids: any): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select(
        "orders.order_id,orders.payment_method,orders.order_grossprice,a.login_name,a.phone_number,b.order_status_code,b.order_status_description"
      )
      .innerJoin("customers", "a", "orders.customer_id=a.customer_id")
      .innerJoin(
        "ref_order_status_codes",
        "b",
        "orders.order_status_code=b.order_status_code"
      )
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        "orders.date_created BETWEEN '" +
          interval.start +
          "' AND '" +
          interval.end +
          "'"
      )
      .orderBy("orders.order_id", "DESC")
      .limit(5)
      .getRawMany();
  }
  async recentPayments(interval: any, store_ids: any): Promise<any> {
    return await this.paymentRep
      .createQueryBuilder("payments")
      .select(
        "payments.payment_id,payments.payment_amount,payments.payment_status,payments.payment_method,payments.date_created,payments.order_id,payments.store_id,a.store_name,a.branch_code,b.cardOrderId,c.first_name,c.phone_number"
      )
      .innerJoin("stores", "a", "payments.store_id=a.store_id")
      .innerJoin("orders", "b", "payments.order_id=b.order_id")
      .innerJoin("customers", "c", "b.customer_id=c.customer_id")
      .where(`payments.store_id IN (${store_ids})`)
      .andWhere(
        `DATE(payments.date_created)>= DATE_SUB(CURDATE(),INTERVAL ${interval} DAY)`
      )
      .orderBy("payments.payment_id", "DESC")
      .limit(5)
      .getRawMany();
  }
  async recentPaymentsWithDateTime(
    interval: any,
    store_ids: any
  ): Promise<any> {
    return await this.paymentRep
      .createQueryBuilder("payments")
      .select(
        "payments.payment_id,payments.payment_amount,payments.payment_status,payments.payment_method,payments.date_created,payments.order_id,payments.store_id,a.store_name,a.branch_code,b.cardOrderId,c.first_name,c.phone_number"
      )
      .innerJoin("stores", "a", "payments.store_id=a.store_id")
      .innerJoin("orders", "b", "payments.order_id=b.order_id")
      .innerJoin("customers", "c", "b.customer_id=c.customer_id")
      .where(`payments.store_id IN (${store_ids})`)
      .andWhere(
        "payments.date_created BETWEEN '" +
          interval.start +
          "' AND '" +
          interval.end +
          "'"
      )
      .orderBy("payments.payment_id", "DESC")
      .limit(5)
      .getRawMany();
  }
  async orderDataForhomeCounter(interval: any, store_ids: any): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select(
        "order_id,order_grossprice,delivery_status,store_id,order_status_code,delivery_time_json,date_created"
      )
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        `DATE(orders.date_created)>= DATE_SUB(CURDATE(),INTERVAL ${interval} DAY)`
      )
      .getRawMany();
  }
  async orderDataForhomeCounterWithDatetime(
    interval: any,
    store_ids: any
  ): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select(
        "order_id,order_grossprice,delivery_status,store_id,order_status_code,delivery_time_json,date_created"
      )
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        `orders.date_created BETWEEN '${interval.start}' AND '${interval.end}'`
      )
      .getRawMany();
  }
  async totalCustomersCounter(interval: any): Promise<any> {
    return await this.customerRep
      .createQueryBuilder("customers")
      .select("COUNT(customer_id) as total")
      .andWhere(
        `DATE(customers.date_created)>= DATE_SUB(CURDATE(),INTERVAL ${interval} DAY)`
      )
      .getRawOne();
  }
  async totalCustomersCounterWithDateTime(interval: any): Promise<any> {
    return await this.customerRep
      .createQueryBuilder("customers")
      .select("COUNT(customer_id) as total")
      .andWhere(
        `customers.date_created BETWEEN '${interval.start}' AND '${interval.end}'`
      )
      .getRawOne();
  }
  async aggorderDataForhomeCounter(
    interval: any,
    store_ids: any
  ): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select("order_id,order_grossprice,delivery_status,aggregator_orderId")
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        `orders.order_channel = 'FoodPanda' AND DATE(orders.date_created)>= DATE_SUB(CURDATE(),INTERVAL ${interval} DAY)`
      )
      .getRawMany();
  }
  async aggorderDataForhomeCounterWithDatetime(
    interval: any,
    store_ids: any
  ): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select("order_id,order_grossprice,delivery_status,aggregator_orderId")
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        `orders.order_channel = 'FoodPanda' AND orders.date_created BETWEEN '${interval.start}' AND '${interval.end}'`
      )
      .getRawMany();
  }
  async monthlyOrders(interval: any): Promise<any> {
    var querystring = `SELECT CONCAT(YEAR(date_created),'-',MONTH(date_created)) AS YM,Count(CASE WHEN (order_status_code=5 OR order_status_code=6) THEN 1 ELSE NULL END) AS completeOrders,
            Count(CASE WHEN (order_status_code=7 OR order_status_code=8 OR order_status_code=9) THEN 1 ELSE NULL END) AS cancelOrders FROM orders WHERE DATE(date_created)>= DATE_SUB(CURDATE(),INTERVAL ${interval} DAY) AND YEAR(date_created)= YEAR(CURDATE()) GROUP BY YM ORDER BY Month(date_created) DESC`;
    return await getManager().query(querystring);
  }
  async monthlyOrderswithDateTime(interval: any): Promise<any> {
    var querystring = `SELECT CONCAT(YEAR(date_created),'-',MONTH(date_created)) AS YM,Count(CASE WHEN (order_status_code=5 OR order_status_code=6) THEN 1 ELSE NULL END) AS completeOrders,
            Count(CASE WHEN (order_status_code=7 OR order_status_code=8 OR order_status_code=9) THEN 1 ELSE NULL END) AS cancelOrders FROM orders WHERE date_created BETWEEN '${interval.start}' AND '${interval.end}' AND YEAR(date_created)= YEAR(CURDATE()) GROUP BY YM ORDER BY Month(date_created) DESC`;
    return await getManager().query(querystring);
  }
  async monthlySales(): Promise<any> {
    return await getManager().query(
      "SELECT CONCAT(YEAR(date_created),'-',MONTH(date_created)) AS YM,SUM(order_item_quantity) AS totalSales FROM order_items WHERE order_id IN (SELECT order_id FROM orders a WHERE (a.order_status_code=5 OR a.order_status_code=6) AND YEAR(date_created)= YEAR(CURDATE()) GROUP BY YM ORDER BY Month(date_created) DESC)"
    );
  }
  async leaderBoard(interval: any, store_ids: any): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select("a.store_name,COALESCE(COUNT(orders.order_id),0) as totalOrders")
      .innerJoin("stores", "a", "orders.store_id=a.store_id")
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        `(orders.order_status_code=5 OR orders.order_status_code=6) AND DATE(orders.date_created)>= DATE_SUB(CURDATE(),INTERVAL ${interval} DAY)`
      )
      .groupBy("orders.store_id")
      .orderBy("totalOrders", "DESC")
      .limit(5)
      .getRawMany();
  }
  async leaderBoardWithDateTime(interval: any, store_ids: any): Promise<any> {
    return await this.orderRep
      .createQueryBuilder("orders")
      .select("a.store_name,COALESCE(COUNT(orders.order_id),0) as totalOrders")
      .innerJoin("stores", "a", "orders.store_id=a.store_id")
      .where(`orders.store_id IN (${store_ids})`)
      .andWhere(
        `(orders.order_status_code=5 OR orders.order_status_code=6) AND orders.date_created BETWEEN '${interval.start}' AND '${interval.end}'`
      )
      .groupBy("orders.store_id")
      .orderBy("totalOrders", "DESC")
      .limit(5)
      .getRawMany();
  }
  async userData(): Promise<any> {
    return await this.userRep
      .createQueryBuilder("users")
      .select(
        "users.user_id,users.first_name,users.last_name,users.user_name,users.email_address,users.city,users.address,users.phone_number,users.status,users.country_id,users.role_id,users.user_group_id,a.country_name,b.role_name,c.name as group_name"
      )
      .innerJoin("country", "a", "users.country_id=a.country_id")
      .innerJoin("roles", "b", "users.role_id=b.role_id")
      .innerJoin("user_groups", "c", "users.user_group_id=c.id")
      .orderBy("users.date_created", "DESC")
      .getRawMany();
  }
  async getCustomerFeedbacks(page: number, limit: number): Promise<any[]> {
    return await this.customerReviewRep
      .createQueryBuilder("customer_review")
      .select(
        "customer_review.id,customer_review.emoji, customer_review.comment, a.order_id, b.login_name, c.store_name, customer_review.date_created, a.order_channel, a.delivery_status"
      )
      .innerJoin("orders", "a", "customer_review.order_id=a.order_id")
      .innerJoin("customers", "b", "a.customer_id=b.customer_id")
      .innerJoin("stores", "c", "a.store_id=c.store_id")
      .limit(limit)
      .offset((page - 1) * limit)
      .orderBy("customer_review.date_created", "DESC")
      .getRawMany();
  }
  async getFeedbacksCount(): Promise<any> {
    return await this.customerReviewRep
      .createQueryBuilder("customer_review")
      .select("COUNT(customer_review.id) as total")
      .getRawOne();
  }
  async addUser(user: any): Promise<any> {
    const hashed = await bcrypt.hashSync(user.password, SALT_ROUNDS);
    user.password = hashed;
    return await this.userRep.save(user);
  }
  async editUser(user_id: number, user: any): Promise<any> {
    return await this.userRep.update(user_id, user);
  }
  async getUserById(user_id: number): Promise<any> {
    return await this.userRep
      .createQueryBuilder("users")
      .select("users.*,a.role_name")
      .innerJoin("roles", "a", "users.role_id=a.role_id")
      .where("users.user_id= :user_id", { user_id: user_id })
      .getRawOne();
  }
  async getUserByIdWithPassword(user_id: number): Promise<any> {
    return await this.userRep
      .createQueryBuilder("users")
      .select(
        "users.user_id,users.first_name,users.last_name,users.user_name,users.email_address,users.city,users.address,users.phone_number,users.status,users.country_id,users.role_id,users.user_group_id,a.role_name,users.password"
      )
      .select("users.*,a.role_name")
      .innerJoin("roles", "a", "users.role_id=a.role_id")
      .where("users.user_id= :user_id", { user_id: user_id })
      .getRawOne();
  }
  async delUser(user_id: number) {
    return this.userRep.delete(user_id);
  }
  async userBlockUnblock(user_id: any, status: any) {
    return await this.userRep
      .createQueryBuilder()
      .update(User)
      .set({ status: status })
      .where("user_id = :user_id", { user_id: user_id })
      .execute();
  }
  async storeData(store_ids: any): Promise<any> {
    return await this.storeRep
      .createQueryBuilder("stores")
      .select(
        "stores.*,a.country_name,b.brand_name,c.lat,c.lng,e.store_type_name,f.channel_name,g.state_name,h.name as city_name"
      )
      .leftJoin("country", "a", "stores.country_id=a.country_id")
      .innerJoin("brands", "b", "stores.brand_id=b.brand_id")
      .innerJoin("trade_zone", "c", "stores.trade_zone_id=c.trade_zone_id")
      .innerJoin("store_type", "e", "stores.store_type_id=e.store_type_id")
      .innerJoin(
        "sales_channel",
        "f",
        "stores.sales_channel_id=f.sales_channel_id"
      )
      .innerJoin("states", "g", "stores.state_id=g.state_id")
      .innerJoin("city", "h", "stores.city_id=h.id")
      .where(`stores.store_id IN (${store_ids})`)
      .orderBy("stores.store_id", "DESC")
      .getRawMany();
  }
  async storesListForMenu(user_group_id: any): Promise<any> {
    return await this.storeRep
      .createQueryBuilder("stores")
      .select("store_id as value,store_name as label")
      .orderBy("stores.date_created", "DESC")
      .where(
        `stores.store_id IN (SELECT d.store_id FROM group_stores d WHERE d.user_group_id=${user_group_id})`
      )
      .getRawMany();
  }
  async getStoreById(store_id: number): Promise<any> {
    return await this.storeRep
      .createQueryBuilder("stores")
      .select("stores.*,a.brand_name,b.trade_zone_name,b.trade_zone_shape")
      .innerJoin("brands", "a", "stores.brand_id=a.brand_id")
      .innerJoin("trade_zone", "b", "stores.trade_zone_id=b.trade_zone_id")
      .where("stores.store_id= :store_id", { store_id: store_id })
      .getRawOne();
  }
  async addStore(store: any): Promise<any> {
    return await this.storeRep.save(store);
  }
  async addTradezone(zone: any): Promise<any> {
    return await this.tradezoneRep.save(zone);
  }
  async editStore(store_id: number, store: any): Promise<any> {
    return await this.storeRep.update(store_id, store);
  }
  async uploadImage(data: any): Promise<any> {
    return await this.storeRep
      .createQueryBuilder()
      .update(Store)
      .set({ store_image: data.store_image })
      .where("store_id = :store_id", { store_id: data.store_id })
      .execute();
  }
  async updateStoreSnooze(data: any): Promise<any> {
    return await this.storeRep
      .createQueryBuilder()
      .update(Store)
      .set({
        is_snooze: data.is_snooze,
        snooze_start_time: data.snooze_start_time,
        snooze_end_time: data.snooze_end_time,
        snooze_start_cron_id: data.snooze_start_cron_id,
        snooze_start_cron_job: data.snooze_start_cron_job,
        snooze_start_cron_job_date: data.snooze_start_cron_job_date,
        snooze_end_cron_id: data.snooze_end_cron_id,
        snooze_end_cron_job: data.snooze_end_cron_job,
        snooze_end_cron_job_date: data.snooze_end_cron_job_date
      })
      .where("store_id = :store_id", { store_id: data.store_id })
      .execute();
  }
  async updateItemSnooze(data: any): Promise<any> {
    return await this.itemRep
      .createQueryBuilder()
      .update(MenuItem)
      .set({
        is_snooze: data.is_snooze,
        snooze_start_time: data.snooze_start_time,
        snooze_end_time: data.snooze_end_time,
        snooze_start_cron_id: data.snooze_start_cron_id,
        snooze_start_cron_job: data.snooze_start_cron_job,
        snooze_start_cron_job_date: data.snooze_start_cron_job_date,
        snooze_end_cron_id: data.snooze_end_cron_id,
        snooze_end_cron_job: data.snooze_end_cron_job,
        snooze_end_cron_job_date: data.snooze_end_cron_job_date
      })
      .where("menu_item_id = :menu_item_id", {
        menu_item_id: data.menu_item_id
      })
      .execute();
  }
  async updateComboSnooze(data: any): Promise<any> {
    return await this.comboRep
      .createQueryBuilder()
      .update(Combo)
      .set({
        is_snooze: data.is_snooze,
        snooze_start_time: data.snooze_start_time,
        snooze_end_time: data.snooze_end_time,
        snooze_start_cron_id: data.snooze_start_cron_id,
        snooze_start_cron_job: data.snooze_start_cron_job,
        snooze_start_cron_job_date: data.snooze_start_cron_job_date,
        snooze_end_cron_id: data.snooze_end_cron_id,
        snooze_end_cron_job: data.snooze_end_cron_job,
        snooze_end_cron_job_date: data.snooze_end_cron_job_date
      })
      .where("combo_id = :combo_id", { combo_id: data.combo_id })
      .execute();
  }
  async editTradezone(tradeZoneId: any, zone: any): Promise<any> {
    return await this.tradezoneRep.update(tradeZoneId, zone);
  }
  async delZone(tradeZoneId: any) {
    return this.tradezoneRep.delete(tradeZoneId);
  }
  async storeBlockUnblock(store_id, isActive): Promise<any> {
    return await this.storeRep
      .createQueryBuilder()
      .update(Store)
      .set({ is_active: isActive })
      .where("store_id = :store_id", { store_id: store_id })
      .execute();
  }
  //Brand queries
  async brandsData() {
    return this.brandRep.find({ order: { brand_id: "DESC" } });
  }
  async addBrand(brand: any): Promise<any> {
    return await this.brandRep.save(brand);
  }
  async editBrand(brand_id: number, brand: any): Promise<any> {
    return await this.brandRep.update(brand_id, brand);
  }
  async getBrandById(brand_id: number): Promise<any> {
    return this.brandRep.findOne({ where: { brand_id: brand_id } });
  }
  async delBrand(brand_id: number) {
    return this.brandRep.delete(brand_id);
  }
  async saveMov({ sales_channel_id, mov }): Promise<any> {
    return await this.saleschannelRep
      .createQueryBuilder()
      .update(SalesChannel)
      .set({ mov: mov })
      .where("sales_channel_id = :sales_channel_id", {
        sales_channel_id: sales_channel_id
      })
      .execute();
  }
  async brandBlockUnblock(brand_id, isActive): Promise<any> {
    return await this.brandRep
      .createQueryBuilder()
      .update(Brand)
      .set({ is_active: isActive })
      .where("brand_id = :brand_id", { brand_id: brand_id })
      .execute();
  }
  //Menus queries
  async menuData(): Promise<any> {
    return await this.menuRep
      .createQueryBuilder("menus")
      .select(
        "menus.*,a.store_type_name,b.brand_name,c.state_name,d.country_name"
      )
      .leftJoin("store_type", "a", "menus.store_type_id=a.store_type_id")
      .leftJoin("brands", "b", "menus.brand_id=b.brand_id")
      .leftJoin("states", "c", "menus.state_id=c.state_id")
      .leftJoin("country", "d", "c.country_id=d.country_id")
      .orderBy("menus.menu_id", "DESC")
      .getRawMany();
  }

  async getStoreGroups(store_id: any): Promise<any> {
    return await this.storeMenuRep
      .createQueryBuilder("store_menu")
      .select("store_menu.group_id as group_id")
      .where("store_menu.group_id is not Null")
      .andWhere("store_menu.store_id = :store_id", { store_id: store_id })
      .getRawMany();
  }

  async getStoreItems(store_id: any): Promise<any> {
    return await this.storeMenuRep
      .createQueryBuilder("store_menu")
      .select("store_menu.menu_item_id as menu_item_id")
      .where("store_menu.menu_item_id is not Null")
      .andWhere("store_menu.store_id = :store_id", { store_id: store_id })
      .getRawMany();
  }

  async getStoreModGroups(store_id: any): Promise<any> {
    return await this.storeMenuRep
      .createQueryBuilder("store_menu")
      .select("store_menu.modifier_group_id as modifier_group_id")
      .where("store_menu.modifier_group_id is not Null")
      .andWhere("store_menu.store_id = :store_id", { store_id: store_id })
      .getRawMany();
  }

  async getStoreModifiers(store_id: any): Promise<any> {
    return await this.storeMenuRep
      .createQueryBuilder("store_menu")
      .select("store_menu.modifier_id as modifier_id")
      .where("store_menu.modifier_id is not Null")
      .andWhere("store_menu.store_id = :store_id", { store_id: store_id })
      .getRawMany();
  }

  async getStoreSubGroups(store_id: any): Promise<any> {
    return await this.storeMenuRep
      .createQueryBuilder("store_menu")
      .select("store_menu.subgroup_id as subgroup_id")
      .where("store_menu.subgroup_id is not Null")
      .andWhere("store_menu.store_id = :store_id", { store_id: store_id })
      .getRawMany();
  }

  async getAllItems(): Promise<any> {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select("menu_item.menu_item_id as menu_item_id") //,b.image_url
      .getRawMany();
  }

  /* async getAllModGroups() {
         return await this.modGroupRep.createQueryBuilder("modifier_group")
             .select("modifier_group.*")
             .getRawMany()
     }*/

  async getAllModifiers(): Promise<any> {
    return await this.modRep
      .createQueryBuilder("modifiers")
      .select("modifiers.modifier_id as modifier_id")
      .getRawMany();
  }
  async getAllCombos(): Promise<any> {
    return await this.comboRep
      .createQueryBuilder("combo")
      .select("combo.combo_id as combo_id")
      .getRawMany();
  }

  async getAllSubGroups(): Promise<any> {
    return await this.subgroupRep
      .createQueryBuilder("subgroups")
      .select("subgroups.id as id")
      .getRawMany();
  }

  async getAllGroups(): Promise<any> {
    return await this.groupRep
      .createQueryBuilder("groups")
      .select("groups.group_id as group_id")
      .getRawMany();
  }

  async getStoreCombos(store_id: any): Promise<any> {
    return await this.storeMenuRep
      .createQueryBuilder("store_menu")
      .select("store_menu.combo_id as combo_id")
      .where("store_menu.combo_id is not Null")
      .andWhere("store_menu.store_id = :store_id", { store_id: store_id })
      .getRawMany();
  }
  async getMenuById(menu_id: number): Promise<any> {
    return await this.menuRep
      .createQueryBuilder("menus")
      .select("menus.*,a.store_type_name")
      .leftJoin("store_type", "a", "menus.store_type_id=a.store_type_id")
      .where("menus.menu_id= :menu_id", { menu_id: menu_id })
      .getRawOne();
  }
  async addMenu(menu: any): Promise<any> {
    return await this.menuRep.save(menu);
  }
  async editMenu(menu_id: number, menu: any): Promise<any> {
    return await this.menuRep.update(menu_id, menu);
  }
  async getGroupsOfMenu(menu_id: number): Promise<any> {
    return await this.menuRep
      .createQueryBuilder("menus")
      .select("menus.*,a.*")
      .innerJoin("groups", "a", "menus.menu_id=a.menu_id")
      .where("menus.menu_id= :menu_id", { menu_id: menu_id })
      .getRawMany();
  }
  async unpublishMenu(menu_id: number, is_publish: number): Promise<any> {
    let combos: any = await this.comboRep.find({ where: { menu_id: menu_id } });
    let combosToUpdate: any = "";
    let groupsToUpdate: any = "";
    if (combos.length > 0) {
      combosToUpdate = await this.comboRep
        .createQueryBuilder()
        .update(Combo)
        .set({ is_publish: is_publish }) //to update combos
        .where("menu_id = :menu_id", { menu_id: menu_id })
        .execute();
    }
    let groups: any = await this.groupRep.find({ where: { menu_id: menu_id } });
    if (groups.length > 0) {
      groupsToUpdate = await this.groupRep
        .createQueryBuilder()
        .update(Group)
        .set({ is_publish: is_publish })
        .where("menu_id = :menu_id", { menu_id: menu_id })
        .execute(); //To Update groups
      if (groupsToUpdate.affected > 0) {
        let group_ids: any = [];
        groups.forEach((element: any) => {
          group_ids.push(element.group_id);
        });
        let subgroups: any = await this.subgroupRep.find({
          group_id: In(group_ids)
        });
        if (subgroups.length > 0) {
          await this.subgroupRep
            .createQueryBuilder()
            .update(SubGroup)
            .set({ is_publish: is_publish }) //To update menuItem by GroupIds
            .where("group_id In(:...group_id)", { group_id: group_ids })
            .execute();
          let items: any = await this.itemRep.find({
            item_group_id: In(group_ids)
          });
          if (items.length > 0) {
            await this.itemRep
              .createQueryBuilder()
              .update(MenuItem)
              .set({ is_publish: is_publish }) //To update menuItem by GroupIds
              .where("item_group_id In(:...item_group_id)", {
                item_group_id: group_ids
              })
              .execute();
            await this.modGroupRep
              .createQueryBuilder()
              .update(ModifierGroup)
              .set({ is_publish: is_publish })
              .execute(); //To update Modifire Groups by GroupIds
            await this.modRep
              .createQueryBuilder()
              .update(ModifierOption)
              .set({ is_publish: is_publish })
              .execute(); //To update Modifirs by GroupIds
            await this.menuRep
              .createQueryBuilder()
              .update(Menu)
              .set({ is_publish: is_publish }) //To update menu
              .where("menu_id = :menu_id", { menu_id: menu_id })
              .execute();
            return true;
          } else {
            await this.menuRep
              .createQueryBuilder()
              .update(Menu)
              .set({ is_publish: is_publish }) //To update menu
              .where("menu_id = :menu_id", { menu_id: menu_id })
              .execute();
            return true;
          }
        } else {
          await this.menuRep
            .createQueryBuilder()
            .update(Menu)
            .set({ is_publish: is_publish }) //To update menu
            .where("menu_id = :menu_id", { menu_id: menu_id })
            .execute();
          return true;
        }
      }
    } else {
      if (combosToUpdate.affected > 0) {
        await this.menuRep
          .createQueryBuilder()
          .update(Menu)
          .set({ is_publish: 1 }) //To update menu
          .where("menu_id = :menu_id", { menu_id: menu_id })
          .execute();
        return true;
      } else {
        return false;
      }
    }
  }
  async publishmenu(menu_id: number): Promise<any> {
    let combos: any = await this.comboRep.find({ where: { menu_id: menu_id } });
    let combosToUpdate: any = "";
    let groupsToUpdate: any = "";
    if (combos.length > 0) {
      var combosquerystring = `UPDATE combo SET is_publish= CASE is_active WHEN 0 THEN 0 WHEN 1 THEN 1 END
             WHERE menu_id=${menu_id}`;
      combosToUpdate = await getManager().query(combosquerystring);
    }
    let groups: any = await this.groupRep.find({ where: { menu_id: menu_id } });
    if (groups.length > 0) {
      var groupsquerystring =
        "UPDATE `groups` SET is_publish= CASE is_active WHEN 0 THEN 0 WHEN 1 THEN 1 END WHERE" +
        ` menu_id=${menu_id}`;
      groupsToUpdate = await getManager().query(groupsquerystring);
      if (groupsToUpdate.affectedRows > 0) {
        let group_ids: any = [];
        groups.forEach((element: any) => {
          group_ids.push(element.group_id);
        });
        let subgroups: any = await this.subgroupRep.find({
          group_id: In(group_ids)
        });
        if (subgroups.length > 0) {
          let subGroupsquerystring: any = `UPDATE subgroups SET is_publish= CASE is_active WHEN 0 THEN 0 WHEN 1 THEN 1 END
                    WHERE group_id IN(${group_ids})`;
          await getManager().query(subGroupsquerystring);
          let items: any = await this.itemRep.find({
            item_group_id: In(group_ids)
          });
          if (items.length > 0) {
            var itemsquerystring = `UPDATE menu_item SET is_publish= CASE is_active WHEN 0 THEN 0 WHEN 1 THEN 1 END
             WHERE item_group_id IN(${group_ids})`;
            await getManager().query(itemsquerystring);
            var modGroupsquerystring = `UPDATE modifier_group SET is_publish= CASE is_active WHEN 0 THEN 0 WHEN 1 THEN 1 END`;
            await getManager().query(modGroupsquerystring);
            var modifiersquerystring = `UPDATE modifiers SET is_publish= CASE is_active WHEN 0 THEN 0 WHEN 1 THEN 1 END`;
            await getManager().query(modifiersquerystring);
            await this.menuRep
              .createQueryBuilder()
              .update(Menu)
              .set({ is_publish: 1 }) //To update menu
              .where("menu_id = :menu_id", { menu_id: menu_id })
              .execute();
            return true;
          } else {
            await this.menuRep
              .createQueryBuilder()
              .update(Menu)
              .set({ is_publish: 1 }) //To update menu
              .where("menu_id = :menu_id", { menu_id: menu_id })
              .execute();
            return true;
          }
        } else {
          await this.menuRep
            .createQueryBuilder()
            .update(Menu)
            .set({ is_publish: 1 }) //To update menu
            .where("menu_id = :menu_id", { menu_id: menu_id })
            .execute();
          return true;
        }
      }
    } else {
      if (combosToUpdate.affectedRows > 0) {
        await this.menuRep
          .createQueryBuilder()
          .update(Menu)
          .set({ is_publish: 1 }) //To update menu
          .where("menu_id = :menu_id", { menu_id: menu_id })
          .execute();
        return true;
      } else {
        return false;
      }
    }
  }
  async StoresForMenu(menu: any): Promise<any> {
    return await this.menuStoreRep.find({
      relations: ["store_id"],
      where: {
        menu_id: menu.menu_id
      }
    });
  }
  async storesListForUserGroup(user_group_id: any): Promise<any> {
    return await this.storeRep
      .createQueryBuilder("stores")
      .select("store_id as value,store_name as label")
      .where(
        `stores.store_id IN (SELECT d.store_id FROM group_stores d WHERE d.user_group_id=${user_group_id})`
      )
      .getRawMany();
  }
  //Groups queries
  async groupData(): Promise<any> {
    return await this.groupRep
      .createQueryBuilder("groups")
      .select("groups.*,a.menu_id,a.menu_name,b.store_type_name")
      .innerJoin("menus", "a", "groups.menu_id=a.menu_id")
      .leftJoin("store_type", "b", "a.store_type_id=b.store_type_id")
      .orderBy("groups.date_created", "DESC")
      .getRawMany();
  }
  async getGroupById(group_id: number): Promise<any> {
    return await this.groupRep.findOne({
      relations: [
        "menu_id",
        "subgroup_id",
        "itemStores",
        "itemStores.store_id"
      ],
      where: {
        group_id: group_id
      }
    });
  }
  async addGroup(group: any): Promise<any> {
    return await this.groupRep.save(group);
  }
  async getBrand(): Promise<any> {
    return this.brandRep.findOne({ where: { is_active: 1 } });
  }
  async getAllMenu(): Promise<any> {
    return await this.menuRep
      .createQueryBuilder("menus")
      .select("menus.*,a.store_type_name")
      .leftJoin("store_type", "a", "menus.store_type_id=a.store_type_id")
      .getRawMany();
  }
  async getItemsByGroup(group_id: any): Promise<any> {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .where(`menu_item.item_group_id= ${group_id}`)
      .getRawMany();
  }
  async getVariantsByItem(item_id: any): Promise<any> {
    return await this.itemvariantRep
      .createQueryBuilder("menu_item_variants")
      .select("menu_item_variants.*")
      .where(`menu_item_variants.menu_item_id= ${item_id}`)
      .getRawMany();
  }
  async GetAllModifiers(): Promise<any> {
    return await this.modRep
      .createQueryBuilder("modifiers")
      .select("modifiers.*")
      .getRawMany();
  }
  async GetAllModifierGroups(): Promise<any> {
    return await this.modGroupRep
      .createQueryBuilder("modifier_group")
      .select("modifier_group.*")
      .getRawMany();
  }
  async groupBlockUnblock(groupId, isActive): Promise<any> {
    return await this.groupRep
      .createQueryBuilder()
      .update(Group)
      .set({ is_active: isActive })
      .where("group_id = :group_id", { group_id: groupId })
      .execute();
  }
  async editGroup(group_id: number, group: any): Promise<any> {
    return await this.groupRep.update(group_id, group);
  }
  async GetItemsOfSubGroup(group_id: number): Promise<any> {
    return await this.subgroupRep
      .createQueryBuilder("subgroups")
      .select("a.menu_item_id,a.item_name")
      .innerJoin("menu_item", "a", "subgroups.id=a.subgroup_id")
      .where("subgroups.id= :id", { id: group_id })
      .getRawMany();
  }
  //SubGroups queries
  async subGroupData(): Promise<any> {
    return await this.subgroupRep
      .createQueryBuilder("subgroups")
      .select("subgroups.*,a.group_id,a.group_name")
      .innerJoin("groups", "a", "subgroups.group_id=a.group_id")
      .orderBy("subgroups.date_created", "DESC")
      .getRawMany();
  }
  async getSubGroupById(group_id: number): Promise<any> {
    return await this.subgroupRep.findOne({
      relations: ["group_id", "itemStores", "itemStores.store_id"],
      where: {
        id: group_id
      }
    });
  }
  async addSubGroup(group: any): Promise<any> {
    return await this.subgroupRep.save(group);
  }
  async subgroupBlockUnblock(groupId, isActive): Promise<any> {
    return await this.subgroupRep
      .createQueryBuilder()
      .update(SubGroup)
      .set({ is_active: isActive })
      .where("id = :id", { id: groupId })
      .execute();
  }
  async editSubGroup(id: number, group: any): Promise<any> {
    return await this.subgroupRep.update(id, group);
  }
  async GetSubGroupsOfGroup(group_id: number): Promise<any> {
    return await this.groupRep
      .createQueryBuilder("groups")
      .select("groups.*,a.*")
      .innerJoin("subgroups", "a", "groups.group_id=a.group_id")
      .where("groups.group_id= :group_id", { group_id: group_id })
      .andWhere("a.is_publish= :is_publish", { is_publish: 1 })
      .getRawMany();
  }
  //Menu Items queries
  async itemsData(): Promise<any> {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select(
        "menu_item.*,CONCAT(a.group_name,' (',c.sub_group_name,')') as group_name,b.menu_name"
      ) //,b.image_url
      .innerJoin("groups", "a", "menu_item.item_group_id=a.group_id")
      .innerJoin("subgroups", "c", "menu_item.subgroup_id=c.id")
      .innerJoin("menus", "b", "a.menu_id=b.menu_id")
      .orderBy("menu_item.menu_item_id", "DESC")
      .getRawMany();
  }
  async getItemById(menu_item_id: number): Promise<any> {
    return await this.itemRep.findOne({
      relations: [
        "item_group_id",
        "item_channel_id",
        "item_group_id.menu_id",
        "subgroup_id",
        "itemStores",
        "itemStores.store_id",
        "variants",
        "variants.variant_id"
      ],
      where: {
        menu_item_id: menu_item_id
      }
    });
  }
  async addItem(data: any): Promise<any> {
    return await this.itemRep.save(data);
  }
  async additemVariants(data: any): Promise<any> {
    return await this.itemvariantRep.save(data);
  }
  async itemBlockUnblock(itemId, isActive): Promise<any> {
    return await this.itemRep
      .createQueryBuilder()
      .update(MenuItem)
      .set({ is_active: isActive })
      .where("menu_item_id = :menu_item_id", { menu_item_id: itemId })
      .execute();
  }
  async getAllVariants(): Promise<any> {
    return await this.itemvariantRep.find();
  }
  async getAllVariantsWithMenuItem(): Promise<any> {
    return await this.itemvariantRep.find({ relations: ["menu_item_id"] });
  }
  async findVariantOfMenuItemID(menu_item_id: number): Promise<any> {
    return await this.itemvariantRep.find({
      where: { menu_item_id: menu_item_id }
    });
  }
  async findVariantById(variant_id: number): Promise<any> {
    return await this.itemvariantRep.find({
      relations: ["menu_item_id", "menu_item_id.item_group_id"],
      where: { id: variant_id }
    });
  }
  async findVariantByPosCode(pos_code: any): Promise<any> {
    console.log(
      `menu_item.is_active=1 AND menu_item.is_publish=1 AND c.keyword='foodpanda' AND a.pos_code='${pos_code}'`
    );

    // return await this.itemvariantRep.find({ relations: ['menu_item_id', 'menu_item_id.item_group_id'], where: { pos_code: pos_code } });
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select("menu_item.menu_item_id,a.*,d.mrp, gg.pos_code group_poscode")
      .leftJoin(
        "menu_item_variants",
        "a",
        "menu_item.menu_item_id=a.menu_item_id"
      )
      .leftJoin("item_modes", "b", "menu_item.menu_item_id=b.menu_item_id")
      .leftJoin("order_modes", "c", "b.order_mode_id=c.id")
      .leftJoin(
        "order_mode_price",
        "d",
        "a.id=d.item_variant_id AND d.order_mode_id=c.id"
      )
      .innerJoin("groups", "gg", "menu_item.item_group_id=gg.group_id")
      .where(
        `menu_item.is_active=1 AND menu_item.is_publish=1 AND c.keyword='foodpanda' AND a.pos_code='${pos_code}'`
      )
      .getRawMany();
  }

  async findVariantByPosCodeFp(pos_code: any): Promise<any> {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select("menu_item.menu_item_id,a.*,d.mrp, gg.pos_code group_poscode")
      .leftJoin(
        "menu_item_variants",
        "a",
        "menu_item.menu_item_id=a.menu_item_id"
      )
      .leftJoin("item_modes", "b", "menu_item.menu_item_id=b.menu_item_id")
      .leftJoin("order_modes", "c", "b.order_mode_id=c.id")
      .leftJoin(
        "order_mode_price",
        "d",
        "a.id=d.item_variant_id AND d.order_mode_id=c.id"
      )
      .innerJoin("groups", "gg", "menu_item.item_group_id=gg.group_id")
      .where(`a.pos_code='${pos_code}'`)
      .getRawMany();
  }
  async checkPOSCode(pos_code: number): Promise<any> {
    return await this.itemvariantRep.findOne({
      relations: ["menu_item_id"],
      where: { pos_code: pos_code }
    });
  }
  async checkERPId(erp_id: number): Promise<any> {
    return await this.itemvariantRep.findOne({
      relations: ["menu_item_id"],
      where: { erp_id: erp_id }
    });
  }
  async editItem(item_id: number, item: any): Promise<any> {
    // let data = JSON.parse(item);
    return await this.itemRep.update(item_id, item);
  }
  async addLSMitems(itemStores: any) {
    return await this.itemStoreRep.save(itemStores);
  }
  async deleteItemStores(
    menu_item_id: any,
    combo_id: any,
    group_id: any,
    subgroup_id: any,
    banner_id: any,
    suggestive_id: any
  ) {
    return await this.itemStoreRep
      .createQueryBuilder()
      .delete()
      .from(ItemStoreRelation)
      .where("menu_item_id = :menu_item_id", { menu_item_id: menu_item_id })
      .orWhere("combo_id = :combo_id", { combo_id: combo_id })
      .orWhere("group_id = :group_id", { group_id: group_id })
      .orWhere("subgroup_id = :subgroup_id", { subgroup_id: subgroup_id })
      .orWhere("banner_id = :banner_id", { banner_id: banner_id })
      .orWhere("sugg_base_prod_id = :sugg_base_prod_id", {
        sugg_base_prod_id: suggestive_id
      })
      .execute();
  }
  async addSpecificStorePrices(itemStores: any) {
    return await this.storeSpecificPriceRep.save(itemStores);
  }
  async deleteSpecificStorePrices(menu_item_id: any, combo_id: any) {
    return await this.storeSpecificPriceRep
      .createQueryBuilder()
      .delete()
      .from(StoreSpecificPrice)
      .where("menu_item_id = :menu_item_id", { menu_item_id: menu_item_id })
      .orWhere("combo_id = :combo_id", { combo_id: combo_id })
      .execute();
  }
  async deleteItemImages(menu_item_id: number) {
    return await this.ImgRep.createQueryBuilder()
      .delete()
      .from(Images)
      .where("menu_item_id = :menu_item_id", { menu_item_id: menu_item_id })
      .execute();
  }
  async updateItemVariants(item_id: number, item: any) {
    return await this.itemvariantRep.update(item_id, item);
  }
  async deleteItemVariant(variant_id: number) {
    return await this.itemvariantRep
      .createQueryBuilder()
      .delete()
      .from(MenuItemVariants)
      .where("id = :id", { id: variant_id })
      .execute();
  }
  async getComboChoicesVariants(variant_id: any) {
    return await this.comboChoiceRep.find({
      where: {
        size: variant_id
      }
    });
  }
  async getComboChoicesByVariantId(variant_id: any) {
    return await this.comboChoiceRep.find({
      relations: ["combo_id"],
      where: {
        size: variant_id
      }
    });
  }
  async getComboChoicesByComboId(combo_id: any) {
    return await this.comboChoiceRep.find({
      relations: ["mainComboModId"],
      where: {
        combo_id: combo_id
      }
    });
  }
  // async deleteComboChoicesVariants(variant_id: number) {
  //     return await this.comboChoiceRep.createQueryBuilder().delete().from(ComboChoices)
  //         .where("size = :size", { size: variant_id })
  //         .execute();
  // }
  async deleteComboChoices(combo_id: number) {
    return await this.comboChoiceRep
      .createQueryBuilder()
      .delete()
      .from(ComboChoices)
      .where("combo_id = :combo_id", { combo_id: combo_id })
      .execute();
  }
  async deleteComboChoicesModiifersGroups(combo_id: number) {
    return await this.comboChoiceModRep
      .createQueryBuilder()
      .delete()
      .from(ComboChoicesMod)
      .where("combo_id = :combo_id", { combo_id: combo_id })
      .execute();
  }
  async deleteItemHeroImages(menu_item_id: number) {
    return await this.heroRep
      .createQueryBuilder()
      .delete()
      .from(Hero_Item)
      .where("menu_item_id = :menu_item_id", { menu_item_id: menu_item_id })
      .execute();
  }
  async deleteComboImages(combo_id: number) {
    return await this.ImgRep.createQueryBuilder()
      .delete()
      .from(Images)
      .where("combo_id = :combo_id", { combo_id: combo_id })
      .execute();
  }
  async deleteComboHeroImages(combo_id: number) {
    return await this.heroRep
      .createQueryBuilder()
      .delete()
      .from(Hero_Item)
      .where("combo_id = :combo_id", { combo_id: combo_id })
      .execute();
  }
  async deleteModifierImages(modifier_id: number) {
    return await this.ImgRep.createQueryBuilder()
      .delete()
      .from(Images)
      .where("modifier_id = :modifier_id", { modifier_id: modifier_id })
      .execute();
  }
  async addItemImage(itemImages: any) {
    return await this.ImgRep.save(itemImages);
  }
  async add_day(day: any) {
    return await this.daysRep.save(day);
  }

  async add_combo_time(combo_time: any) {
    return await this.combo_timeRep.save(combo_time);
  }
  async add_menu_item_time(menu_item_time: any) {
    return await this.menu_timeRep.save(menu_item_time);
  }
  //combo queries
  async combosData(): Promise<any> {
    return await this.comboRep
      .createQueryBuilder("combo")
      .select(
        "combo.*,a.channel_name,b.menu_name,CONCAT(c.group_name,' (',d.sub_group_name,')') as group_name"
      )
      .innerJoin(
        "sales_channel",
        "a",
        "combo.combo_channel_id=a.sales_channel_id"
      )
      .innerJoin("menus", "b", "combo.menu_id=b.menu_id")
      .innerJoin("groups", "c", "combo.group_id=c.group_id")
      .innerJoin("subgroups", "d", "combo.subgroup_id=d.id")
      .orderBy("combo.combo_id", "DESC")
      .getRawMany();
  }

  async FpcombosData(): Promise<any> {
    return await this.comboRep
      .createQueryBuilder("combo")
      .select("combo.combo_id as value, combo.combo_name as label")
      .where("combo.is_foodpanda = :value", { value: 1 })
      .getRawMany();
  }
  async FpitemsData(): Promise<any> {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select("menu_item.menu_item_id as value, menu_item.item_name as label")
      .where("menu_item.is_foodpanda = :value", { value: 1 })
      .getRawMany();
  }
  async getComboById(combo_id: number): Promise<any> {
    return await this.comboRep.findOne({
      relations: [
        "images",
        "menu_id",
        "combo_channel_id",
        "group_id",
        "subgroup_id",
        "itemStores",
        "itemStores.store_id",
        "mainComboId",
        "mainComboId.mainComboModId",
        "mainComboId.mainComboModId.mod_group_id",
        "mainComboId.variant_id"
      ],
      where: {
        combo_id: combo_id
      }
    });
  }
  async getComboByIdFP(combo_id: number): Promise<any> {
    return await this.comboRep.findOne({
      where: {
        combo_id: combo_id
      }
    });
  }
  async getComboByPosCode(pos_code: number): Promise<any> {
    return await this.comboRep.findOne({
      where: {
        pos_code: pos_code
      }
    });
  }

  async getComboByPosCodeFP(erp_id: number): Promise<any> {
    return await this.comboRep.findOne({
      where: {
        erp_id: erp_id
      }
    });
  }
  async getChoiceByComboId(combo_id: number, choiceId: any): Promise<any> {
    return await this.comboChoiceRep.findOne({
      relations: ["menu_item_id", "group_id"],
      where: {
        combo_id: combo_id,
        size: choiceId
      }
    });
  }
  async getComboChoiceByCombo(comboId: any): Promise<any> {
    // console.log("pos_code",pos_code);

    // return await this.comboChoiceRep.findOne({
    //     relations: ['menu_item_id', 'group_id'],
    //     where: {
    //         combo_pos_code: combo_poscode, menu_variant_pos_code: menu_variant_pos_code, combo_is_publish: 1
    //     }
    // });
    return await this.comboChoiceRep.find({
      where: {
        combo_id: comboId
      }
    });
  }
  async getComboChoiceModifiersByCombo(comboId: any): Promise<any> {
    // console.log("pos_code",pos_code);

    // return await this.comboChoiceRep.findOne({
    //     relations: ['menu_item_id', 'group_id'],
    //     where: {
    //         combo_pos_code: combo_poscode, menu_variant_pos_code: menu_variant_pos_code, combo_is_publish: 1
    //     }
    // });
    return await this.comboChoiceModRep.findOne({
      relations: ["combo_choice_id", "mod_group_id"],
      where: {
        combo_id: comboId
      }
    });
  }
  async modifiersByPosCode(modPosCode: any): Promise<any> {
    return await this.modRep.findOne({
      relations: ["mod_variant_id", "mod_variant_id.variant_id"],

      where: {
        pos_code: modPosCode
      }
    });
  }

  async getModifiersByPosCode(modPosCode: any): Promise<any> {
    return await this.modRep.findOne({
      where: {
        pos_code: modPosCode
      }
    });
  }

  async modifiersByErpId(erp_id: any): Promise<any> {
    return await this.modRep.findOne({
      relations: ["mod_variant_id", "mod_variant_id.variant_id"],

      where: {
        erp_id: erp_id
      }
    });
  }

  async modifiersByPosWithRelation(pos_code: any): Promise<any> {
    return await this.modRep.findOne({
      relations: ["mod_variant_id", "mod_variant_id.variant_id"],
      where: {
        pos_code: pos_code
      }
    });
  }

  async getComboChoiceByModifierGroupID(
    comboID: any,
    modGroupID: any
  ): Promise<any> {
    console.log("tttttttttttttttttttt", comboID, modGroupID);

    return await this.comboChoiceModRep.findOne({
      relations: ["combo_choice_id"],
      where: {
        combo_id: comboID,
        mod_group_id: modGroupID
      }
    });
  }
  async getComboModifiersByPosCode(modPosCode: any): Promise<any> {
    // console.log("pos_code",pos_code);

    // return await this.comboChoiceRep.findOne({
    //     relations: ['menu_item_id', 'group_id'],
    //     where: {
    //         combo_pos_code: combo_poscode, menu_variant_pos_code: menu_variant_pos_code, combo_is_publish: 1
    //     }
    // });
    return await this.modRep.findOne({
      where: {
        pos_code: modPosCode
      }
    });
  }
  async getChoiceByComboandVarId(combo_id: any, variant_id: any): Promise<any> {
    return await this.comboChoiceRep.findOne({
      relations: ["combo_id", "menu_item_id"],
      where: {
        combo_id: combo_id,
        size: variant_id
      }
    });
  }

  async getTopDealCount() {
    return await this.comboRep
      .createQueryBuilder("combo")
      .select("COUNT(*) as count")
      .where("combo.topDeal=1")
      .getRawOne();
  }
  async editCombo(combo_id: number, combo: any): Promise<any> {
    return await this.comboRep.update(combo_id, combo);
  }
  async comboBlockUnblock(comboId, isActive): Promise<any> {
    let reason: any = isActive == 1 ? null : "Normal Block";
    return await this.comboRep
      .createQueryBuilder()
      .update(Combo)
      .set({ is_active: isActive, combo_block_reason: reason })
      .where("combo_id = :combo_id", { combo_id: comboId })
      .execute();
  }
  async suggestiveBlockUnblock(suggestiveId, isActive): Promise<any> {
    return await this.suggestive_base_products
      .createQueryBuilder()
      .update(SuggestiveBaseProducts)
      .set({ is_active: isActive })
      .where("id = :id", { id: suggestiveId })
      .execute();
  }
  async addCombo(combo: any): Promise<any> {
    return await this.comboRep.save(combo);
  }
  async addComboChoices(data: any): Promise<any> {
    return await this.comboChoiceRep.save(data);
  }
  async addComboChoicesModifiers(data: any): Promise<any> {
    return await this.comboChoiceModRep.save(data);
  }
  async addHeroItem(item: any) {
    return await this.heroRep.save(item);
  }
  async getItemHeroImages(menu_item_id: number) {
    return await this.heroRep.findOne({
      where: { menu_item_id: menu_item_id }
    });
  }
  async getComboHeroImages(combo_id: number) {
    return await this.heroRep.findOne({ where: { combo_id: combo_id } });
  }
  //Suggestive Deals
  async checkSuggestiveChildProductsForCombo(combo_id: number) {
    return await this.suggestive_base_products.find({
      relations: [
        "suggestive_child_products",
        "suggestive_child_products.suggestive_combo_id"
      ],
      where: { combo_id: combo_id }
    });
  }
  async checkSuggestiveProductsForComboandStore(
    store_id: number,
    combo_id: number
  ) {
    return await this.suggestive_base_products.findOne({
      where: { store_id: store_id, combo_id: combo_id }
    });
  }
  async deleteSuggestiveProd(id: any) {
    return await this.suggestive_base_products
      .createQueryBuilder()
      .delete()
      .from(SuggestiveBaseProducts)
      .where("id = :id", { id: id })
      .execute();
  }
  async deleteSuggestiveChildProd(id: any) {
    return await this.suggestive_child_products
      .createQueryBuilder()
      .delete()
      .from(SuggestiveChildProducts)
      .where("sugg_base_product_id = :sugg_base_product_id", {
        sugg_base_product_id: id
      })
      .execute();
  }
  async addSuggestiveDeals(data: any): Promise<any> {
    return await this.suggestive_base_products.save(data);
  }
  async addSuggestiveChildDeals(data: any): Promise<any> {
    return await this.suggestive_child_products.save(data);
  }
  //ModGroups queries
  async modGroupData(): Promise<any> {
    return await this.modGroupRep
      .createQueryBuilder("modifier_group")
      .select("modifier_group.*,a.group_name,b.menu_name")
      .leftJoin("groups", "a", "modifier_group.group_id=a.group_id")
      .leftJoin("menus", "b", "a.menu_id=b.menu_id")
      .orderBy("modifier_group.mod_group_id", "DESC")
      .getRawMany();
  }
  async GetModGroupById(mod_group_id: number): Promise<any> {
    return await this.modGroupRep
      .createQueryBuilder("modifier_group")
      .select("modifier_group.*")
      .where("modifier_group.mod_group_id= :mod_group_id", {
        mod_group_id: mod_group_id
      })
      .getRawOne();
  }
  async editModGroup(mod_group_id: number, modGroup: any): Promise<any> {
    return await this.modGroupRep.update(mod_group_id, modGroup);
  }
  async addModGroup(group: any): Promise<any> {
    return await this.modGroupRep.save(group);
  }
  async modGroupsBlockUnblock(modGroupId, isActive): Promise<any> {
    return await this.modGroupRep
      .createQueryBuilder()
      .update(ModifierGroup)
      .set({ is_active: isActive })
      .where("mod_group_id = :mod_group_id", { mod_group_id: modGroupId })
      .execute();
  }
  async modifierBlockUnblock(modId, isActive): Promise<any> {
    return await this.modRep
      .createQueryBuilder()
      .update(ModifierOption)
      .set({ is_active: isActive })
      .where("modifier_id = :mod_id", { mod_id: modId })
      .execute();
  }
  async GetModifiersByModGroupId(mod_group_id: number): Promise<any> {
    return await this.modGroupRep
      .createQueryBuilder("modifier_group")
      .select("modifier_group.*,a.*")
      .leftJoin("modifiers", "a", "modifier_group.mod_group_id=a.mod_group_id")
      .where("modifier_group.mod_group_id= :mod_group_id", {
        mod_group_id: mod_group_id
      })
      .getRawMany();
  }
  async modifierData(): Promise<any> {
    return await this.modRep
      .createQueryBuilder("modifiers")
      .select("modifiers.*,a.mod_group_name,b.group_name,c.menu_name")
      .leftJoin("modifier_group", "a", "modifiers.mod_group_id=a.mod_group_id")
      .leftJoin("groups", "b", "a.group_id=b.group_id")
      .leftJoin("menus", "c", "b.menu_id=c.menu_id")
      .where(`modifiers.isFoodPanda= 0`)
      .orderBy("modifiers.modifier_id", "DESC")
      .getRawMany();
  }
  async fpModifierData(): Promise<any> {
    return await this.modRep
      .createQueryBuilder("modifiers")
      .select("modifiers.*,a.mod_group_name,b.group_name,c.menu_name")
      .leftJoin("modifier_group", "a", "modifiers.mod_group_id=a.mod_group_id")
      .leftJoin("groups", "b", "a.group_id=b.group_id")
      .leftJoin("menus", "c", "b.menu_id=c.menu_id")
      .where(`modifiers.isFoodPanda= 1`)
      .orderBy("modifiers.modifier_id", "DESC")
      .getRawMany();
  }
  async GetModifierById(modifier_id: number): Promise<any> {
    return await this.modRep
      .createQueryBuilder("modifiers")
      .select("modifiers.*,a.image_url")
      .leftJoin("images", "a", "modifiers.modifier_id=a.modifier_id")
      .where("modifiers.modifier_id= :modifier_id", {
        modifier_id: modifier_id
      })
      .getRawOne();
  }
  async getModVariantByModId(modifier_id: any) {
    return await this.varRelRep
      .createQueryBuilder("variant_relationships")
      .select("variant_relationships.*,a.name as variant_name")
      .leftJoin("variants", "a", "a.id = variant_relationships.variant_id")
      .where(`variant_relationships.modifier_id = ${modifier_id}`)
      .getRawMany();
  }
  async editModifier(modifier_id: number, modifier: any): Promise<any> {
    return await this.modRep.update(modifier_id, modifier);
  }
  async addModifier(modifier: any): Promise<any> {
    return await this.modRep.save(modifier);
  }
  async addVariatnsRelations(modVar: any) {
    return await this.varRelRep.save(modVar);
  }
  async deleteVarRel(modifier_id, discount_id): Promise<any> {
    return await this.varRelRep
      .createQueryBuilder()
      .delete()
      .from(VariantRelations)
      .where(`modifier_id = :modifier_id`, { modifier_id: modifier_id })
      .orWhere(`discount_id = :discount_id`, { discount_id: discount_id })
      .execute();
  }
  async GetModGroups(): Promise<any> {
    return await this.modGroupRep
      .createQueryBuilder("modifier_group")
      .select(
        `modifier_group.mod_group_id,modifier_group.mod_group_id as value,CASE WHEN modifier_group.mod_group_name_hint IS NULL 
            THEN modifier_group.mod_group_name 
            ELSE CONCAT(modifier_group.mod_group_name, ' - ', modifier_group.mod_group_name_hint) 
       END as label, modifier_group.is_crust,modifier_group.is_flavour,modifier_group.is_toping,modifier_group.is_condiment,modifier_group.is_addon,modifier_group.mod_group_name_hint`
      )
      .where("modifier_group.is_active=1")
      .getRawMany();
  }
  async deleteModifier(modifier_id: number) {
    return this.modRep.delete(modifier_id);
  }
  async getAllPublishModifiers(): Promise<any> {
    return await this.modRep
      .createQueryBuilder("modifiers")
      .select("modifiers.*")
      .where("is_publish=1 AND isFoodPanda=0 AND mod_groups_json IS NOT NULL")
      .getRawMany();
  }
  ///Menu For Stores
  async addStoresForMenu(menuStores: any) {
    return await this.storeMenuRep.save(menuStores);
  }
  async checkGroupForStores(store_id: any, group_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: store_id, group_id: group_id }
    });
  }
  async checkSubGroupForStores(store_id: any, group_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: store_id, subgroup_id: group_id }
    });
  }
  async checkMenuItemForStores(store_id: any, menu_item_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: store_id, menu_item_id: menu_item_id }
    });
  }
  async checkBannersForStores(store_id: any, banner_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: store_id, banner_id: banner_id }
    });
  }
  async checkMenuItemForExceptStores(store_id: any, menu_item_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: !store_id, menu_item_id: menu_item_id }
    });
  }
  async checkComboForStores(store_id: any, combo_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: store_id, combo_id: combo_id }
    });
  }
  async checkComboForExceptStores(store_id: any, combo_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: !store_id, combo_id: combo_id }
    });
  }
  async checkModifierForStores(store_id: any, modifier_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: store_id, modifier_id: modifier_id }
    });
  }
  async checkItemVariantsForStores(store_id: any, item_variant_id: any) {
    return await this.storeMenuRep.findOne({
      where: { store_id: store_id, item_variant_id: item_variant_id }
    });
  }
  //Banners queries
  async bannerData(): Promise<any> {
    return await this.bannersRep
      .createQueryBuilder("banners")
      .select(
        "banners.*,a.item_name as sync_item_name,b.coupon_name as sync_coupon_name"
      )
      .leftJoin("menu_item", "a", "banners.sync_type_id=a.menu_item_id")
      .leftJoin("coupon", "b", "banners.sync_type_id=b.coupon_id")
      .orderBy("banners.date_created", "DESC")
      .getRawMany();
  }
  async getBannerById(banner_id: number): Promise<any> {
    return await this.bannersRep.findOne({
      relations: ["itemStores", "itemStores.store_id"],
      where: {
        id: banner_id
      }
    });
  }
  async addBanner(banner: any): Promise<any> {
    return await this.bannersRep.save(banner);
  }
  async bannerBlockUnblock(bannerId, isActive): Promise<any> {
    return await this.bannersRep
      .createQueryBuilder()
      .update(Banners)
      .set({ is_active: isActive })
      .where("id = :id", { id: bannerId })
      .execute();
  }
  async editBanner(id: number, banner: any): Promise<any> {
    return await this.bannersRep.update(id, banner);
  }
  async getItemsForBanners() {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select("menu_item_id as value,item_name as label,menu_item_id")
      .where("menu_item.is_publish= 1")
      .getRawMany();
  }
  async getCombosForBanners() {
    return await this.comboRep
      .createQueryBuilder("combo")
      .select("combo_id as value,combo_name as label,combo_id")
      .where("combo.is_publish= 1")
      .andWhere("combo.is_voucher=0")
      .getRawMany();
  }
  async getSuggCombos() {
    return await this.comboRep
      .createQueryBuilder("combo")
      .select("combo_id as value,combo_name as label,combo_id")
      .where("combo.is_suggestive_deal = 1")
      .getRawMany();
  }
  async getSuggestiveCombos() {
    return await this.suggestive_base_products.find({
      relations: [
        "combo_id",
        "suggestive_child_products",
        "suggestive_child_products.suggestive_combo_id"
      ]
    });
  }

  async getSuggestiveById(id: number): Promise<any> {
    return await this.suggestive_base_products.findOne({
      relations: [
        "combo_id",
        "suggestive_child_products",
        "suggestive_child_products.suggestive_combo_id",
        "itemStores",
        "itemStores.store_id"
      ],
      where: {
        id: id
      }
    });
  }
  async editSuggestive(suggestive_id: number, suggestive: any): Promise<any> {
    let success: any = await this.suggestive_base_products.update(
      suggestive_id,
      suggestive
    );
    if (success.affected > 0) {
      return await this.suggestive_base_products.findOne({
        relations: ["suggestive_child_products"],
        where: {
          id: suggestive_id
        }
      });
    }
  }
  async bulkCouponUpdateByPOSCode(type: string, param: string, data: Partial<Coupon>): Promise<any> {
    return await this.couponRep.update({ [type]: param }, data );
  }
  //Coupons queries
  async CouponData(): Promise<any> {
    return await this.couponRep.find({
      relations: [
        "storeCouponId",
        "storeCouponId.store_id",
        "customerCouponId",
        "customerCouponId.customer_id",
        "menuCouponId",
        "menuCouponId.menu_id",
        "groupCouponId",
        "groupCouponId.group_id",
        "groupCouponId.group_id.menu_id"
      ],
      order: { coupon_id: "DESC" }
    });
  }
  async GetCouponById(coupon_id: number): Promise<any> {
    return await this.couponRep.findOne({
      relations: [
        "storeCouponId",
        "storeCouponId.store_id",
        "customerCouponId",
        "customerCouponId.customer_id",
        "menuCouponId",
        "menuCouponId.menu_id",
        "groupCouponId",
        "groupCouponId.group_id",
        "groupCouponId.group_id.menu_id"
      ],
      where: { coupon_id: coupon_id }
    });
  }
  async GetCouponByCouponCode(coupon_code: number): Promise<any> {
    return await this.couponRep.findOne({
      where: { coupon_code: coupon_code }
    });
  }
  async editCoupon(coupon_id: number, coupon: any): Promise<any> {
    return await this.couponRep.update(coupon_id, coupon);
  }
  async addCoupon(coupon: any): Promise<any> {
    return await this.couponRep.save(coupon);
  }
  async addBulkCoupons(coupon: any) {
    return await this.couponRep
      .createQueryBuilder()
      .insert()
      .into(Coupon)
      .values(coupon)
      .orIgnore()
      .execute();
  }
  async getLastInsertedCouponIds(coupon_id: any) {
    return await this.couponRep.find({
      select: ["coupon_id"],
      where: { coupon_id: MoreThanOrEqual(coupon_id) }
    });
  }
  async deleteEmpCoupon(coupon_id: number) {
    return await this.empCouponRep
      .createQueryBuilder()
      .delete()
      .from(EmpCoupons)
      .where("coupon_id = :coupon_id", { coupon_id: coupon_id })
      .execute();
  }
  async deleteCoupon(coupon_id: number) {
    return this.couponRep.delete(coupon_id);
  }
  async couponActiveInactive(coupon_id, isArchive): Promise<any> {
    return await this.couponRep
      .createQueryBuilder()
      .update(Coupon)
      .set({ is_archive: isArchive })
      .where("coupon_id = :coupon_id", { coupon_id: coupon_id })
      .execute();
  }
  async addStoresForCoupons(couponStores: any) {
    return await this.storeCouponRep.save(couponStores);
  }
  async addMenusForCoupons(CouponMenus: any) {
    return await this.menuCouponRep.save(CouponMenus);
  }
  async addGroupsForCoupons(couponGroup: any) {
    return await this.groupCouponRep.save(couponGroup);
  }
  async removeCouponStore(couponId: any): Promise<any> {
    return await this.storeCouponRep
      .createQueryBuilder()
      .delete()
      .from(CouponStores)
      .where("coupon_id = :coupon_id", { coupon_id: couponId })
      .execute();
  }
  async removeCouponMenu(couponId: any): Promise<any> {
    return await this.menuCouponRep
      .createQueryBuilder()
      .delete()
      .from(CouponMenus)
      .where("coupon_id = :coupon_id", { coupon_id: couponId })
      .execute();
  }
  async removeCouponGroup(couponId: any): Promise<any> {
    return await this.groupCouponRep
      .createQueryBuilder()
      .delete()
      .from(CouponGroups)
      .where("coupon_id = :coupon_id", { coupon_id: couponId })
      .execute();
  }
  async addCustomersForCoupons(couponCustomers: any) {
    return await this.customerCouponRep.save(couponCustomers);
  }
  async updateAssignedCouponCustomers(
    couponId: any,
    assigned: any,
    customer_id?: any
  ): Promise<any> {
    if (customer_id) {
      return await this.customerCouponRep
        .createQueryBuilder()
        .update(CouponUsageByCustomer)
        .set({ assigned: assigned })
        .where("coupon_id = :coupon_id && customer_id = :customer_id", {
          coupon_id: couponId,
          customer_id: customer_id
        })
        .execute();
    } else {
      return await this.customerCouponRep
        .createQueryBuilder()
        .update(CouponUsageByCustomer)
        .set({ assigned: assigned })
        .where("coupon_id = :coupon_id", { coupon_id: couponId })
        .execute();
    }
  }
  async GetCouponCustomerBycouponId(
    coupon_id: number,
    customer_id: any
  ): Promise<any> {
    return await this.customerCouponRep.findOne({
      relations: ["customer_id"],
      where: { coupon_id: coupon_id, customer_id: customer_id }
    });
  }
  async activeEmpCoupons(): Promise<any> {
    return await this.couponRep
      .createQueryBuilder("coupon")
      .select("coupon_id as value,coupon_code as label")
      .orderBy("coupon.date_created", "DESC")
      .where("coupon.is_archive=0")
      .andWhere("coupon.is_emp=1")
      .getRawMany();
  }

  // pagination coupen table record fetch

  async countCouponDataFetch(): Promise<any> {
    return await this.couponRep.count();
  }

  async CouponDataForPaginations(offset: number, limit: number): Promise<any> {
    return await this.couponRep.find({
      select: [
        "coupon_id",
        "coupon_name",
        "coupon_code",
        "pos_code",
        "coupon_description",
        "discount_type",
        "coupon_value",
        "is_archive",
        "start_date",
        "expire_date",
        "total_usage",
        "mode",
        "channel",
        "type",
        "type_id",
        "percent",
        "limit",
        "for_customer",
        "min_amount",
        "min_orders",
        "total_usage_customer",
        "is_emp",
        "is_partner",
        "is_vip",
        "is_nps"
      ],
      order: { coupon_id: "DESC" },
      skip: offset, // Specify the offset for pagination
      take: limit // Specify the limit for pagination
    });
  }

  async countSearchCouponsDataFetch(type: string, value: string): Promise<any> {
    return await this.couponRep.find({
      where: {
        [type]: value
      },
      order: { coupon_id: "DESC" }
    });
  }

  async massActiveInactiveCoupon(
    pos_code: string,
    status: number
  ): Promise<any> {
    return await this.couponRep
      .createQueryBuilder()
      .update(Coupon)
      .set({ is_archive: status })
      .where("pos_code = :pos_code", { pos_code: pos_code })
      .execute();
  }

  async searchCouponsByCode(
    type: string,
    value: string,
    offset: number,
    limit: number
  ): Promise<any> {
    return await this.couponRep.find({
      where: {
        [type]: value
      },
      order: { coupon_id: "DESC" },
      skip: offset,
      take: limit
    });
  }

  //Discounts queries
  async DiscountData(): Promise<any> {
    return await this.discountRep
      .createQueryBuilder("discounts")
      .select("discounts.*,a.menu_name")
      .leftJoin("menus", "a", "discounts.type_id=a.menu_id")
      .orderBy("discounts.discount_id", "DESC")
      .getRawMany();
  }
  async FPDiscountData(): Promise<any> {
    return await this.fpdiscountRep
      .createQueryBuilder("fp_discounts")
      .select("fp_discounts.*")
      .orderBy("fp_discounts.id", "DESC")
      .getRawMany();
  }
  async GetDiscountById(discount_id: number): Promise<any> {
    return await this.discountRep.findOne({
      relations: [
        "storeDiscountId",
        "storeDiscountId.store_id",
        "discount_variant_id",
        "discount_variant_id.variant_id"
      ],
      where: { discount_id: discount_id }
    });
  }
  async GetFPDiscountById(id: number): Promise<any> {
    return await this.fpdiscountRep.findOne({ where: { id: id } });
  }
  async editDiscount(discount_id: number, discounts: any): Promise<any> {
    return await this.discountRep.update(discount_id, discounts);
  }
  async editfpDiscount(discount_id: number, discounts: any): Promise<any> {
    return await this.fpdiscountRep.update(discount_id, discounts);
  }
  async addDiscount(discounts: any): Promise<any> {
    return await this.discountRep.save(discounts);
  }
  async addFPDiscount(fpdiscounts: any): Promise<any> {
    return await this.fpdiscountRep.save(fpdiscounts);
  }
  async fpdiscountActiveInactive(id, is_active): Promise<any> {
    return await this.fpdiscountRep
      .createQueryBuilder()
      .update(FPDiscounts)
      .set({ is_active: is_active })
      .where("id = :id", { id: id })
      .execute();
  }
  async deleteDiscount(discount_id: number) {
    return this.discountRep.delete(discount_id);
  }
  async discountActiveInactive(discount_id, is_active): Promise<any> {
    return await this.discountRep
      .createQueryBuilder()
      .update(Discounts)
      .set({ is_active: is_active })
      .where("discount_id = :discount_id", { discount_id: discount_id })
      .execute();
  }
  async removeDiscountStore(discount_id: any): Promise<any> {
    return await this.storeDiscountRep
      .createQueryBuilder()
      .delete()
      .from(DiscountStores)
      .where("discount_id = :discount_id", { discount_id: discount_id })
      .execute();
  }
  async addStoresForDiscounts(discountStores: any) {
    return await this.storeDiscountRep.save(discountStores);
  }
  //Promos queries
  async PromoData(): Promise<any> {
    return await this.promoRep
      .createQueryBuilder("promo")
      .select("promo.*,a.menu_name")
      .leftJoin("menus", "a", "promo.menu_id=a.menu_id")
      .orderBy("promo.promo_id", "DESC")
      .getRawMany();
  }
  async GetPromoById(promo_id: number): Promise<any> {
    return await this.promoRep
      .createQueryBuilder("promo")
      .select("promo.*")
      .where("promo.promo_id= :promo_id", { promo_id: promo_id })
      .getRawOne();
  }
  async editPromo(promo_id: number, promo: any): Promise<any> {
    return await this.promoRep.update(promo_id, promo);
  }
  async addPromo(promo: any): Promise<any> {
    return await this.promoRep.save(promo);
  }
  async deletePromo(promo_id: number) {
    return this.promoRep.delete(promo_id);
  }
  async promoActiveInactive(promo_id, isArchive): Promise<any> {
    return await this.promoRep
      .createQueryBuilder()
      .update(Promo)
      .set({ is_archive: isArchive })
      .where("promo_id = :promo_id", { promo_id: promo_id })
      .execute();
  }
  ///
  async getAllActiveFireBaseCustomers(offset) {
    return await this.customerRep
      .createQueryBuilder("customers")
      .select("device_token,ios_device_token")
      .where(
        `customers.device_token IS NOT NULL OR customers.ios_device_token IS NOT NULL`
      )
      .limit(10000)
      .offset(offset)
      .getRawMany();
  }
  async getTotalCustomers() {
    return await this.customerRep
      .createQueryBuilder("customers")
      .select("device_token")
      .where(
        `customers.device_token IS NOT NULL OR customers.ios_device_token IS NOT NULL`
      )
      .getCount();
  }
  //Push Notification queries
  async PushNotificationsData(): Promise<any> {
    return await this.notificationRep.find({
      order: {
        id: "DESC"
      }
    });
  }
  async GetNotificationById(notification_id: number): Promise<any> {
    return await this.notificationRep
      .createQueryBuilder("push_notification")
      .select("push_notification.*")
      .where("push_notification.id= :id", { id: notification_id })
      .getRawOne();
  }
  async editNotification(id: number, push_notification: any): Promise<any> {
    return await this.notificationRep.update(id, push_notification);
  }
  async addNotification(notification: any): Promise<any> {
    return await this.notificationRep.save(notification);
  }
  async deleteNotification(id: number) {
    return this.notificationRep.delete(id);
  }
  async notificationActiveInactive(notification_id, is_active): Promise<any> {
    return await this.notificationRep
      .createQueryBuilder()
      .update(PushNotification)
      .set({ is_active: is_active })
      .where("id = :id", { id: notification_id })
      .execute();
  }
  //Settings Querries
  async saveDiscount(discount: Discounts): Promise<any> {
    return await this.discountRep
      .createQueryBuilder()
      .update(Discounts)
      .set({ discount_value: discount.discount_value })
      .where("discount_id = :discount_id", {
        discount_id: discount.discount_id
      })
      .execute();
  }
  async saveDeliveryModes(data: any): Promise<any> {
    return await this.orderModeRep
      .createQueryBuilder()
      .update(OrderModes)
      .set({ free_delivery: data.free_delivery })
      .where("id = :order_mode_id", { order_mode_id: data.mode_id })
      .execute();
  }
  async saveDelivery(data: State): Promise<any> {
    return await this.storeRep
      .createQueryBuilder()
      .update(Store)
      .set({ delivery_fee: data.delivery_fee })
      .where({ store_id: data.store_id })
      .execute();
  }
  async getDeliveryTiming(): Promise<any> {
    return await this.DeliveryTimingsRep.findOne();
  }
  async addDeliveryTiming(data: any): Promise<any> {
    return await this.DeliveryTimingsRep.save(data);
  }
  async saveDeliveryTiming(data): Promise<any> {
    return await this.DeliveryTimingsRep.createQueryBuilder()
      .update(DeliveryTimings)
      .set({
        start_time: data.start_time,
        end_time: data.end_time
      })
      .execute();
  }
  async savePOSFEE(data: State): Promise<any> {
    return await this.stateRep
      .createQueryBuilder()
      .update(State)
      .set({ pos_service_fee: data.pos_service_fee })
      .where("state_name = :state_name", { state_name: data.state_name })
      .execute();
  }
  //Roles Queries
  async rolesData() {
    return this.roleRep.find();
  }
  async addRole(role: any): Promise<any> {
    return await this.roleRep.save(role);
  }
  async editRole(role_id: number, role: any): Promise<any> {
    return await this.roleRep.update(role_id, role);
  }
  async getRoleById(role_id: number): Promise<any> {
    return await this.roleRep.findOne({ where: { role_id: role_id } });
  }
  async delRole(role_id: number) {
    return this.roleRep.delete(role_id);
  }
  //Feedback Applications
  async getAllFeedback(store_ids: any) {
    return await this.feedbackRep
      .createQueryBuilder("feedback")
      .select("feedback.*,a.store_name,a.address as store_address")
      .innerJoin("stores", "a", "feedback.store_id=a.store_id")
      .where(`feedback.store_id IN (${store_ids})`)
      .andWhere(
        `DATE(feedback.date_created)>= DATE_SUB(CURDATE(),INTERVAL 0 DAY)`
      )
      .orderBy("feedback.id", "DESC")
      .getRawMany();
  }
  //Franchise Applications
  async getAllFranchiseApp() {
    return await this.franchiseRep
      .createQueryBuilder("franchise")
      .select("franchise.*")
      .orderBy("franchise.id", "DESC")
      .getRawMany();
  }
  //for User Group Stores
  async addDefaultGroup(defaultGroup: any) {
    return await this.userGroupsRep.save(defaultGroup);
  }
  async addGroupStore(groupStore: any) {
    return await this.groupStoreRep.save(groupStore);
  }
  async getStoresByGroupId(group_id: any) {
    return this.groupStoreRep.find({
      relations: ["store_id"],
      where: { user_group_id: group_id }
    });
  }
  async allAccessGroups() {
    return this.userGroupsRep.find({ where: { all_access: 1 } });
  }
  //Predefined Things
  async saleChannelData() {
    return this.saleschannelRep.find();
  }
  async variantsData() {
    return await this.variantsRep
      .createQueryBuilder("variants")
      .select("id as value, name as label ")
      .where("is_active=1")
      .getRawMany();
  }
  async discountlData() {
    return this.discountRep.find();
  }
  async storeTypeData() {
    return this.storetypeRep.find();
  }
  async countryData() {
    return this.countryRep.find();
  }
  async getStoresListByCountryandState(country_id: any, state_id: any) {
    return this.storeRep.find({
      select: ["store_id", "store_name"],
      where: { country_id: country_id, state_id: state_id }
    });
  }
  async getDeliveryFeeByStateandStore(
    country_id: any,
    state_id: any,
    store_id: any
  ) {
    return this.storeRep.findOne({
      select: ["delivery_fee"],
      where: { country_id: country_id, state_id: state_id, store_id: store_id }
    });
  }

  async comboOptionData(menu_id: any): Promise<any> {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select(
        "menu_item_id as id,item_name as value,item_name as label,menu_item.pos_code,a.pos_code as group_poscode,a.group_name"
      )
      .innerJoin("groups", "a", "menu_item.item_group_id=a.group_id")
      .where("a.menu_id= :menu_id", { menu_id: menu_id })
      .getRawMany();
  }
  async getItemsVariations(id: any) {
    return await this.itemRep.findOne({
      relations: [
        "variants",
        "variants.variant_id",
        "item_group_id",
        "variants.orderModePrice",
        "variants.orderModePrice.order_mode_id"
      ],
      where: { menu_item_id: id }
    });
  }
  async getComboChoiceMenuItems(group_id: any) {
    return await this.groupRep
      .createQueryBuilder("groups")
      .select("menu_item_id as value,item_name as label")
      .innerJoin("menu_item", "a", "groups.group_id=a.item_group_id")
      .where("groups.group_id= :group_id", { group_id: group_id })
      .getRawMany();
  }
  async groupOptionsData() {
    return await this.groupRep
      .createQueryBuilder("groups")
      .select("group_id as value,group_name as label,category_type")
      .orderBy("groups.date_created", "DESC")
      .where("groups.is_active=1")
      .getRawMany();
  }
  async subgroupOptionsData() {
    return await this.subgroupRep
      .createQueryBuilder("subgroups")
      .select("id as value,sub_group_name as label,category_type")
      .innerJoin("groups", "a", "subgroups.group_id=a.group_id")
      .orderBy("subgroups.date_created", "DESC")
      .where("subgroups.is_active=1")
      .getRawMany();
  }
  async getAllbusinessTypes() {
    return this.businessTypeRep.find({ where: { is_active: 1 } });
  }
  async getAllActiveItems() {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select("menu_item_id as value,item_name as label,menu_item_id")
      .where("menu_item.is_active= 1")
      .getRawMany();
  }
  async getAllActiveCombo() {
    return await this.comboRep
      .createQueryBuilder("combo")
      .select("combo_id as value,combo_name as label,combo_id")
      .where("combo.is_active= 1")
      .getRawMany();
  }
  async getBackupStoreByStoreId(id: any) {
    return await this.backupStoreRep.find({
      relations: ["backupStoreId"],
      where: { storeId: id }
    });
  }
  async addBackupStore(store: any): Promise<any> {
    return await this.backupStoreRep.save(store);
  }
  async removeBackupStore(storeId: any): Promise<any> {
    // return await this.backupStoreRep.delete(store);
    return await this.backupStoreRep
      .createQueryBuilder()
      .delete()
      .from(BackupStore)
      .where("storeId = :storeId", { storeId: storeId })
      .execute();
  }

  async addLogs(data: any) {
    return await this.activitylogsRep.save(data);
  }
  async addStoreToken(store: any): Promise<any> {
    return await this.storeTokenRep.save(store);
  }

  async GetOrderById(order_id: any) {
    return await this.orderRep.findOne({
      relations: ["customer_id", "store_id", "order_status_code"],
      where: { order_id: order_id }
    });
  }

  async npsData(startDate: string, endDate: string): Promise<any> {
    return await this.surveyFeedbackRep
      .createQueryBuilder("survey_feedback")
      .select(
        "survey_feedback.id as id, CONCAT(d.first_name, ' ', d.last_name) as user, e.store_name as store, survey_feedback.order_id, a.question, b.option as answer, survey_feedback.date_created"
      )
      .innerJoin(
        "survey_questions",
        "a",
        "survey_feedback.survey_question=a.id"
      )
      .innerJoin("survey_options", "b", "survey_feedback.survey_option=b.id")
      .innerJoin("orders", "c", "survey_feedback.order_id=c.order_id")
      .innerJoin("customers", "d", "c.customer_id=d.customer_id")
      .innerJoin("stores", "e", "c.store_id=e.store_id")
      .where(
        "survey_feedback.date_created BETWEEN '" +
          startDate +
          "' AND '" +
          endDate +
          "'"
      )
      .orderBy("survey_feedback.id", "DESC")
      .getRawMany();
  }

  async customerData(startDate: string, endDate: string): Promise<any> {
    return await this.customerRep
      .createQueryBuilder("customers")
      .select(
        "customer_id as id, CONCAT(first_name, ' ', last_name) as name, email_address, phone_number, customer_birthday as dob, date_created"
      )
      .where("date_created BETWEEN '" + startDate + "' AND '" + endDate + "'")
      .orderBy("customer_id", "DESC")
      .getRawMany();
  }

  async customerByPhone(phone: string) {
    return await this.customerRep.findOne({
      where: {
        phone_number: phone,
        is_guest: 0
      }
    });
  }
  async addAddress(address: any): Promise<any> {
    return await this.addressRep.save(address);
  }
  async reactiveAccount(data: any) {
    const delete_reason: any = null;
    return await this.customerRep.update(data.customer_id, {
      is_delete: 0,
      delete_reason: delete_reason
    });
  }
  async stores() {
    return this.storeRep.find();
  }
  async getStoreAggBranchCode(fp_branch_code: any) {
    return this.storeRep.findOne({
      select: ["store_id"],
      where: { fp_branch_code: fp_branch_code }
    });
  }
  async getCities() {
    return this.cityRep.find({
      order: {
        id: "DESC"
      },
      relations: ["country_id"],
      where: { is_active: 1 }
    });
  }
  async saveCity(data: any): Promise<any> {
    return await this.cityRep.save(data);
  }
  async editCity(id: number, city: any): Promise<any> {
    return await this.cityRep.update(id, city);
  }
  async cityData(city_id: number) {
    return this.cityRep.findOne({
      relations: ["country_id"],
      where: { id: city_id, is_active: 1 }
    });
  }
  async cityDataByName(city: string) {
    return this.cityRep.findOne({
      relations: ["country_id"],
      where: { name: city, is_active: 1 }
    });
  }
  async delCity(id: number) {
    return await this.cityRep
      .createQueryBuilder()
      .delete()
      .where(`id = ${id}`)
      .execute();
  }
  async activeInActiveCity(id: number) {
    return await this.cityRep
      .createQueryBuilder()
      .update(City)
      .set({ is_active: 0 })
      .where(`id = ${id}`)
      .execute();
  }
  //Order Mode Prices
  async addOrderModePrices(modePrice: any): Promise<any> {
    return await this.orderModePriceRep.save(modePrice);
  }
  async deleteModePriceForVariant(variant_id: number) {
    return await this.orderModePriceRep
      .createQueryBuilder()
      .delete()
      .from(OrderModesPrice)
      .where("item_variant_id = :item_variant_id", {
        item_variant_id: variant_id
      })
      .execute();
  }
  async deleteMenuBlockLogsForVariant(variant_id: number) {
    return await this.menuLogs
      .createQueryBuilder()
      .delete()
      .from(MenuLogs)
      .where("item_variant_id = :item_variant_id", {
        item_variant_id: variant_id
      })
      .execute();
  }
  async deleteStoreMenuItemVar(variant_id: any) {
    return await this.storeMenuRep
      .createQueryBuilder()
      .delete()
      .from(StoresMenu)
      .where("item_variant_id = :item_variant_id", {
        item_variant_id: variant_id
      })
      .execute();
  }
  async deleteStoreMenuModifier(modifier_id: any) {
    return await this.storeMenuRep
      .createQueryBuilder()
      .delete()
      .from(StoresMenu)
      .where("modifier_id = :modifier_id", { modifier_id: modifier_id })
      .execute();
  }
  async deleteModePriceForCombo(combo_id: number) {
    return await this.orderModePriceRep
      .createQueryBuilder()
      .delete()
      .from(OrderModesPrice)
      .where("combo_id = :combo_id", { combo_id: combo_id })
      .execute();
  }
  //OrderModes
  async getOrderModesForFbrSettings(): Promise<any> {
    return await this.orderModeRep.find({
      relations: ["modeStoreId", "modeStoreId.store_id"]
    });
  }
  async getOrderModes(): Promise<any> {
    return await this.orderModeRep.find({ where: { is_active: 1 } });
  }
  async getOrderModesPrice(item_variant_id: any): Promise<any> {
    return await this.orderModePriceRep.find({
      relations: ["order_mode_id"],
      where: { item_variant_id: item_variant_id }
    });
  }
  async getOrderModesComboPrice(combo_id: any): Promise<any> {
    return await this.orderModePriceRep.find({
      relations: ["order_mode_id"],
      where: { combo_id: combo_id }
    });
  }
  async modEnableDisableFbr(id, FbrCheck): Promise<any> {
    return await this.orderModeRep
      .createQueryBuilder()
      .update(OrderModes)
      .set({ fbr_check: FbrCheck })
      .where(`id = ${id}`)
      .execute();
  }
  async getOrderModeByID(id: number): Promise<any> {
    return this.orderModeRep.findOne({
      relations: ["modeStoreId", "modeStoreId.store_id"],
      where: { id: id }
    });
  }
  async editOrderMode(order_mode_id: number, mode: any): Promise<any> {
    return await this.orderModeRep.update(order_mode_id, mode);
  }
  async addStoresForOrderMode(menuStores: any) {
    return await this.modeStoreRep.save(menuStores);
  }
  async deleteModeStores(order_mode_id: any) {
    return await this.modeStoreRep
      .createQueryBuilder()
      .delete()
      .from(ModeStoreRelation)
      .where("order_mode_id = :order_mode_id", { order_mode_id: order_mode_id })
      .execute();
  }
  async checkModeForStores(store_id: any, order_mode_id: any) {
    return await this.modeStoreRep.findOne({
      where: { store_id: store_id, order_mode_id: order_mode_id }
    });
  }
  async checkModesForStoresByAlohaBranchCode(store_id: any) {
    return await this.modeStoreRep.find({
      relations: ["order_mode_id"],
      where: { store_id: store_id }
    });
  }
  //Item Modes
  async deleteItemModes(
    menu_item_id: any,
    combo_id: any,
    group_id: any,
    subgroup_id: any
  ) {
    if (menu_item_id) {
      return await this.itemModesRep
        .createQueryBuilder()
        .delete()
        .from(ItemModes)
        .where("menu_item_id = :menu_item_id", { menu_item_id: menu_item_id })
        .execute();
    } else if (combo_id) {
      return await this.itemModesRep
        .createQueryBuilder()
        .delete()
        .from(ItemModes)
        .where("combo_id = :combo_id", { combo_id: combo_id })
        .execute();
    } else if (group_id) {
      return await this.itemModesRep
        .createQueryBuilder()
        .delete()
        .from(ItemModes)
        .where("group_id = :group_id", { group_id: group_id })
        .execute();
    } else if (subgroup_id) {
      return await this.itemModesRep
        .createQueryBuilder()
        .delete()
        .from(ItemModes)
        .where("subgroup_id = :subgroup_id", { subgroup_id: subgroup_id })
        .execute();
    }
  }
  async addOrderItemMode(itemMode: any): Promise<any> {
    return await this.itemModesRep.save(itemMode);
  }
  async getItemModesByItem(menu_item_id: any): Promise<any> {
    return await this.itemModesRep.find({
      relations: ["order_mode_id"],
      where: { menu_item_id: menu_item_id }
    });
  }
  async getItemModesByCombo(combo_id: any): Promise<any> {
    return await this.itemModesRep.find({
      relations: ["order_mode_id"],
      where: { combo_id: combo_id }
    });
  }
  async getItemModesByGroup(group_id: any): Promise<any> {
    return await this.itemModesRep.find({
      relations: ["order_mode_id"],
      where: { group_id: group_id }
    });
  }
  async getItemModesBySubGroup(group_id: any): Promise<any> {
    return await this.itemModesRep.find({
      relations: ["order_mode_id"],
      where: { subgroup_id: group_id }
    });
  }
  async getAllComboChoices(): Promise<any> {
    return await this.comboChoiceRep.find({ relations: ["combo_id"] });
  }
  async updateComboChoicesPosCode(id: any, data: any): Promise<any> {
    return await this.comboChoiceRep.update(id, data);
  }
  async getSpecificStorePricesByItem(menu_item_id: any): Promise<any> {
    return await this.storeSpecificPriceRep.find({
      relations: ["store_id", "menu_item_id"],
      where: { menu_item_id: menu_item_id }
    });
  }
  async getSpecificStorePricesByCombo(combo_id: any): Promise<any> {
    return await this.storeSpecificPriceRep.find({
      relations: ["store_id", "combo_id"],
      where: { combo_id: combo_id }
    });
  }

  async saveMakeACombo(data: any): Promise<any> {
    return await this.makeAComboRep.save(data);
  }

  async makeAComboList(): Promise<any> {
    // return await this.makeAComboRep.find();
    return await this.makeAComboRep
      .createQueryBuilder("make_a_combo")
      .select(
        "make_a_combo.*,m.menu_id,m.menu_name,g.group_id,g.group_name,mi.menu_item_id,mi.item_name"
      )
      .leftJoin("menus", "m", "m.menu_id = make_a_combo.menu_id")
      .leftJoin("groups", "g", "g.group_id = make_a_combo.group_id")
      .leftJoin(
        "menu_item",
        "mi",
        "mi.menu_item_id = make_a_combo.menu_item_id"
      )
      .getRawMany();
  }

  async makeAComboListActive(): Promise<any> {
    // return await this.makeAComboRep.find();
    return await this.makeAComboRep
      .createQueryBuilder("make_a_combo")
      .select(
        "make_a_combo.*,m.menu_id,m.menu_name,g.group_id,g.group_name,mi.menu_item_id,mi.item_name"
      )
      .leftJoin("menus", "m", "m.menu_id = make_a_combo.menu_id")
      .leftJoin("groups", "g", "g.group_id = make_a_combo.group_id")
      .leftJoin(
        "menu_item",
        "mi",
        "mi.menu_item_id = make_a_combo.menu_item_id"
      )
      .where("make_a_combo.is_active = 1")
      .getRawMany();
  }

  async getMakeACombo(id: any): Promise<any> {
    return await this.makeAComboRep
      .createQueryBuilder("make_a_combo")
      .select("make_a_combo.*,m.*,g.*,mi.*")
      .leftJoin("menus", "m", "m.menu_id = make_a_combo.menu_id")
      .leftJoin("groups", "g", "g.group_id = make_a_combo.group_id")
      .leftJoin(
        "menu_item",
        "mi",
        "mi.menu_item_id = make_a_combo.menu_item_id"
      )
      .where(`make_a_combo.id = ${id}`)
      .getRawOne();
    // return await this.makeAComboRep.find({where:{id:id}});
  }

  async blockUnblockMakeACombo(id: any, value: any): Promise<any> {
    return await this.makeAComboRep
      .createQueryBuilder()
      .update(MakeACombo)
      .set({ is_active: value })
      .where(`id = ${id}`)
      .execute();
  }

  async saveMakeAComboChoices(data: any): Promise<any> {
    return await this.makeAComboChoiceRep.save(data);
  }

  async updateMakeACombo(id, data: any): Promise<any> {
    return await this.makeAComboRep.update(id, data);
  }

  async updateMakeAComboChoices(id, data: any): Promise<any> {
    return await this.makeAComboChoiceRep.update(id, data);
  }

  async getMakeComboChoicesByMakeComboId(make_a_combo_id: any) {
    return await this.makeAComboChoiceRep
      .createQueryBuilder("make_a_combo_choices")
      .select(
        "make_a_combo_choices.*,g.group_name,m.item_name,mv.size,mac.name"
      )
      .leftJoin("groups", "g", "g.group_id = make_a_combo_choices.group_id")
      .leftJoin(
        "menu_item",
        "m",
        "m.menu_item_id = make_a_combo_choices.menu_item_id"
      )
      .leftJoin(
        "menu_item_variants",
        "mv",
        "mv.id = make_a_combo_choices.variant_id"
      )
      .leftJoin(
        "make_a_combo",
        "mac",
        "mac.id = make_a_combo_choices.make_a_combo_id"
      )
      .where(`make_a_combo_choices.make_a_combo_id = ${make_a_combo_id}`)
      .andWhere(`make_a_combo_choices.is_active = 1`)
      .getRawMany();
  }

  async updateMakeComboChoicesInactive(id: any): Promise<any> {
    return await this.customerRep
      .createQueryBuilder()
      .update(MakeAComboChoices)
      .set({ is_active: 0 })
      .where("id = :id", { id: id })
      .execute();
  }

  async updateComboChoices(id: any, obj: any): Promise<any> {
    return await this.customerRep
      .createQueryBuilder()
      .update(MakeAComboChoices)
      .set(obj)
      .where("id = :id", { id: id })
      .execute();
  }

  async GetItemsByGroup(group_id: number): Promise<any> {
    return await this.itemRep
      .createQueryBuilder("menu_item")
      .select(
        "menu_item.*,menu_item.item_name as label,menu_item.menu_item_id as value"
      )
      .where("menu_item.item_group_id= :item_group_id", {
        item_group_id: group_id
      })
      .getRawMany();
  }

  async GetVariantsByItem(item_id: number): Promise<any> {
    return await this.itemvariantRep
      .createQueryBuilder("menu_item_variants")
      .select(
        "menu_item_variants.*,m.*,menu_item_variants.id as value,menu_item_variants.size as label"
      )
      .leftJoin(
        "menu_item",
        "m",
        "m.menu_item_id = menu_item_variants.menu_item_id"
      )
      .where("menu_item_variants.menu_item_id= :menu_item_id", {
        menu_item_id: item_id
      })
      .getRawMany();
  }

  async getOrderModesPriceForMenu(
    item_variant_id: any,
    mode_id: any
  ): Promise<any> {
    return await this.orderModePriceRep
      .createQueryBuilder("order_mode_price")
      .select("order_mode_price.*")
      .where("order_mode_price.item_variant_id= :item_variant_id", {
        item_variant_id: item_variant_id
      })
      .andWhere("order_mode_price.order_mode_id= :order_mode_id", {
        order_mode_id: mode_id
      })
      .getRawOne();
    // return await this.orderModePriceRep.findOne({ relations: ["order_mode_id"], where: { item_variant_id: item_variant_id,order_mode_id:mode_id } })
  }

  async getOrderModesPriceForCombo(combo_id: any, mode_id: any): Promise<any> {
    return await this.orderModePriceRep
      .createQueryBuilder("order_mode_price")
      .select("order_mode_price.*")
      .where("order_mode_price.combo_id= :combo_id", { combo_id: combo_id })
      .andWhere("order_mode_price.order_mode_id= :order_mode_id", {
        order_mode_id: mode_id
      })
      .getRawOne();
    // return await this.orderModePriceRep.findOne({ relations: ["order_mode_id"], where: { item_variant_id: item_variant_id,order_mode_id:mode_id } })
  }

  async getOrderMode(keyword: any): Promise<any> {
    return await this.orderModeRep.findOne({ where: { keyword: keyword } });
  }
  async findMakeAComboOptionById(
    order_id: number,
    order_item_id: any
  ): Promise<any> {
    return await this.orderQuickComboRep.find({
      relations: [
        "make_a_combo_choice_id",
        "make_a_combo_choice_id.menu_item_id",
        "make_a_combo_choice_id.variant_id",
        "make_a_combo_choice_id.group_id"
      ],
      where: {
        order_id: order_id,
        order_item_id: order_item_id
      }
    });
  }
  async blockItemRelatedCombos(combo_ids, itemId) {
    return await this.comboRep
      .createQueryBuilder()
      .update(Combo)
      .set({
        is_active: 0,
        is_publish: 0,
        combo_block_reason: `Block due to itemId ${itemId}`
      })
      .where("combo_id In(:...combo_id)", { combo_id: combo_ids })
      .execute();
  }
  async getRandomNPSVoucherCode() {
    var querystring = `SELECT t1.coupon_id,t1.coupon_code FROM coupon t1 LEFT JOIN coupon_customer_assign t2 ON t2.coupon_id = t1.coupon_id WHERE t2.coupon_id IS NULL AND t1.is_nps=1 LIMIT 1`;
    return await getManager().query(querystring);
  }
  async saveNPSCouponCustomer(data: any) {
    return await this.couponCustomerAssignRep.save(data);
  }
  async editFlashAppProfile(
    id: number,
    {
      phone_number,
      last_name,
      first_name,
      email_address,
      city,
      address
    }: EditProfileDto
  ): Promise<any> {
    return await this.userRep.update(id, {
      phone_number,
      last_name,
      first_name,
      email_address,
      city,
      address
    });
  }

  async isUserBlocked(userId: number): Promise<boolean> {
    const user = await this.userRep.findOne({
      where: { user_id: userId },
      select: ["status"]
    });

    if (!user) {
      console.error("User not found in the database");
      return true;
    }

    return (
      parseInt(user.status.toString()) === USER_BLOCKED_STATUS_CODE.BLOCKED
    );
  }
}
