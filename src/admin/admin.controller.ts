/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import {
  Controller,
  UsePipes,
  Body,
  HttpStatus,
  ValidationPipe,
  HttpException,
  Param,
  Get,
  Post,
  Put,
  Delete,
  UseInterceptors,
  Injectable,
  UploadedFiles,
  UploadedFile,
  Res,
  Req,
  Headers,
  Patch,
  ParseIntPipe
} from "@nestjs/common";
import { AdminService } from "./admin.service";
import { FileInterceptor, FilesInterceptor } from "@nestjs/platform-express";
import { diskStorage } from "multer";
import { Request } from "express";
var voucher_codes = require("voucher-code-generator");
import jwtDecode from "jwt-decode";
import {
  User,
  Customer,
  Store,
  Group,
  MenuItem,
  Combo,
  ModifierOption,
  Promo,
  Discounts,
  ActivityLogs,
  Delivery_Areas,
  SubGroup,
  Banners,
  Coupon
} from "entities";
import {
  editFileName,
  excelFileFilter,
  imageFileFilter
} from "utils/file-upload.utils";
import { Response } from "express";
import { LoggingService } from "utils/loggingService.utils";
import axios from "axios";
var http = require("http");
import * as fs from "fs";
import * as csvParser from "csv-parser";
const qs = require("qs");
const uuid = require("uuid");
import * as moment from "moment";
import { sendNotification } from "../utils/sendNotification";
import { BackupStore } from "entities/backup_stores.entity";
import { TradeZoneService } from "trade-zone/trade-zone.service";
import { isEmpty } from "lodash";
import { couponsPerPage } from "settings";
import { sendDataUploadNotification } from "utils/sendEmail";
import { WorkerService } from "./my-worker.service";
import { USER_BLOCKED_STATUS_CODE } from "lib/enums";
import { bulkCouponUpdate } from "./admin.dto";
import { badRequestValidationPipe } from "pipes";
const exec = require("child_process").exec;
@Injectable()
// ADMIN ONLY ROUTES
@Controller("api")
export class AdminController {
  constructor(
    private adminService: AdminService,
    private tradeZoneService: TradeZoneService,
    private readonly workerService: WorkerService,
    private readonly loggingService: LoggingService
  ) {}
  @Post("admin/sync_menu_with_simplex_sync")
  async syncMenu(@Res() response: Response): Promise<any> {
    // @Req() request: Request,
    // let token: any = request.headers.authorization?.split("Bearer ")[1];
    // let jwtData: any = jwtDecode(token);
    let brand: any = await this.adminService.getBrand();
    console.log("brand");
    let menus: any = await this.adminService.getAllMenu();
    // let stores: any = await this.adminService.storeDataForSync(brand.brand_id)
    console.log("menus", menus.length);
    if (menus && menus.length > 0) {
      let menus_arr: any = [];
      let groups_arr: any = [];
      let items_arr: any = [];
      let items_Variants_arr: any = [];
      let payload: any = {
        brand_unique_code: brand.brand_unique_code
      };
      for (let i = 0; i < menus.length; i++) {
        let menu: any = menus[i];
        // get item by groups
        let groups: any = await this.adminService.getGroupsOfMenu(menu.menu_id);
        if (groups && groups.length) {
          for (let g = 0; g < groups.length; g++) {
            const group = groups[g];
            // get item by groups
            let items: any = await this.adminService.getItemsByGroup(
              group.group_id
            );
            if (items && items.length) {
              for (let i = 0; i < items.length; i++) {
                const item = items[i];
                // console.log('item ID======================',item.menu_item_menu_item_id);
                let itemVariants: any = await this.adminService.getVariantsByItem(
                  item.menu_item_menu_item_id
                );
                // console.log('variantsssssssssssssssssssss',itemVariants);
                items_Variants_arr.push(itemVariants);

                let itemObj: any = {
                  menu_item_id: item.menu_item_menu_item_id,
                  item_name: item.menu_item_item_name,
                  item_description: item.menu_item_item_description,
                  is_publish: item.menu_item_is_publish,
                  is_active: item.menu_item_is_active,
                  hero_item: item.menu_item_hero_item,
                  item_group_id: item.menu_item_item_group_id,
                  item_mode: item.menu_item_item_mode,
                  is_lsm: item.menu_item_is_lsm,
                  priority: item.menu_item_priority,
                  erp_id: item.menu_item_pos_code
                  // Variants: itemVariants
                };
                items_arr.push(itemObj);
              }
            } else {
              console.log("No items Found");
            }

            let groupObj: any = {
              group_id: group.group_id,
              group_name: group.group_name,
              is_publish: group.is_publish,
              is_active: group.is_active,
              priority: group.priority,
              group_image: group.group_image,
              brand_id: group.brand_id,
              menu_id: group.menu_id,
              mode: group.mode,
              is_lsm: group.is_lsm
            };
            groups_arr.push(groupObj);
            // console.log('group ======================',group.group_name);
          }
        } else {
          console.log("No Groups Found");
        }

        let obj: any = {
          menu_id: menu.menu_id,
          menu_name: menu.menu_name,
          is_publish: menu.is_publish,
          store_type_id: menu.store_type_id,
          brand_id: menu.brand_id
        };
        menus_arr.push(obj);
        // console.log('menu ======================',menu.menu_name);
      }
      let modifiers: any = await this.adminService.GetAllModifiers();
      let modGroups: any = await this.adminService.GetAllModifierGroups();
      Object.assign(payload, {
        menus: menus_arr,
        groups: groups_arr,
        items: items_arr,
        items_Variants_arr: items_Variants_arr,
        modifiers: modifiers,
        modGroups: modGroups
      });
      console.log("payload======================", JSON.stringify(payload));

      axios
        .post(`http://localhost:4002/admin/sync_menu_with_cms`, payload)
        .then((resp: any) => {
          console.log("resp", resp);
          if (resp.data.success) {
            response.status(200).send({
              success: true,
              successResponse: resp.data.successResponse
            });
          } else {
            response
              .status(403)
              .send({ success: false, message: resp.data.message });
          }
        })
        .catch((err: any) => {
          console.log(err);
          if (err.data && err.data.message) {
            response
              .status(403)
              .send({ success: false, message: err.data.message });
          } else {
            response
              .status(403)
              .send({ success: false, message: "Some Error Occured" });
          }
        });
    } else {
      response
        .status(403)
        .send({ success: false, successResponse: "Menu Not Found" });
    }
  }

  @Get("admin/getFPJson/:order_id")
  async getFpJson(@Param("order_id") order_id): Promise<any> {
    try {
      const fpJson = await this.adminService.getFPJson(order_id);
      return { success: true, successResponse: fpJson.order_json };
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: "Error While Getting Food Panda Json Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Home
  @Post("admin/home_counter")
  async homeCounter(
    @Req() request: Request,
    @Body() interval: any
  ): Promise<any> {
    //console.log("respoense: ", response);
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data: any;
    let totalCustomers: any;
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    if (interval.days == 0) {
      data = await this.adminService.orderDataForhomeCounter(
        interval.days,
        store_ids
      );
      totalCustomers = await this.adminService.totalCustomersCounter(
        interval.days
      );
    } else {
      data = await this.adminService.orderDataForhomeCounterWithDatetime(
        interval,
        store_ids
      );
      totalCustomers = await this.adminService.totalCustomersCounterWithDateTime(
        interval
      );
    }
    if (data.length > 0) {
      let total = 0;
      let totalSales = 0;
      let totalOrders = data.length;
      let totalDeliveryCompleted = 0;
      let deliverySales = 0;
      let pickupSales = 0;
      let qrSales = 0;
      let processingOrders = 0;
      let completedOrders = 0;
      let cancelOrders = 0;

      for (let i = 0; i < data.length; i++) {
        if (data[i].order_status_code == 5 || data[i].order_status_code == 6) {
          completedOrders++;
          totalSales = totalSales + data[i].order_grossprice;
        }
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].delivery_status == "Delivery"
        ) {
          deliverySales = deliverySales + data[i].order_grossprice;
        }
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].delivery_status == "Pickup"
        ) {
          pickupSales = pickupSales + data[i].order_grossprice;
        }
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].delivery_status == "Qr-Pickup"
        ) {
          qrSales = qrSales + data[i].order_grossprice;
        }
        if (data[i].order_status_code == 2) {
          processingOrders++;
        }
        if (
          data[i].order_status_code == 7 ||
          data[i].order_status_code == 8 ||
          data[i].order_status_code == 9
        ) {
          cancelOrders++;
        }
        if (data[i].delivery_time_json && data[i].order_status_code == 6) {
          totalDeliveryCompleted++;
          let delivery_time = JSON.parse(data[i].delivery_time_json);
          total +=
            delivery_time.order_placed +
            delivery_time.prep +
            delivery_time.bake +
            delivery_time.quality_check;
        }
      }
      let avg = total / totalDeliveryCompleted;
      let obj = [
        {
          totalUsers: totalCustomers.total,
          totalSales: totalSales,
          totalOrders: totalOrders,
          deliverySales: deliverySales,
          pickupSales: pickupSales,
          qrSales: qrSales,
          processingOrders: processingOrders,
          completeOrders: completedOrders,
          cancelOrders: cancelOrders,
          avgTicket: completedOrders == 0 ? 0 : totalSales / completedOrders,
          avg_delivery_time: Math.round(avg)
        }
      ];
      return { success: true, successResponse: obj };
    } else {
      let obj = [
        {
          totalUsers: 0,
          totalSales: 0,
          totalOrders: 0,
          deliverySales: 0,
          pickupSales: 0,
          qrSales: 0,
          processingOrders: 0,
          completeOrders: 0,
          cancelOrders: 0,
          avgTicket: 0,
          avg_delivery_time: 0
        }
      ];
      return { success: true, successResponse: obj };
    }
  }
  @Post("admin/monthly_orders")
  async monthlyOrders(@Body() interval: any): Promise<any> {
    let data: any;
    if (interval.days == 0) {
      data = await this.adminService.monthlyOrders(interval.days);
    } else {
      data = await this.adminService.monthlyOrderswithDateTime(interval);
    }
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Data Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  // Bulk upload for customers
  @Post("admin/customer-upload")
  async CustomerUpload(@Body() data: any[]) {
    try {
      // console.log("Data ", data);
      // let dataArr: any = [];
      let duplicateArr: any = [];
      // for (let index = 0; index < data.length; index++) {
      //     // let response: any = await this.adminService.findDuplicateWithEmail(data[index])
      //     let responsewithphone: any = await this.adminService.findDuplicateWithPhone(data[index])
      //     // if (response) {
      //     //     duplicateArr.push(response)
      //     // } else
      //     if (responsewithphone) {
      //         duplicateArr.push(responsewithphone)
      //     } else {
      //         dataArr.push(data[index])
      //     }
      // }
      if (data.length > 0) {
        var i,
          j,
          batch,
          chunk = 1000;
        for (i = 0, j = data.length; i < j; i += chunk) {
          batch = data.slice(i, i + chunk);
          await this.adminService
            .customerUpload(batch)
            //console.log("chunks: ", batch);
            .then((response: any) => {
              console.log(response.length + " data were sent successfully");
            })
            .catch(error => {
              console.log("customerUpload Error sending message:", error);
            });
        }

        if (duplicateArr.length > 0) {
          console.log("IF DUP");
          return {
            statusCode: HttpStatus.OK,
            message: "Data Saved With Duplications!",
            data: duplicateArr
          };
        } else {
          console.log("ELSE DUP");
          return {
            statusCode: HttpStatus.OK,
            message: "Data Saved Successfully.",
            dataArr: data
          };
        }
      } else {
        if (duplicateArr.length > 0) {
          return {
            statusCode: HttpStatus.OK,
            message: "Please remove the Duplications from your file!",
            data: duplicateArr
          };
        }
      }
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: "Data not saved"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Post("admin/nps-data")
  async npsData(
    @Body() body: { fromDate: string; toDate: string }
  ): Promise<any> {
    const { fromDate, toDate } = body;
    const dateFormat = /^\d{2}-\d{2}-\d{4}$/;

    if (
      !fromDate ||
      !toDate ||
      !dateFormat.test(fromDate) ||
      !dateFormat.test(toDate)
    ) {
      throw new HttpException(
        {
          success: false,
          message: "Starting and ending date are required"
        },
        HttpStatus.FORBIDDEN
      );
    }

    const [startDay, startMonth, startYear] = fromDate.split("-");
    const [endDay, endMonth, endYear] = toDate.split("-");

    const start = new Date(
      Date.UTC(Number(startYear), Number(startMonth) - 1, Number(startDay))
    );
    const end = new Date(
      Date.UTC(Number(endYear), Number(endMonth) - 1, Number(endDay))
    );

    if (start > end) {
      throw new HttpException(
        {
          success: false,
          message: "Starting date cannot be greater than ending date"
        },
        HttpStatus.FORBIDDEN
      );
    }

    const oneMonthLater = new Date(start);
    oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);

    if (end > oneMonthLater) {
      throw new HttpException(
        {
          success: false,
          message: "Difference between dates must be less than a month"
        },
        HttpStatus.FORBIDDEN
      );
    }

    const data = await this.adminService.npsData(
      start.toISOString().split("T")[0],
      end.toISOString().split("T")[0]
    );
    if (data.length == 0) {
      throw new HttpException(
        {
          success: false,
          message: "No data found"
        },
        HttpStatus.NOT_FOUND
      );
    }
    return {
      success: true,
      successResponse: data
    };
  }

  @Post("admin/customer-data")
  async customerData(
    @Body() body: { fromDate: string; toDate: string }
  ): Promise<any> {
    const { fromDate, toDate } = body;
    const dateFormat = /^\d{2}-\d{2}-\d{4}$/;

    if (
      !fromDate ||
      !toDate ||
      !dateFormat.test(fromDate) ||
      !dateFormat.test(toDate)
    ) {
      throw new HttpException(
        {
          success: false,
          message: "Starting and ending date are required"
        },
        HttpStatus.FORBIDDEN
      );
    }

    const [startDay, startMonth, startYear] = fromDate.split("-");
    const [endDay, endMonth, endYear] = toDate.split("-");

    const start = new Date(
      Date.UTC(Number(startYear), Number(startMonth) - 1, Number(startDay))
    );
    const end = new Date(
      Date.UTC(Number(endYear), Number(endMonth) - 1, Number(endDay))
    );

    if (start > end) {
      throw new HttpException(
        {
          success: false,
          message: "Starting date cannot be greater than ending date"
        },
        HttpStatus.FORBIDDEN
      );
    }

    const oneMonthLater = new Date(start);
    oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);

    if (end > oneMonthLater) {
      throw new HttpException(
        {
          success: false,
          message: "Difference between dates must be less than a month"
        },
        HttpStatus.FORBIDDEN
      );
    }

    const data = await this.adminService.customerData(
      start.toISOString().split("T")[0],
      end.toISOString().split("T")[0]
    );
    if (data.length == 0) {
      throw new HttpException(
        {
          success: false,
          message: "No data found"
        },
        HttpStatus.NOT_FOUND
      );
    }
    return {
      success: true,
      successResponse: data
    };
  }

  @Post("admin/address-upload")
  async AddressUpload(@Body() data: any[]) {
    try {
      // console.log("Data ", data);
      console.log(data.length);
      var i,
        j,
        batch,
        chunk = 10000;
      for (i = 0, j = data.length; i < j; i += chunk) {
        batch = data.slice(i, i + chunk);
        let customers: any = [];
        console.log("batch", batch.length);
        for (let k = 0; k < batch.length; k++) {
          if (batch[k].phone_number > 0) {
            let obj = await this.adminService.customerByPhone(
              batch[k].phone_number
            );
            // console.log("obj", obj)
            let addressData = {};
            if (obj) {
              addressData = {
                place: "Other",
                full_address: batch[k].address,
                customer_id: obj.customer_id,
                address_type: "callcenter"
              };
              customers.push(addressData);
            }
          }
        }
        console.log("customers", customers.length);
        await this.adminService
          .addAddress(customers)
          //console.log("chunks: ", batch);
          .then((response: any) => {
            console.log(response.length + " data were sent successfully");
          })
          .catch(error => {
            console.log("Error sending message:", error);
          });
      }
      return { success: true, message: "Addresses saved" };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: "Data not saved"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/monthly_sales")
  async monthlySales(): Promise<any> {
    let data = await this.adminService.monthlySales();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Data Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/recent_payments")
  async recentPayments(
    @Req() request: Request,
    @Body() interval: any
  ): Promise<any> {
    let data: any;
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    if (interval.days == 0) {
      data = await this.adminService.recentPayments(interval.days, store_ids);
    } else {
      data = await this.adminService.recentPaymentsWithDateTime(
        interval,
        store_ids
      );
    }
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Payments Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/recent_orders")
  async recentOrders(
    @Req() request: Request,
    @Body() interval: any
  ): Promise<any> {
    let data: any;
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    if (interval.days == 0) {
      data = await this.adminService.recentOrders(interval.days, store_ids);
    } else {
      data = await this.adminService.recentOrdersWithDateTime(
        interval,
        store_ids
      );
    }
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Orders Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/last_seven_days_piechart_sos")
  async lastSevenDaysPieSOS(@Req() request: Request): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    let data = await this.adminService.lastSevenDaysOfDataPieChartSOS(
      store_ids
    );
    let finalData: any = {};
    let totalOrderPlace = 0;
    let totalPrep = 0;
    let totalBake = 0;
    let totalQualityCheck = 0;
    for (let i = 0; i < data.length; i++) {
      if (data[i].delivery_time_json) {
        let delivery_time = JSON.parse(data[i].delivery_time_json);
        if (delivery_time) {
          totalOrderPlace += delivery_time.order_placed;
          totalPrep += delivery_time.prep;
          totalBake += delivery_time.bake;
          totalQualityCheck += delivery_time.quality_check;
        }
      }
    }
    finalData.avgOrderPlacedTime = Math.round(totalOrderPlace / data.length);
    finalData.avgPrepTime = Math.round(totalPrep / data.length);
    finalData.avgBakeTime = Math.round(totalBake / data.length);
    finalData.avgQualityCheckTime = Math.round(totalQualityCheck / data.length);
    if (finalData) {
      return { success: true, successResponse: finalData };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Orders Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Post("admin/last_seven_days_dougnutchart_channel")
  async lastSevenDaysDougnutChannel(@Req() request: Request): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    let data = await this.adminService.lastSevenDaysOrderDataForDougnutChartChannel(
      store_ids
    );
    if (data.length > 0) {
      let web = 0;
      let mobile = 0;
      let ios = 0;
      let Android = 0;
      let callCenter = 0;

      for (let i = 0; i < data.length; i++) {
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].order_channel == "web"
        ) {
          web = web + data[i].order_grossprice;
        }
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].order_channel == "mobile"
        ) {
          mobile = mobile + data[i].order_grossprice;
        }
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].order_channel == "mobile" &&
          data[i].device_info.includes("iOS")
        ) {
          ios = ios + data[i].order_grossprice;
        }
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].order_channel == "mobile" &&
          data[i].device_info.includes("Android")
        ) {
          Android = Android + data[i].order_grossprice;
        }
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].order_channel == "call center"
        ) {
          callCenter = callCenter + data[i].order_grossprice;
        }
      }
      let obj = [
        {
          web: web,
          mobile: mobile,
          ios: ios,
          Android: Android,
          callCenter: callCenter
        }
      ];
      return { success: true, successResponse: obj };
    } else {
      let obj = [
        {
          web: null,
          mobile: null,
          ios: null,
          Android: null,
          callCenter: null
        }
      ];
      return { success: true, successResponse: obj };
    }
  }
  @Post("admin/last_seven_days_linechart")
  async lastSevenDays(@Req() request: Request): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    let data = await this.adminService.lastSevenDaysOfDataLineChart(store_ids);
    var lastSevenDays: any = [];
    for (var i = 0; i < 7; i++) {
      var d = new Date();
      d.setDate(d.getDate() - i);
      lastSevenDays.push({
        date_created: moment(d).format("YYYY-MM-DD"),
        completeOrders: 0,
        totalSales: 0
      });
    }
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < lastSevenDays.length; j++) {
        if (
          lastSevenDays[j].date_created ===
          moment(data[i].date_created).format("YYYY-MM-DD")
        ) {
          lastSevenDays[j].completeOrders = data[i].completeOrders;
          lastSevenDays[j].totalSales = data[i].totalSales;
        }
      }
    }
    if (lastSevenDays && lastSevenDays.length > 0) {
      return { success: true, successResponse: lastSevenDays };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Orders Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/leader_board")
  async leaderBoard(
    @Req() request: Request,
    @Body() interval: any
  ): Promise<any> {
    let data: any;
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    if (interval.days == 0) {
      data = await this.adminService.leaderBoard(interval.days, store_ids);
    } else {
      data = await this.adminService.leaderBoardWithDateTime(
        interval,
        store_ids
      );
    }
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Orders Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  // Admin ONLY ROUTES
  @Put("admin/change_password")
  async adminChangePassword(@Body() credentials: User) {
    let success: any = await this.adminService.changePassword(
      credentials.user_id,
      credentials.password
    );
    if (success.affected > 0) {
      return {
        success: true,
        successResponse: "Password Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Password Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/profile/:id")
  async GetProfile(@Param("id") id): Promise<any> {
    let userId = Number(id);
    let data = await this.adminService.getProfileById(userId);
    return { success: true, successResponse: data };
  }
  @Put("admin/edit_profile/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editProfile(@Param("id") id, @Body() user: User): Promise<any> {
    let userId = Number(id);
    let success = await this.adminService.editProfile(userId, user);
    if (success.affected > 0) {
      return { success: true, successResponse: "Profile Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Admin Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Put("admin/customer_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async customerBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() customer: any
  ): Promise<any> {
    let customerId = Number(id);
    let success = await this.adminService.customerBlockUnblock(
      customerId,
      customer.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: customer.user_info.role,
        user_name: customer.user_info.first_name + customer.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Customers",
        activity_name: customer.phone_number,
        status: customer.is_active == 1 ? "UnBlock Customer" : "Block Customer",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Customer Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Customer Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/reactive_account")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async reActiveAccount(
    @Req() request: Request,
    @Body() customer: any
  ): Promise<any> {
    let success: any = await this.adminService.reactiveAccount(customer);
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: customer.user_info.role,
        user_name: customer.user_info.first_name + customer.user_info.last_name,
        reason: "Customer Activation",
        activity_type: "Customers",
        activity_name: customer.phone_number,
        status: "Activate Customer",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Account Reactived Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Account not reactived"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_customer/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editCustomer(
    @Param("id") id,
    @Body() customer: Customer
  ): Promise<any> {
    let customer_id = Number(id);
    throw new HttpException(
      {
        statusCode: HttpStatus.FORBIDDEN,
        success: false,
        message: "You are not authorized for this request."
      },
      HttpStatus.FORBIDDEN
    );
    let success = await this.adminService.editCustomer(customer_id, customer);
    if (success.affected > 0) {
      return {
        success: true,
        successResponse: "Customer Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Customer Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/customer/:id")
  async GetCustomer(@Param("id") id): Promise<any> {
    throw new HttpException(
      {
        statusCode: HttpStatus.FORBIDDEN,
        success: false,
        message: "You are not authorized for this request."
      },
      HttpStatus.FORBIDDEN
    );
    let customer_id = Number(id);
    let data = await this.adminService.getCustomerById(customer_id);
    return { success: true, successResponse: data };
  }
  @Post("admin/search_customer/:phone")
  async GetCustomerByPhone(
    @Req() request: Request,
    @Param("phone") phone,
    @Body() customer: any
  ): Promise<any> {
    throw new HttpException(
      {
        statusCode: HttpStatus.FORBIDDEN,
        success: false,
        message: "You are not authorized for this request."
      },
      HttpStatus.FORBIDDEN
    );
    let data = await this.adminService.getCustomerByPhone(phone);
    if (data.length > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      if (!customer.block) {
        let logData: any = {
          role: customer.user_info.role,
          user_name:
            customer.user_info.first_name + customer.user_info.last_name,
          reason: "Search",
          activity_type: "Customers",
          activity_name: phone,
          status: "Search Customer",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Customer Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/wishlist/:id")
  async wishlistData(@Param("id") customer_id): Promise<any> {
    let data = await this.adminService.getWishlistOfCustomer(customer_id);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Favorites Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/order_history/:id")
  async getordersHistory(@Param("id") id): Promise<any> {
    let data = await this.adminService.getordersHistory(id);
    if (data.length > 0) {
      data.forEach(function(obj) {
        var OrderDate = obj.date_created.toLocaleString().split(",");
        obj.date_created = OrderDate[0];
      });
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Orders Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  // Customer Feedback ONLY ROUTES
  @Get("admin/customer_feedbacks")
  async feedbackData(): Promise<any> {
    let data = await this.adminService.getCustomerFeedback();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Customer FeedBack Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Delete("admin/del_feedback/:id")
  async deleteFeedback(@Param("id") id): Promise<any> {
    let customer_feedback_id = Number(id);
    let success = await this.adminService.delFeedback(customer_feedback_id);
    if (success) {
      return {
        success: true,
        successResponse: "Customer Feedback Deleted Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Customer Feedback Not Deleted"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  // Payment ONLY ROUTES

  @Get("admin/payments/:page/:limit")
  async paymentData(
    @Req() request: Request,
    @Param("page") page: number,
    @Param("limit") limit: number
  ): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    let data = await this.adminService.getPayments(store_ids, page, limit);
    let count = await this.adminService.getPaymentsCount(store_ids);
    if (data.length > 0) {
      return { success: true, successResponse: data, count: count.total };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Payments Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/payment/:order_id")
  async paymentById(
    @Param("order_id") order_id: number
  ): Promise<object | string> {
    let data = await this.adminService.getCustomerPayment(order_id);
    if (data) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Payment Not Found"
        },
        HttpStatus.NOT_FOUND
      );
    }
  }
  // Users ONLY ROUTES

  @Get("admin/users")
  async userData(): Promise<any> {
    let data = await this.adminService.userData();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Users Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/customer-feedbacks/:page/:limit")
  async customerFeedbacks(
    @Param("page") page: number,
    @Param("limit") limit: number
  ): Promise<any> {
    let data = await this.adminService.getCustomerFeedbacks(page, limit);
    let count = await this.adminService.getFeedbacksCount();
    if (data.length > 0) {
      return { success: true, message: data, count: count.total };
    } else {
      throw new HttpException(
        {
          success: false,
          error: "Customer Feedbacks Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Post("admin/add_user")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addUser(@Req() request: Request, @Body() user: any): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = user.user_info;
    delete user.user_info;
    let success = await this.adminService.addUser(user);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "User Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "New User",
        activity_type: "Users",
        activity_name: user.email_address,
        status: "Add User",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "User Inserted Successfully" };
    }
  }
  @Put("admin/edit_user/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editUser(
    @Req() request: Request,
    @Param("id") id,
    @Body() user: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = user.user_info;
    delete user.user_info;
    let user_id = Number(id);
    let success = await this.adminService.editUser(user_id, user);
    if (success.affected > 0) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Users",
        activity_name: user.email_address,
        status: "Update User",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "User Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "User Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/user/:id")
  async GetUser(@Param("id") id): Promise<any> {
    let user_id = Number(id);
    let data = await this.adminService.getUserById(user_id);
    return { success: true, successResponse: data };
  }
  @Post("admin/del_user/:id")
  async deleteUser(
    @Req() request: Request,
    @Param("id") id,
    @Body() user: any
  ): Promise<any> {
    let user_id = Number(id);
    let success = await this.adminService.delUser(user_id);
    if (success) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: user.user_info.role,
        user_name: user.user_info.first_name + " " + user.user_info.last_name,
        reason: "Delete",
        activity_type: "Users",
        activity_name: user.email_address,
        status: "Delete User",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "User Deleted Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "User Not Deleted"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/user_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async userBlockUnblock(
    @Headers() header,
    @Param("id", ParseIntPipe) id: number,
    @Body() user: any
  ): Promise<any> {
    const userId = Number(id);
    const success: any = await this.adminService.userBlockUnblock(
      userId,
      user.status
    );
    if (success.affected > 0) {
      const token: any = header["authorization"]?.split("Bearer ")[1];
      const jwtData: any = jwtDecode(token);
      if (user.status == USER_BLOCKED_STATUS_CODE.ACTIVE)
        await this.adminService.resetUserBadAttempts(userId);
      const logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Block/UnBlock",
        activity_type: "Users",
        activity_name: user.email_address,
        status: user.status == 1 ? "UnBlock User" : "Block User"
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "User Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "User Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Stores Only Routes
  @Get("admin/stores")
  async storeData(@Req() request: Request): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    let data = await this.adminService.storeData(store_ids);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Stores Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/store/:id")
  async GetStore(@Param("id") id): Promise<any> {
    let store_id = Number(id);
    let data = await this.adminService.getStoreById(store_id);
    return { success: true, successResponse: data };
  }
  @Post("admin/add_store")
  async addStore(@Req() request: Request, @Body() store: any): Promise<any> {
    let menuItemsData = await this.adminService.itemsData();
    let comboData = await this.adminService.combosData();
    let groupsData = await this.adminService.groupData();
    let subgroupsData = await this.adminService.subGroupData();
    let modData = await this.adminService.modifierData();
    let variantsData = await this.adminService.getAllVariants();
    let menuStore: any[] = [];
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    //for logs
    let storeData: any = store;
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    store = storeData;
    try {
      let data: any = {
        store_name: store.store_name,
        store_email: store.store_email,
        pulse_ip: store.pulse_ip,
        escalation_email: store.escalation_email,
        contact1: store.contact1,
        contact2: store.contact2,
        contact3: store.contact3,
        city: store.city,
        ntn_number: store.ntn_number,
        branch_code: store.branch_code,
        address: store.address,
        brand_id: store.brand_id,
        is_active: store.is_active,
        store_open_time: store.store_open_time,
        store_close_time: store.store_close_time,
        expected_delivery_time: store.expected_delivery_time,
        store_type_id: store.store_type_id,
        sales_channel_id: store.sales_channel_id,
        trade_zone_id: store.trade_zone_id,
        business_type_id: store.business_type_id,
        franchise_name:
          store.business_type_id == 1 ? store.franchise_name : null,
        isDays: store.timeByDays,
        daysTiming: store.daysTiming,
        cloudKitchen: store.cloudKitchen,
        is_specialDays: store.specialDaysTiming,
        special_daysTiming: store.special_daysTiming,
        pos_no: store.pos_no ? store.pos_no : 0,
        city_id: store.city_id,
        lat: store.lat,
        lng: store.lng,
        is_backup_store: store.is_backup_store,
        fpRestaurantId: store.fpRestaurantId,
        state_id: store.state_id
      };
      if (store.backup_zone_id) {
        data.backup_zone_id = store.backup_zone_id;
      }
      if (store.country_id) {
        data.country_id = store.country_id;
      }
      if (store.fp_branch_code) {
        let aggregatorBRanchCodeExist: any = await this.adminService.getStoreAggBranchCode(
          store.fp_branch_code
        );
        if (aggregatorBRanchCodeExist) {
          throw new HttpException(
            {
              success: false,
              message: "Aggregator Branch Code already exist"
            },
            HttpStatus.FORBIDDEN
          );
        }
        data.fp_branch_code = store.fp_branch_code;
      } else {
        data.fp_branch_code = null;
      }
      let response = await this.adminService.addStore(data);
      let code: any[] = voucher_codes.generate({
        length: 20,
        count: 1
      });

      let storeToken = {
        store_id: data.store_id,
        branch_code: store.branch_code,
        token: code[0]
      };
      await this.adminService.addStoreToken(storeToken);
      if (!response) {
        throw new HttpException(
          {
            success: false,
            message: "Store Not Inserted"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        const backupObj = JSON.parse(store.backupStore);

        if (backupObj.length > 0) {
          console.log("backupObj.length: ", backupObj.length);

          for (let i = 0; i < backupObj.length; i++) {
            const element = backupObj[i];
            if (element.backUpStoreId !== "") {
              const backupStore: any = new BackupStore();
              backupStore.storeId = data.store_id;
              backupStore.backupStoreId = element.backUpStoreId;
              // backupStore.priority = element.priority;
              backupStore.isActive = data.is_active;
              // backupStore.expected_delivery_time = Number(store.expected_delivery_time) + 15;
              await this.adminService.addBackupStore(backupStore);
            }
          }
        }
        const tradeArea = JSON.parse(store.tradeAreas);

        if (tradeArea.length > 0) {
          for (let i = 0; i < tradeArea.length; i++) {
            const element = tradeArea[i];

            if (element.tradeArea !== "" && element.tradeZone !== "") {
              const Delivery_Area: any = new Delivery_Areas();
              Delivery_Area.store_id = data.store_id;
              Delivery_Area.delivery_zone_id = element.tradeZone;
              Delivery_Area.area_name = element.tradeArea;
              Delivery_Area.expected_delivery_time =
                element.expected_delivery_time;
              if (element.backup_store_id !== "") {
                Delivery_Area.backup_store_id = element.backup_store_id;
              }
              Delivery_Area.is_active = 1;
              await this.tradeZoneService.saveArea(Delivery_Area);
            }
          }
        }
        let obj: any;
        for (let i = 0; i < menuItemsData.length; i++) {
          obj = {
            store_id: data.store_id,
            menu_item_id: menuItemsData[i].menu_item_id,
            is_active: 1
          };
          menuStore.push(obj);
        }
        for (let i = 0; i < comboData.length; i++) {
          obj = {
            store_id: data.store_id,
            combo_id: comboData[i].combo_id,
            is_active: 1
          };
          menuStore.push(obj);
        }
        for (let i = 0; i < groupsData.length; i++) {
          obj = {
            store_id: data.store_id,
            group_id: groupsData[i].group_id,
            is_active: 1
          };
          menuStore.push(obj);
        }
        for (let i = 0; i < subgroupsData.length; i++) {
          obj = {
            store_id: data.store_id,
            subgroup_id: subgroupsData[i].id,
            is_active: 1
          };
          menuStore.push(obj);
        }
        for (let i = 0; i < modData.length; i++) {
          obj = {
            store_id: data.store_id,
            modifier_id: modData[i].modifier_id,
            is_active: 1
          };
          menuStore.push(obj);
        }
        for (let i = 0; i < variantsData.length; i++) {
          obj = {
            store_id: data.store_id,
            item_variant_id: variantsData[i].id,
            is_active: 1
          };
          menuStore.push(obj);
        }
        await this.adminService.addStoresForMenu(menuStore);
        //For Default Group
        let userDefaultGroup = {
          name: response.store_name,
          description:
            "Default group is created automatically when store is created",
          default_group: 1,
          is_active: 1
        };
        let defaultGroup = await this.adminService.addDefaultGroup(
          userDefaultGroup
        );
        let findAllAccessGroups = await this.adminService.allAccessGroups();
        if (defaultGroup) {
          // let user = {
          //     first_name: response.store_name,
          //     user_name: response.store_name,
          //     email_address: response.store_email,
          //     city: response.city,
          //     address: response.address,
          //     password: "Welcome@Dominos",
          //     country_id: 1,
          //     role_id: 6,
          //     user_group_id: defaultGroup.id,
          //     status: 1
          // }
          let groupStore = {
            user_group_id: defaultGroup.id,
            store_id: response.store_id,
            is_active: 1
          };
          await this.adminService.addGroupStore(groupStore);
          // await this.adminService.addUser(user);
          let allAccessGroupStores: any = [];
          findAllAccessGroups.forEach(element => {
            let obj = {
              user_group_id: element.id,
              store_id: response.store_id,
              is_active: 1
            };
            allAccessGroupStores.push(obj);
          });
          await this.adminService.addGroupStore(allAccessGroupStores);
        }
        let logData: any = {
          role: jwtData.role,
          user_name: jwtData.first_name + " " + jwtData.last_name,
          reason: "New Store",
          activity_type: "Store",
          activity_name: store.store_name,
          status: "Add Store",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return {
          success: true,
          successResponse: "Store Inserted Successfully"
        };
      }
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_store/:id")
  async editStore(
    @Req() request: Request,
    @Param("id") id,
    @Body() store: any
  ): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    //for logs
    let storeData: any = store;
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    store = storeData;
    try {
      let store_id = Number(id);
      let data: any = {
        store_name: store.store_name,
        store_email: store.store_email,
        pulse_ip: store.pulse_ip,
        escalation_email: store.escalation_email,
        contact1: store.contact1,
        contact2: store.contact2,
        contact3: store.contact3,
        city: store.city,
        ntn_number: store.ntn_number,
        expected_delivery_time: store.expected_delivery_time,
        address: store.address,
        branch_code: store.branch_code,
        store_open_time: store.store_open_time,
        store_close_time: store.store_close_time,
        sales_channel_id: store.sales_channel_id,
        business_type_id: store.business_type_id,
        trade_zone_id: store.trade_zone_id,
        franchise_name:
          store.business_type_id == 1 ? store.franchise_name : null,
        isDays: store.timeByDays,
        daysTiming: store.daysTiming,
        is_specialDays: store.specialDaysTiming,
        special_daysTiming: store.special_daysTiming,
        pos_no: store.pos_no ? store.pos_no : 0,
        city_id: store.city_id,
        lat: store.lat,
        lng: store.lng,
        is_backup_store: store.is_backup_store,
        fpRestaurantId: store.fpRestaurantId
      };
      if (store.backup_zone_id) {
        data.backup_zone_id = store.backup_zone_id;
      } else {
        data.backup_zone_id = null;
      }
      if (store.country_id) {
        data.country_id = store.country_id;
      } else {
        data.country_id = null;
      }
      if (store.state_id) {
        data.state_id = store.state_id;
      } else {
        data.state_id = null;
      }
      if (store.fp_branch_code) {
        let aggregatorBRanchCodeExist: any = await this.adminService.getStoreAggBranchCode(
          store.fp_branch_code
        );
        console.log(aggregatorBRanchCodeExist);
        if (
          aggregatorBRanchCodeExist &&
          aggregatorBRanchCodeExist.store_id != store_id
        ) {
          throw new HttpException(
            {
              success: false,
              message: "Aggregator Branch Code already exist"
            },
            HttpStatus.FORBIDDEN
          );
        }
        data.fp_branch_code = store.fp_branch_code;
      } else {
        data.fp_branch_code = null;
      }
      let response = await this.adminService.editStore(store_id, data);
      if (response.affected > 0) {
        let data = await this.adminService.getStoreById(store_id);

        const backupObj = JSON.parse(store.backupStore);
        let tradeAreaObj: any = [];
        tradeAreaObj = store.tradeAreas && JSON.parse(store.tradeAreas);

        if (backupObj.length > 0) {
          await this.adminService.removeBackupStore(store_id);
          for (let i = 0; i < backupObj.length; i++) {
            const element = backupObj[i];
            if (element.backUpStoreId !== "") {
              const backupStore: any = new BackupStore();
              backupStore.storeId = store_id;
              backupStore.backupStoreId = element.backUpStoreId;
              // backupStore.priority = element.priority;
              backupStore.isActive = data.is_active;
              // backupStore.expected_delivery_time = 15 + parseInt(store.expected_delivery_time);
              await this.adminService.addBackupStore(backupStore);
            }
          }
        }
        if (tradeAreaObj.length > 0) {
          let tradeAreas: any = [];
          let dbTradeAreas: any = await this.tradeZoneService.getTradeAreasByStoreId(
            data.store_id
          );
          for (let i = 0; i < tradeAreaObj.length; i++) {
            const element = tradeAreaObj[i];
            let Delivery_Area: any;
            if (element.tradeArea !== "" && element.tradeZone !== "") {
              Delivery_Area = new Delivery_Areas();
              Delivery_Area.store_id = data.store_id;
              Delivery_Area.delivery_zone_id = element.tradeZone;
              Delivery_Area.area_name = element.tradeArea;
              Delivery_Area.expected_delivery_time =
                element.expected_delivery_time;
              if (element.backup_store_id !== "") {
                Delivery_Area.backup_store_id = element.backup_store_id;
              } else {
                Delivery_Area.backup_store_id = null;
              }
              Delivery_Area.is_active = 1;
            }
            if (element.tradeAreaId == "") {
              tradeAreas.push(Delivery_Area);
            }
            //To update the existing variants
            dbTradeAreas.forEach(async obj => {
              if (obj.id === element.tradeAreaId) {
                await this.tradeZoneService.editTradeArea(
                  element.tradeAreaId,
                  Delivery_Area
                );
              }
            });
          }
          //To delete the missing variants
          dbTradeAreas.forEach(async element => {
            let data = tradeAreaObj.find(obj => {
              if (element.id == obj.tradeAreaId) {
                return obj;
              }
            });
            if (!data) {
              // console.log("missing", data, element.id)
              await this.tradeZoneService.tradeAreaBlockUnblock(element.id, 0);
            }
          });
          //To insert the new variants
          try {
            if (tradeAreas.length > 0) {
              for (let i = 0; i < tradeAreas.length; i++) {
                await this.tradeZoneService.saveArea(tradeAreas[i]);
              }
            }
          } catch (e) {
            console.log("Edit Store Area Exception", e);
          }
          // await this.tradeZoneService.removeTradeAreas(store_id);
          // for (let i = 0; i < tradeAreaObj.length; i++) {
          //     const element = tradeAreaObj[i];
          //     if (element.tradeArea !== "" && element.tradeZone !== "") {
          //         const Delivery_Area: any = new Delivery_Areas();
          //         Delivery_Area.store_id = data.store_id;
          //         Delivery_Area.delivery_zone_id = element.tradeZone;
          //         Delivery_Area.area_name = element.tradeArea;
          //         Delivery_Area.is_active = 1;
          //         await this.tradeZoneService.saveArea(Delivery_Area);
          //     }
          // }
        }
        let logData: any = {
          role: jwtData.role,
          user_name: jwtData.first_name + " " + jwtData.last_name,
          reason: "Edit Info",
          activity_type: "Store",
          activity_name: store.store_name,
          status: "Update Store",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return { success: true, successResponse: "Store Updated Successfully" };
      } else {
        throw new HttpException(
          {
            success: false,
            message: "Store Not Updated"
          },
          HttpStatus.FORBIDDEN
        );
      }
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/upload_store_image/:id")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async uploadStoreImage(
    @Req() request: Request,
    @UploadedFile() file,
    @Param("id") store_id
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data: any = {
      store_id: store_id
    };
    let storeData: any = await this.adminService.getStoreById(store_id);
    if (file) {
      data["store_image"] = file.filename;
      if (storeData.store_image) {
        if (
          storeData.store_image !== "" &&
          fs.existsSync(`Uploads/images/${storeData.store_image}`)
        ) {
          fs.unlinkSync(`Uploads/images/${storeData.store_image}`);
        }
      }
      let success = await this.adminService.uploadImage(data);
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: "Group Not Inserted"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        let logData: any = {
          role: jwtData.role,
          user_name: jwtData.first_name + " " + jwtData.last_name,
          reason: "Upload Store Image",
          activity_type: "Stores",
          activity_name: storeData.store_name,
          status: "Upload Image",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return {
          success: true,
          successResponse: "Upload Store Image Successfully"
        };
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "No Image Selected"
        },
        HttpStatus.NOT_ACCEPTABLE
      );
    }
  }
  @Post("admin/update_store_snooze/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async updateStoreSnooze(
    @Res() res: Response,
    @Req() request: Request,
    @Param("id") store_id,
    @Body() store: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let storeData: any = await this.adminService.getStoreById(store_id);
    let data: any = {
      store_id: store_id,
      is_snooze: "0"
    };
    let success: any = null;
    if (store.snoozeReason !== "") {
      data.snooze_start_time = store.snooze_start_time;
      data.snooze_end_time = store.snooze_end_time;
      //Snooze Start time in utc
      // .subtract(5, 'h')
      let snooze_start_utc = moment(data.snooze_start_time).subtract(5, "h");
      console.log(snooze_start_utc);
      let snoozeStarthours = moment(snooze_start_utc)
        .utc(true)
        .hours();
      let snoozeStartminutes = moment(snooze_start_utc)
        .utc(true)
        .minutes();
      console.log(snoozeStarthours);
      console.log(snoozeStartminutes);
      let snoozeStartdate = snooze_start_utc.toISOString().split("T")[0];
      const snoozeStartsplitDate: any = snoozeStartdate.split("-");
      const snoozeStartdayOfMonth = snoozeStartsplitDate[2];
      const snoozeStartmonth = snoozeStartsplitDate[1] - 1; //because cron month index starts from 0
      //Snooze End time in utc
      // .subtract(5, 'h')
      let snooze_end_utc = moment(data.snooze_end_time).subtract(5, "h");
      console.log(snooze_end_utc);
      let snoozeEndhours = moment(snooze_end_utc)
        .utc(true)
        .hours();
      let snoozeEndminutes = moment(snooze_end_utc)
        .utc(true)
        .minutes();
      console.log(snoozeEndhours);
      console.log(snoozeEndminutes);
      let snoozeEnddate = snooze_end_utc.toISOString().split("T")[0];
      const snoozeEndsplitDate: any = snoozeEnddate.split("-");
      const snoozeEnddayOfMonth = snoozeEndsplitDate[2];
      const snoozeEndmonth = snoozeEndsplitDate[1] - 1; //because cron month index starts from 0
      let SnoozeStartCron: any = "";
      let SnoozeEndCron: any = "";
      // Generate a v1 (time-based) id
      const SnoozeStartCronname = uuid
        .v1()
        .substr(0, 20)
        .replace(/-/g, "");
      SnoozeStartCron = `${snoozeStartminutes} ${snoozeStarthours} ${snoozeStartdayOfMonth} ${snoozeStartmonth} *`;
      data.snooze_start_cron_id = SnoozeStartCronname;
      data.snooze_start_cron_job = SnoozeStartCron;
      data.snooze_start_cron_job_date =
        snoozeStartdate +
        " " +
        snoozeStarthours +
        ":" +
        snoozeStartminutes +
        ":00";
      const SnoozeEndCronname = uuid
        .v1()
        .substr(0, 20)
        .replace(/-/g, "");
      SnoozeEndCron = `${snoozeEndminutes} ${snoozeEndhours} ${snoozeEnddayOfMonth} ${snoozeEndmonth} *`;
      data.snooze_end_cron_id = SnoozeEndCronname;
      data.snooze_end_cron_job = SnoozeEndCron;
      data.snooze_end_cron_job_date =
        snoozeEnddate + " " + snoozeEndhours + ":" + snoozeEndminutes + ":00";
      success = await this.adminService.updateStoreSnooze(data);
    } else {
      data.snooze_start_time = null;
      data.snooze_end_time = null;
      data.snooze_start_cron_id = null;
      data.snooze_start_cron_job = null;
      data.snooze_start_cron_job_date = null;
      data.snooze_end_cron_id = null;
      data.snooze_end_cron_job = null;
      data.snooze_end_cron_job_date = null;
      success = await this.adminService.updateStoreSnooze(data);
    }
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Snooze date not saved"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logDataArray: any = [];
      if (store.snoozeReason !== "") {
        axios
          .post(
            `http://localhost:5014/tasks/snooze_Job`,
            {
              store_id: storeData.store_id,
              SnoozeStartCronname: data.snooze_start_cron_id,
              SnoozeEndCronname: data.snooze_end_cron_id,
              snoozeStartCron: data.snooze_start_cron_job,
              snoozeEndCron: data.snooze_end_cron_job,
              snooze_type: "store"
            },
            {
              headers: { "Content-Type": "application/json" }
            }
          )
          .then(async (resp: any) => {
            console.log("response", resp.data);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: "Snooze Store Cron Created",
                activity_type: "Stores",
                activity_name: storeData.store_name,
                status: "Snooze Store Cron Creation Successfully",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: store.snoozeReason,
                activity_type: "Stores",
                activity_name: storeData.store_name,
                status: "Snooze Store",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(200).send({
              success: true,
              successResponse: "Update Store Snooze Successfully"
            });
          })
          .catch(async (err: any) => {
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: err.message,
                activity_type: "Stores",
                activity_name: storeData.store_name,
                status: "Snooze Store Cron not saved",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: store.snoozeReason,
                activity_type: "Stores",
                activity_name: storeData.store_name,
                status: "Snooze Store",
                ip_address: _ip
              }
            );
            console.log("err", err);
            await this.adminService.addLogs(logDataArray);
            res.status(500).send({ success: false, message: err.message });
          });
      } else {
        axios
          .put(
            `http://localhost:5014/tasks/delete_snooze_Job`,
            {
              store_id: storeData.store_id,
              snooze_start_cron_id: storeData.snooze_start_cron_id,
              snooze_end_cron_id: storeData.snooze_end_cron_id,
              is_snooze: storeData.is_snooze,
              snooze_type: "store"
            },
            {
              headers: { "Content-Type": "application/json" }
            }
          )
          .then(async (resp: any) => {
            console.log("response", resp.data);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: "Snooze Store Cron Deleted",
                activity_type: "Stores",
                activity_name: storeData.store_name,
                status: "Snooze Store Cron Deleted Successfully",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: store.snoozeReason,
                activity_type: "Stores",
                activity_name: storeData.store_name,
                status: "End Snooze Store",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(200).send({
              success: true,
              successResponse: "Update Store Snooze Successfully"
            });
          })
          .catch(async (err: any) => {
            console.log("err:", err.message);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: err.message,
                activity_type: "Stores",
                activity_name: storeData.store_name,
                status: "Snooze Store Cron Not Deleted",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: store.snoozeReason,
                activity_type: "Stores",
                activity_name: storeData.store_name,
                status: "End Snooze Store",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(500).send({ success: false, message: err.message });
          });
      }
    }
  }
  @Get("admin/storesListForMenu")
  async storesListForMenu(@Req() request: Request): Promise<any> {
    //stores to show list in menu_items
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data = await this.adminService.storesListForMenu(jwtData.user_group_id);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Stores Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/store_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async storeBlockUnblock(@Param("id") id, @Body() store: Store): Promise<any> {
    let storeId = Number(id);
    let success = await this.adminService.storeBlockUnblock(
      storeId,
      store.is_active
    );
    if (success.affected > 0) {
      return { success: true, successResponse: "Store Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Store Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  // Brands ONLY ROUTES

  @Get("admin/brands")
  async brandsData(): Promise<any> {
    let data = await this.adminService.brandsData();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Brands Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/add_brand")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addBrand(@Req() request: Request, @Body() brand: any): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = brand.user_info;
    delete brand.user_info;
    let success = await this.adminService.addBrand(brand);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Brand Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "New Brand",
        activity_type: "Brands",
        activity_name: brand.brand_name,
        status: "Add Brand",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Brand Inserted Successfully" };
    }
  }
  @Put("admin/edit_brand/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editBrand(
    @Req() request: Request,
    @Param("id") id,
    @Body() brand: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = brand.user_info;
    delete brand.user_info;
    let brand_id = Number(id);
    let success = await this.adminService.editBrand(brand_id, brand);
    if (success.affected > 0) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Brands",
        activity_name: brand.brand_name,
        status: "Update Brand",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Brand Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Brand Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/brand/:id")
  async GetBrand(@Param("id") id): Promise<any> {
    let brand_id = Number(id);
    let data = await this.adminService.getBrandById(brand_id);
    return { success: true, successResponse: data };
  }
  @Post("admin/del_brand/:id")
  async deleteBrand(
    @Req() request: Request,
    @Param("id") id,
    @Body() brand: any
  ): Promise<any> {
    let brand_id = Number(id);
    let success = await this.adminService.delBrand(brand_id);
    if (success) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: brand.user_info.role,
        user_name: brand.user_info.first_name + brand.user_info.last_name,
        reason: "Delete",
        activity_type: "Brands",
        activity_name: brand.brand_name,
        status: "Delete Brand",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Brand Deleted Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Brand Not Deleted"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/brand_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async brandBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() brand: any
  ): Promise<any> {
    let brandId = Number(id);
    let success = await this.adminService.brandBlockUnblock(
      brandId,
      brand.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: brand.user_info.role,
        user_name: brand.user_info.first_name + brand.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Brands",
        activity_name: brand.brand_name,
        status: brand.is_active == 1 ? "UnBlock Brand" : "Block Brand",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Brand Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Brand Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/update_brand_nps/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async updateBrandNPS(
    @Req() request: Request,
    @Param("id") id,
    @Body() brand: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let brand_id = Number(id);
    let success = await this.adminService.editBrand(brand_id, brand);
    if (success.affected > 0) {
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Update NPS Configuration",
        activity_type: "NPS",
        activity_name: `Brand (${brand_id})`,
        status: "Update NPS",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "NPS Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "NPS Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Menus Only Routes
  @Get("admin/menus")
  async menuData(): Promise<any> {
    let data = await this.adminService.menuData();
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        data[i].label = data[i].menu_name;
        data[i].value = data[i].menu_id;
        let storesData = await this.adminService.StoresForMenu(data[i]);
        for (let j = 0; j < storesData.length; j++) {
          let obj = storesData[j].store_id;
          storesData[j].value = obj.store_id;
          storesData[j].label = obj.store_name;
        }
        data[i].stores = storesData;
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Menu Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/menu/:id")
  async GetMenu(@Param("id") id): Promise<any> {
    let menu_id = Number(id);
    let data = await this.adminService.getMenuById(menu_id);
    return { success: true, successResponse: data };
  }
  @Post("admin/add_menu")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addMenu(@Req() request: Request, @Body() menu: any): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = menu.user_info;
    delete menu.user_info;
    let success = await this.adminService.addMenu(menu);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Menu Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "New Menu",
        activity_type: "Menus",
        activity_name: menu.menu_name,
        status: "Add Menu",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Menu Inserted Successfully" };
    }
  }
  @Put("admin/edit_menu/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editMenu(
    @Req() request: Request,
    @Param("id") id,
    @Body() menu: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = menu.user_info;
    delete menu.user_info;
    let menuId = Number(id);
    let success = await this.adminService.editMenu(menuId, menu);
    if (success.affected > 0) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Menus",
        activity_name: menu.menu_name,
        status: "Update Menu",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Menu Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Menu Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/storesForMenuRelation")
  async storesForMenuRelation(@Req() request: Request): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data = await this.adminService.storesListForUserGroup(
      jwtData.user_group_id
    );
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Stores Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/menu/groups/:id")
  async GetGroupsOfMenu(@Param("id") id): Promise<any> {
    let menu_id = Number(id);
    let data = await this.adminService.getGroupsOfMenu(menu_id);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Groups Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/menu_publish/:menu_id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async publishMenu(
    @Req() req: Request,
    @Res() res: Response,
    @Param("menu_id") id,
    @Body() menu: any
  ): Promise<any> {
    var _ip: any = req.headers.host?.split(":")[0];
    let userData = menu.user_info;
    delete menu.user_info;
    let menuId = Number(id);
    if (menu.cron_job_id) {
      let success = false;
      if (menu.is_publish == 0) {
        success = await this.adminService.unpublishMenu(
          menuId,
          menu.is_publish
        );
      } else {
        success = await this.adminService.publishmenu(menuId);
      }
      if (success) {
        try {
          const port = 4000;
          const body = JSON.stringify({
            menu_id: menuId,
            cron_job_id: menu.cron_job_id
          });
          var options = {
            host: "localhost",
            port: port,
            path: "/tasks/delete_menuJob",
            method: "PUT",
            body: body,
            headers: { "Content-Type": "application/json" }
          };
          var request = http.request(options, response => {
            console.log("Task Server status: " + response.statusCode);
            response.setEncoding("utf8");
            response.on("data", async chunk => {
              console.log("Task Server body: " + chunk);
              let logData: any = {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: "Publish/UnPublish",
                activity_type: "Menus",
                activity_name: menu.menu_name,
                status:
                  menu.is_publish == 1 ? "Publish Menu" : "UnPublish Menu",
                ip_address: _ip
              };
              await this.adminService.addLogs(logData);
              res.status(HttpStatus.CREATED).send({
                success: true,
                successResponse: "Menu Published Successfully"
              });
            });
          });
          request.on("error", function(e) {
            console.log("problem with request: " + e.message);
            res.status(500).send({ success: false, message: e.message });
          });
          // write data to request body
          request.write(body);
          request.end();
        } catch (err) {
          res.status(500);
          res.json(err);
        }
      } else {
        throw new HttpException(
          {
            success: false,
            message: "Please add items in the menu before publishing"
          },
          HttpStatus.FORBIDDEN
        );
      }
    } else {
      let success = false;
      if (menu.is_publish == 0) {
        success = await this.adminService.unpublishMenu(
          menuId,
          menu.is_publish
        );
      } else {
        success = await this.adminService.publishmenu(menuId);
      }
      if (success) {
        let logData: any = {
          role: userData.role,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "Publish/UnPublish",
          activity_type: "Menus",
          activity_name: menu.menu_name,
          status: menu.is_publish == 1 ? "Publish Menu" : "UnPublish Menu",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        res.status(HttpStatus.CREATED).send({
          success: true,
          successResponse: "Menu Published Successfully"
        });
      } else {
        throw new HttpException(
          {
            success: false,
            message: "Please add items in the menu before publishing"
          },
          HttpStatus.FORBIDDEN
        );
      }
    }
  }
  @Put("admin/publish_menu_future/:menu_id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async publishMenuInFuture(
    @Req() req: Request,
    @Res() res: Response,
    @Param("menu_id") id,
    @Body() data: any
  ): Promise<any> {
    try {
      var _ip: any = req.headers.host?.split(":")[0];
      let userData = data.user_info;
      delete data.user_info;
      let menuId;
      if (id !== "" && data.date !== "") {
        menuId = Number(id);
        // let momTome = new Date(data.date);
        // momTome.setHours(data.time.split(':')[0], data.time.split(':')[1]);
        let momTome = moment(data.date);
        console.log(momTome);
        let hours = moment(momTome)
          .utc(false)
          .hours();
        let minutes = moment(momTome)
          .utc(false)
          .minutes();
        console.log(hours);
        console.log(minutes);
        let date = momTome.toISOString().split("T")[0];
        const splitDate: any = date.split("-");
        const dayOfMonth = splitDate[2];
        const month = splitDate[1] - 1; //because cron month index starts from 0
        let cron: any = "";
        // Generate a v1 (time-based) id
        const name = uuid
          .v1()
          .substr(0, 20)
          .replace(/-/g, "");
        cron = `${minutes} ${hours} ${dayOfMonth} ${month} *`;
        let obj: any = {
          cron_job_id: name,
          cron_job: cron,
          cron_job_date: date + " " + hours + ":" + minutes + ":00"
        };
        console.log(obj);
        let response = await this.adminService.saveCronByMenuId(menuId, obj);
        if (response.affected > 0) {
          try {
            const port = 4000;
            const body = JSON.stringify({
              menuId: menuId,
              name: name,
              cron: cron,
              is_publish: data.is_publish
            });
            var options = {
              host: "localhost",
              port: port,
              path: "/tasks/menu_job",
              method: "POST",
              body: body,
              headers: { "Content-Type": "application/json" }
            };
            var request: any = http.request(options, response => {
              console.log("Task Server status: " + response.statusCode);
              response.setEncoding("utf8");
              response.on("data", async chunk => {
                console.log("Task Server body: " + chunk);
                let logData: any = {
                  role: userData.role,
                  user_name: userData.first_name + " " + userData.last_name,
                  reason: "Publish/UnPublish",
                  activity_type: "Menus",
                  activity_name: data.menu_name,
                  status: "Publish Menu Later",
                  ip_address: _ip
                };
                await this.adminService.addLogs(logData);
                res.status(HttpStatus.CREATED).send({
                  success: true,
                  successResponse: "Menu saved successfully"
                });
              });
            });
            request.on("error", function(e) {
              console.log("problem with request: " + e.message);
              res.status(500).send({ success: false, message: e.message });
            });
            // write data to request body
            request.write(body);
            request.end();
          } catch (err) {
            res.status(500);
            res.json(err);
          }
        } else {
          res.status(409).send({
            success: false,
            message: "Please add items in the menu before publishing"
          });
        }
      } else {
        res
          .status(403)
          .send({ success: false, message: "Please fill in all fields" });
      }
    } catch (e) {
      res.status(500).send({ success: false, message: e.message });
    }
  }
  //Groups Only Routes
  @Get("admin/groups")
  async groupData(): Promise<any> {
    let data = await this.adminService.groupData();
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        data[i].label = data[i].group_name + " (" + data[i].menu_name + ")";
        data[i].value = data[i].group_id;
        let Modes: any = [];
        let itemModes = await this.adminService.getItemModesByGroup(
          data[i].group_id
        );
        if (itemModes && itemModes.length > 0) {
          itemModes.forEach(element => {
            Modes.push({
              label: element.order_mode_id.name,
              value: element.order_mode_id.id
            });
          });
          data[i].mode = JSON.stringify(Modes);
        }
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Groups Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/group/:id")
  async GetGroup(@Param("id") id): Promise<any> {
    let group_id = Number(id);
    let data = await this.adminService.getGroupById(group_id);
    //stores Json to show the selected stores in CMS Edit COMBO
    if (data.itemStores.length > 0) {
      let Array: any = [];
      data.itemStores.forEach(element => {
        let obj = {
          value: element.store_id.store_id,
          label: element.store_id.store_name
        };
        Array.push(obj);
      });
      data.stores_json = JSON.stringify(Array);
    }
    let Modes: any = [];
    let itemModes = await this.adminService.getItemModesByGroup(group_id);
    if (itemModes && itemModes.length > 0) {
      itemModes.forEach(element => {
        Modes.push({
          label: element.order_mode_id.name,
          value: element.order_mode_id.id
        });
      });
      data.mode = JSON.stringify(Modes);
    }
    return { success: true, successResponse: data };
  }
  @Post("admin/add_group")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addGroup(
    @Req() request: Request,
    @UploadedFile() file,
    @Body() group: Group
  ): Promise<any> {
    let menuStores: any = [];
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let itemModes: any = [];
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    if (file) {
      let data: any = group;
      let userData = JSON.parse(data.user_info);
      delete data.user_info;
      data["group_image"] = file.filename;
      let Modes: any = data.mode;
      delete data.mode;
      let success = await this.adminService.addGroup(data);
      let stores = await this.adminService.storesListForMenu(
        jwtData.user_group_id
      );
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: "Group Not Inserted"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        let itemStores: any = [];
        if (data.is_lsm == 1) {
          //if item is lsm item so then stores will insert in ItemStoreRelationTable
          let obj: any = "";
          let storesJson = JSON.parse(data.stores_json);
          if (stores.length > 0) {
            for (let i = 0; i < stores.length; i++) {
              let storeGroupExist = await this.adminService.checkGroupForStores(
                stores[i].value,
                success.group_id
              );
              if (!storeGroupExist) {
                obj = {
                  store_id: stores[i].value,
                  group_id: success.group_id,
                  is_active: 1
                };
                menuStores.push(obj);
              }
            }
          }
          storesJson.forEach(item => {
            // for (let i = 0; i < menuStores.length; i++) {
            //     if (item.value == menuStores[i].store_id) {
            //         menuStores[i].is_active = 1;
            //     }
            // }
            let LSMStores = {
              store_id: item.value,
              group_id: success.group_id
            };
            itemStores.push(LSMStores);
          });
          await this.adminService.addLSMitems(itemStores);
        } else {
          let obj: any;
          if (stores.length > 0) {
            for (let i = 0; i < stores.length; i++) {
              let storeGroupExist = await this.adminService.checkGroupForStores(
                stores[i].value,
                success.group_id
              );
              if (!storeGroupExist) {
                obj = {
                  store_id: stores[i].value,
                  group_id: success.group_id,
                  is_active: 1
                };
                menuStores.push(obj);
              }
            }
          }
        }
        await this.adminService.addStoresForMenu(menuStores);
        if (Modes) {
          //if data has comboChoices item
          let parseModes = JSON.parse(Modes);
          parseModes.forEach(mode => {
            let itemmode = {
              order_mode_id: mode.value,
              group_id: success.group_id
            };
            itemModes.push(itemmode);
          });
        }
        await this.adminService.addOrderItemMode(itemModes);
        let logData: any = {
          role: userData.role,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "New Group",
          activity_type: "Groups",
          activity_name: data.group_name,
          status: "Add Group",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return {
          success: true,
          successResponse: "Group Inserted Successfully"
        };
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "No Image Selected"
        },
        HttpStatus.NOT_ACCEPTABLE
      );
    }
  }
  @Put("admin/group_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async groupBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() group: any
  ): Promise<any> {
    let groupId = Number(id);
    let success = await this.adminService.groupBlockUnblock(
      groupId,
      group.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: group.user_info.role,
        user_name: group.user_info.first_name + " " + group.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Groups",
        activity_name: group.group_name,
        status: group.is_active == 1 ? "UnBlock Group" : "Block Group",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Group Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Group Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_group/:id")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editGroup(
    @Req() request: Request,
    @UploadedFile() file,
    @Param("id") id,
    @Body() group: Group
  ): Promise<any> {
    let group_id = Number(id);
    let itemStores: any = [];
    let menuStores: any = [];
    let itemModes: any = [];
    let data: any = group;
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let userData = JSON.parse(data.user_info);
    delete data.user_info;
    if (file) {
      data["group_image"] = file.filename;
    }
    if (data.pos_code == "null") {
      data.pos_code = null;
    }
    if (data.erp_id == "null") {
      data.erp_id = null;
    }
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    if (data.is_lsm == 1) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteItemStores(
        null,
        null,
        group_id,
        null,
        null,
        null
      );
      let obj: any;
      let storesJson = JSON.parse(data.stores_json);
      if (stores.length > 0) {
        for (let i = 0; i < stores.length; i++) {
          let storeGroupExist = await this.adminService.checkGroupForStores(
            stores[i].value,
            group_id
          );
          if (!storeGroupExist) {
            obj = {
              store_id: stores[i].value,
              group_id: group_id,
              is_active: 1
            };
            menuStores.push(obj);
          }
        }
      }
      storesJson.forEach(item => {
        // for (let i = 0; i < menuStores.length; i++) {
        //     if (item.value == menuStores[i].store_id) {
        //         menuStores[i].is_active = 1;
        //     }
        // }
        let LSMStores = {
          store_id: item.value,
          group_id: group_id
        };
        itemStores.push(LSMStores);
      });
    } else {
      let obj: any;
      if (stores.length > 0) {
        stores.forEach(async element => {
          let storeGroupExist = await this.adminService.checkGroupForStores(
            element.value,
            group_id
          );
          if (!storeGroupExist) {
            obj = { store_id: element.value, group_id: group_id, is_active: 1 };
            menuStores.push(obj);
          }
        });
      }
      await this.adminService.deleteItemStores(
        null,
        null,
        group_id,
        null,
        null,
        null
      );
    }
    delete data.stores_json;
    if (data.mode) {
      //if data has comboChoices item
      await this.adminService.deleteItemModes(null, null, group_id, null);
      let Modes = JSON.parse(data.mode);
      Modes.forEach(mode => {
        let itemmode = {
          order_mode_id: mode.value,
          group_id: group_id
        };
        itemModes.push(itemmode);
      });
    }
    delete data.mode;
    let success = await this.adminService.editGroup(group_id, data);
    if (success.affected > 0) {
      await this.adminService.addStoresForMenu(menuStores);
      await this.adminService.addLSMitems(itemStores);
      await this.adminService.addOrderItemMode(itemModes);
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Groups",
        activity_name: data.group_name,
        status: "Update Group",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Group Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Group Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //SubGroups Only Routes
  @Get("admin/sub-groups")
  async subGroupData(): Promise<any> {
    let data = await this.adminService.subGroupData();
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        data[i].label =
          data[i].sub_group_name + " (" + data[i].group_name + ")";
        data[i].value = data[i].id;
        let Modes: any = [];
        let itemModes = await this.adminService.getItemModesBySubGroup(
          data[i].id
        );
        if (itemModes && itemModes.length > 0) {
          itemModes.forEach(element => {
            Modes.push({
              label: element.order_mode_id.name,
              value: element.order_mode_id.id
            });
          });
          data[i].mode = JSON.stringify(Modes);
        }
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "SubGroups Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/subgroup/:id")
  async GetSubGroup(@Param("id") id): Promise<any> {
    let group_id = Number(id);
    let data = await this.adminService.getSubGroupById(group_id);
    //stores Json to show the selected stores in CMS Edit COMBO
    if (data.itemStores.length > 0) {
      let Array: any = [];
      data.itemStores.forEach(element => {
        let obj = {
          value: element.store_id.store_id,
          label: element.store_id.store_name
        };
        Array.push(obj);
      });
      data.stores_json = JSON.stringify(Array);
    }
    let Modes: any = [];
    let itemModes = await this.adminService.getItemModesBySubGroup(group_id);
    if (itemModes && itemModes.length > 0) {
      itemModes.forEach(element => {
        Modes.push({
          label: element.order_mode_id.name,
          value: element.order_mode_id.id
        });
      });
      data.mode = JSON.stringify(Modes);
    }
    return { success: true, successResponse: data };
  }
  @Post("admin/add_subgroup")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addSubGroup(
    @Req() request: Request,
    @Body() group: SubGroup
  ): Promise<any> {
    let menuStores: any = [];
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let itemModes: any = [];
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data: any = group;
    let Modes: any = data.mode;
    delete data.mode;
    let success = await this.adminService.addSubGroup(data);
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "SubGroup Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let itemStores: any = [];
      if (data.is_lsm == 1) {
        //if item is lsm item so then stores will insert in ItemStoreRelationTable
        let obj: any = "";
        let storesJson = JSON.parse(data.stores_json);
        if (stores.length > 0) {
          for (let i = 0; i < stores.length; i++) {
            let storeGroupExist = await this.adminService.checkSubGroupForStores(
              stores[i].value,
              success.group_id
            );
            if (!storeGroupExist) {
              obj = {
                store_id: stores[i].value,
                subgroup_id: success.id,
                is_active: 1
              };
              menuStores.push(obj);
            }
          }
        }
        storesJson.forEach(item => {
          // for (let i = 0; i < menuStores.length; i++) {
          //     if (item.value == menuStores[i].store_id) {
          //         menuStores[i].is_active = 1;
          //     }
          // }
          let LSMStores = {
            store_id: item.value,
            subgroup_id: success.id
          };
          itemStores.push(LSMStores);
        });
        await this.adminService.addLSMitems(itemStores);
      } else {
        let obj: any;
        if (stores.length > 0) {
          for (let i = 0; i < stores.length; i++) {
            let storeGroupExist = await this.adminService.checkSubGroupForStores(
              stores[i].value,
              success.group_id
            );
            if (!storeGroupExist) {
              obj = {
                store_id: stores[i].value,
                subgroup_id: success.id,
                is_active: 1
              };
              menuStores.push(obj);
            }
          }
        }
      }
      await this.adminService.addStoresForMenu(menuStores);
      if (Modes) {
        //if data has comboChoices item
        let parseModes = JSON.parse(Modes);
        parseModes.forEach(mode => {
          let itemmode = {
            order_mode_id: mode.value,
            subgroup_id: success.id
          };
          itemModes.push(itemmode);
        });
      }
      await this.adminService.addOrderItemMode(itemModes);
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "New SubGroup",
        activity_type: "SubGroups",
        activity_name: data.sub_group_name,
        status: "Add SubGroup",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "SubGroup Inserted Successfully"
      };
    }
  }
  @Put("admin/subgroup_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async subgroupBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() group: any
  ): Promise<any> {
    let groupId = Number(id);
    let success = await this.adminService.subgroupBlockUnblock(
      groupId,
      group.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: group.user_info.role,
        user_name: group.user_info.first_name + " " + group.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "SubGroups",
        activity_name: group.sub_group_name,
        status: group.is_active == 1 ? "UnBlock SubGroup" : "Block SubGroup",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "SubGroup Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "SubGroup Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_subgroup/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editSubGroup(
    @Req() request: Request,
    @Param("id") id,
    @Body() group: SubGroup
  ): Promise<any> {
    let subgroup_id = Number(id);
    let itemStores: any = [];
    let menuStores: any = [];
    let itemModes: any = [];
    let data: any = group;
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    if (data.pos_code == "null") {
      data.pos_code = null;
    }
    if (data.erp_id == "null") {
      data.erp_id = null;
    }
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    if (data.is_lsm == 1) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteItemStores(
        null,
        null,
        null,
        subgroup_id,
        null,
        null
      );
      let obj: any;
      let storesJson = JSON.parse(data.stores_json);
      if (stores.length > 0) {
        for (let i = 0; i < stores.length; i++) {
          let storeGroupExist = await this.adminService.checkSubGroupForStores(
            stores[i].value,
            subgroup_id
          );
          if (!storeGroupExist) {
            obj = {
              store_id: stores[i].value,
              subgroup_id: subgroup_id,
              is_active: 1
            };
            menuStores.push(obj);
          }
        }
      }
      storesJson.forEach(item => {
        // for (let i = 0; i < menuStores.length; i++) {
        //     if (item.value == menuStores[i].store_id) {
        //         menuStores[i].is_active = 1;
        //     }
        // }
        let LSMStores = {
          store_id: item.value,
          subgroup_id: subgroup_id
        };
        itemStores.push(LSMStores);
      });
    } else {
      let obj: any;
      if (stores.length > 0) {
        stores.forEach(async element => {
          let storeGroupExist = await this.adminService.checkSubGroupForStores(
            element.value,
            subgroup_id
          );
          if (!storeGroupExist) {
            obj = {
              store_id: element.value,
              subgroup_id: subgroup_id,
              is_active: 1
            };
            menuStores.push(obj);
          }
        });
      }
      await this.adminService.deleteItemStores(
        null,
        null,
        null,
        subgroup_id,
        null,
        null
      );
    }
    delete data.stores_json;
    if (data.mode) {
      //if data has comboChoices item
      await this.adminService.deleteItemModes(null, null, null, subgroup_id);
      let Modes = JSON.parse(data.mode);
      Modes.forEach(mode => {
        let itemmode = {
          order_mode_id: mode.value,
          subgroup_id: subgroup_id
        };
        itemModes.push(itemmode);
      });
    }
    delete data.mode;
    let success = await this.adminService.editSubGroup(subgroup_id, data);
    if (success.affected > 0) {
      await this.adminService.addStoresForMenu(menuStores);
      await this.adminService.addLSMitems(itemStores);
      await this.adminService.addOrderItemMode(itemModes);
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Edit Info",
        activity_type: "SubGroups",
        activity_name: data.sub_group_name,
        status: "Update SubGroup",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "SubGroup Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "SubGroup Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/group/subgroups/:id")
  async GetSubGroupsOfGroup(@Param("id") id): Promise<any> {
    let group_id = Number(id);
    let data = await this.adminService.GetSubGroupsOfGroup(group_id);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "SubGroups Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  ///Get Menu Items by groupId
  @Get("admin/group/items/:id")
  async GetItemsOfGroup(@Param("id") id): Promise<any> {
    let group_id = Number(id);
    let data = await this.adminService.GetItemsOfSubGroup(group_id);
    console.log("group_id", group_id, "data", data);
    for (let index = 0; index < data.length; index++) {
      let variant: any = await this.adminService.getItemsVariations(
        data[index].menu_item_id
      );
      data[index].item_size = JSON.stringify(variant.variants);
    }
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Items Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Menu Items Only Routes
  @Get("admin/menu_items")
  async ItemData(): Promise<any> {
    let data = await this.adminService.itemsData();
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        let Modes: any = [];
        let specificPrice: any = [];
        let itemModes = await this.adminService.getItemModesByItem(
          data[i].menu_item_id
        );
        if (itemModes && itemModes.length > 0) {
          itemModes.forEach(element => {
            Modes.push({
              label: element.order_mode_id.name,
              value: element.order_mode_id.id
            });
          });
          data[i].item_mode = JSON.stringify(Modes);
        }
        let specificPrices = await this.adminService.getSpecificStorePricesByItem(
          data[i].menu_item_id
        );
        if (specificPrices && specificPrices.length > 0) {
          specificPrices.forEach(element => {
            specificPrice.push({
              store_id: element.store_id.store_id,
              mrp: element.mrp
            });
          });
          data[i].storesJson = JSON.stringify(specificPrice);
        }
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Items Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/menu_item/:id")
  async GetItem(@Param("id") id): Promise<any> {
    let menu_item_id = Number(id);
    let data = await this.adminService.getItemById(menu_item_id);
    if (data.itemStores.length > 0) {
      let Array: any = [];
      data.itemStores.forEach(element => {
        let obj = {
          value: element.store_id.store_id,
          label: element.store_id.store_name
        };
        Array.push(obj);
      });
      data.stores_json = JSON.stringify(Array);
    }
    let Modes: any = [];
    let itemModes = await this.adminService.getItemModesByItem(menu_item_id);
    if (itemModes && itemModes.length > 0) {
      itemModes.forEach(element => {
        Modes.push({
          label: element.order_mode_id.name,
          value: element.order_mode_id.id
        });
      });
      data.item_mode = JSON.stringify(Modes);
    }
    if (data.variants.length > 0) {
      let Array: any = [];
      for (let i = 0; i < data.variants.length; i++) {
        let orderModePrice: any = [];
        let orderModeData = await this.adminService.getOrderModesPrice(
          data.variants[i].id
        );
        if (orderModeData.length > 0) {
          orderModeData.forEach(orderModeId => {
            orderModePrice.push({
              item_variant_id: data.variants[i].id,
              order_mode_id: orderModeId.order_mode_id.id,
              mrp: orderModeId.mrp,
              extra_price: orderModeId.extra_price,
              label: orderModeId.order_mode_id.name,
              value: orderModeId.order_mode_id.id
            });
          });
        } else {
          let mode = data.item_mode ? JSON.parse(data.item_mode) : [];
          let orderingModes = await this.adminService.getOrderModes();
          const results = orderingModes.filter(({ name: id1 }) =>
            mode.some(({ label: id2 }) => id2 === id1)
          );
          results.forEach(element => {
            orderModePrice.push({
              item_variant_id: data.variants[i].id,
              order_mode_id: element.id,
              mrp: data.variants[i].mrp,
              extra_price: data.variants[i].extra_price,
              label: element.name,
              value: element.id
            });
          });
        }
        let sizejson = {
          id: data.variants[i].id,
          pos_code: data.variants[i].pos_code,
          erp_id: data.variants[i].erp_id,
          size: data.variants[i].size,
          variant_id: data.variants[i].variant_id,
          cost: data.variants[i].cost,
          mrp: data.variants[i].mrp,
          extra_price: data.variants[i].extra_price,
          description: data.variants[i].description,
          image_url: data.variants[i].image_url,
          thumbnail_url: data.variants[i].thumbnail_url,
          alt_text: data.variants[i].alt_text,
          nutritional_info: data.variants[i].total_nutrition,
          serving: data.variants[i].serving,
          order_modes_price: orderModePrice
        };
        Array.push(sizejson);
      }
      data.item_size = JSON.stringify(Array);
    }
    return { success: true, successResponse: data };
  }
  @Post("admin/add_menu_item")
  @UseInterceptors(
    FilesInterceptor("files", 20, {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      // limits: {
      //     fileSize: 2000000   //2md
      // },
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addItem(
    @Req() request: Request,
    @UploadedFiles() files,
    @Body() items: MenuItem
  ): Promise<any> {
    if (files.length > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let token: any = request.headers.authorization?.split("Bearer ")[1];
      let jwtData: any = jwtDecode(token);
      let data: any = items;
      let userData = JSON.parse(data.user_info);
      delete data.user_info;
      let menuStores: any = [];
      let itemModes: any = [];
      let Modes: any = data.item_mode;
      delete data.item_mode;
      if (!data.serving_hours) {
        data.serving_hours = null;
      }
      let success = await this.adminService.addItem(data);
      let stores = await this.adminService.storesListForMenu(
        jwtData.user_group_id
      );
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: "Item Not Inserted"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        let heroImgData: any = [];
        let itemStores: any = [];
        let variants: any = [];
        let modifiers: any = [];
        let heroImageobj: any = {
          menu_item_id: null,
          image: "",
          hero_mobile_image: null
        };
        files.forEach(file => {
          if (file.filename.includes("_hero_image")) {
            heroImageobj.menu_item_id = success.menu_item_id;
            heroImageobj.image = file.filename;
          }
          if (file.filename.includes("_hero_mobile_image")) {
            heroImageobj.menu_item_id = success.menu_item_id;
            heroImageobj.hero_mobile_image = file.filename;
          }
        });
        if (heroImageobj.menu_item_id) {
          heroImgData.push(heroImageobj);
          await this.adminService.addHeroItem(heroImgData);
        }
        if (data.itemSizes) {
          //if item is lsm item so then stores will insert in ItemStoreRelationTable
          let sizes = JSON.parse(data.itemSizes);
          for (var x = 0; x < sizes.length; x++) {
            files.forEach(file => {
              if (file.filename.includes(`_variant_${x}`)) {
                sizes[x].image_url = file.filename;
              }
              if (file.filename.includes(`_thumbnail_${x}`)) {
                sizes[x].thumbnail_url = file.filename;
              }
            });
          }

          sizes.forEach(item => {
            let itemSize: any = {
              menu_item_id: success.menu_item_id,
              size: item.size,
              cost: item.cost,
              mrp: item.mrp,
              variant_id: item.variant_id,
              extra_price: item.extra_price,
              description: item.description,
              alt_text: item.alt_text,
              image_url: item.image_url,
              thumbnail_url: item.thumbnail_url,
              total_nutrition: item.nutritional_info,
              serving: item.serving
            };
            if (item.pos_code !== "") {
              itemSize.pos_code = item.pos_code;
            }
            if (item.erp_id !== "") {
              itemSize.erp_id = item.erp_id;
            }
            if (item.aloha_cat_id !== "") {
              itemSize.aloha_cat_id = item.aloha_cat_id;
            }
            itemSize.OrderModePrice = item.order_modes_price;
            variants.push(itemSize);

            let modifier: any = {
              modifier_name: success?.item_name + itemSize?.size,
              erp_id: itemSize.erp_id,
              pos_code: itemSize.pos_code,
              isFoodPanda: 1
            };
            modifiers.push(modifier);
          });
        }
        if (data.is_lsm == 1) {
          //if item is lsm item so then stores will insert in ItemStoreRelationTable
          let obj: any;
          let storesJson = JSON.parse(data.stores_json);
          if (stores.length > 0) {
            for (let i = 0; i < stores.length; i++) {
              let storeGroupExist = await this.adminService.checkMenuItemForStores(
                stores[i].value,
                success.menu_item_id
              );
              if (!storeGroupExist) {
                obj = {
                  store_id: stores[i].value,
                  menu_item_id: success.menu_item_id,
                  is_active: 1
                };
                menuStores.push(obj);
              }
            }
          }
          storesJson.forEach(item => {
            // for (let i = 0; i < menuStores.length; i++) {
            //     if (item.value == menuStores[i].store_id) {
            //         menuStores[i].is_active = 1;
            //     }
            // }
            let LSMStores = {
              store_id: item.value,
              menu_item_id: success.menu_item_id
            };
            itemStores.push(LSMStores);
          });
          await this.adminService.addLSMitems(itemStores);
        } else if (data.is_lsm == 2) {
          //if item is lsm item so then stores will insert in ItemStoreRelationTable
          let obj: any;

          let storesJson = JSON.parse(data.stores_json);
          if (stores.length > 0) {
            for (let i = 0; i < stores.length; i++) {
              let storeGroupExist = await this.adminService.checkMenuItemForStores(
                stores[i].value,
                success.menu_item_id
              );
              if (!storeGroupExist) {
                obj = {
                  store_id: stores[i].value,
                  menu_item_id: success.menu_item_id,
                  is_active: 1
                };
                menuStores.push(obj);
              }
            }
          }
          storesJson.forEach(item => {
            // for (let i = 0; i < menuStores.length; i++) {
            //     if (item.value == menuStores[i].store_id) {
            //         menuStores[i].is_active = 1;
            //     }
            // }
            let LSMStores = {
              store_id: item.value,
              menu_item_id: success.menu_item_id
            };
            itemStores.push(LSMStores);
          });
          await this.adminService.addLSMitems(itemStores);
        } else {
          let obj: any;
          for (let i = 0; i < stores.length; i++) {
            let storeGroupExist = await this.adminService.checkMenuItemForStores(
              stores[i].value,
              success.menu_item_id
            );
            if (!storeGroupExist) {
              obj = {
                store_id: stores[i].value,
                menu_item_id: success.menu_item_id,
                is_active: 1
              };
              menuStores.push(obj);
            }
          }
        }
        try {
          if (modifiers.length > 0) {
            await this.adminService.addModifier(modifiers);
          }
          if (variants.length > 0) {
            for (let i = 0; i < variants.length; i++) {
              let ModePrices = variants[i].OrderModePrice;
              let variantSuccess = await this.adminService.additemVariants(
                variants[i]
              );
              let obj: any;
              for (let j = 0; j < stores.length; j++) {
                let storeVarExist = await this.adminService.checkItemVariantsForStores(
                  stores[j].value,
                  variantSuccess.id
                );
                if (!storeVarExist) {
                  obj = {
                    store_id: stores[j].value,
                    item_variant_id: variantSuccess.id,
                    is_active: 1
                  };
                  menuStores.push(obj);
                }
              }
              for (let i = 0; i < ModePrices.length; i++) {
                ModePrices[i].item_variant_id = variantSuccess.id;
              }
              await this.adminService.addOrderModePrices(ModePrices);
            }
          }
          await this.adminService.addStoresForMenu(menuStores);
        } catch (e) {
          console.log("add Item Variant Exception", e);
        }
        if (Modes) {
          //if data has comboChoices item
          let parseModes = JSON.parse(Modes);
          parseModes.forEach(mode => {
            let itemmode = {
              order_mode_id: mode.value,
              menu_item_id: success.menu_item_id
            };
            itemModes.push(itemmode);
          });
        }
        await this.adminService.addOrderItemMode(itemModes);
        let logData: any = {
          role: userData.role,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "New Menu Item",
          activity_type: "Menu Items",
          activity_name: data.item_name,
          status: "Add Menu Item",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return { success: true, successResponse: "Item Inserted Successfully" };
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "No Image Selected"
        },
        HttpStatus.NOT_ACCEPTABLE
      );
    }
  }
  @Post("admin/menu_file_upload")
  @UseInterceptors(
    FileInterceptor("menu_file", {
      storage: diskStorage({
        destination: "./Uploads/images",
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  async menuFileUpload(@UploadedFile() file, @Body() body: any): Promise<any> {
    if (file) {
      let brandData = await this.adminService.getBrandById(body.brand_id);
      if (brandData.menu_file) {
        if (fs.existsSync(`Uploads/images/${brandData.menu_file}`)) {
          fs.unlinkSync(`Uploads/images/${brandData.menu_file}`);
        }
      }
      let data = { menu_file: file.filename };
      if (data.menu_file !== "") {
        let success = await this.adminService.editBrand(body.brand_id, data);
        if (success.affected > 0) {
          return {
            success: true,
            successResponse: "Menu file uploaded successfully"
          };
        } else {
          throw new HttpException(
            {
              success: false,
              message: "Menu file not uploaded"
            },
            HttpStatus.FORBIDDEN
          );
        }
      } else {
        throw new HttpException(
          {
            success: false,
            message: "Menu file not uploaded"
          },
          HttpStatus.FORBIDDEN
        );
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "File Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/item_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async itemBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() item: any
  ): Promise<any> {
    let itemId = Number(id);
    let success = await this.adminService.itemBlockUnblock(
      itemId,
      item.is_active
    );
    if (success.affected > 0) {
      if (item.relatedCombos && item.relatedCombos.length > 0) {
        let comboIds: any = [];
        for (var obj of item.relatedCombos) {
          comboIds.push(obj.combo_id.combo_id);
        }
        await this.adminService.blockItemRelatedCombos(comboIds, itemId);
      }
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: item.user_info.role,
        user_name: item.user_info.first_name + " " + item.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Menu Items",
        activity_name: item.item_ame,
        status: item.is_active == 1 ? "UnBlock Menu Item" : "Block Menu Item",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Item Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Item Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_menu_item/:id")
  @UseInterceptors(
    FilesInterceptor("files", 20, {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editItem(
    @Req() request: Request,
    @UploadedFiles() files,
    @Param("id") id,
    @Body() items: MenuItem
  ): Promise<any> {
    let itemId = Number(id);
    let data: any = items;
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let userData = JSON.parse(data.user_info);
    delete data.user_info;
    let itemStores: any = [];
    let menuStores: any = [];
    let itemModes: any = [];
    // let variants: any = [];
    if (data.item_start_time == "null" && data.item_close_time == "null") {
      data.item_start_time = null;
      data.item_close_time = null;
    }
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    if (data.is_lsm == 1) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteItemStores(
        itemId,
        null,
        null,
        null,
        null,
        null
      );
      let obj: any;
      let storesJson = JSON.parse(data.stores_json);
      if (stores.length > 0) {
        for (let i = 0; i < stores.length; i++) {
          let storeGroupExist = await this.adminService.checkMenuItemForStores(
            stores[i].value,
            itemId
          );
          if (!storeGroupExist) {
            obj = {
              store_id: stores[i].value,
              menu_item_id: itemId,
              is_active: 1
            };
            menuStores.push(obj);
          }
        }
      }
      storesJson.forEach(item => {
        // for (let i = 0; i < menuStores.length; i++) {
        //     if (item.value == menuStores[i].store_id) {
        //         menuStores[i].is_active = 1;
        //     }
        // }
        let LSMStores = {
          store_id: item.value,
          menu_item_id: itemId
        };
        itemStores.push(LSMStores);
      });
    } else if (data.is_lsm == 2) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteItemStores(
        itemId,
        null,
        null,
        null,
        null,
        null
      );
      let obj: any;
      let storesJson = JSON.parse(data.stores_json);
      if (stores.length > 0) {
        for (let i = 0; i < stores.length; i++) {
          let storeGroupExist = await this.adminService.checkMenuItemForStores(
            stores[i].value,
            itemId
          );
          if (!storeGroupExist) {
            obj = {
              store_id: stores[i].value,
              menu_item_id: itemId,
              is_active: 1
            };
            menuStores.push(obj);
          }
        }
      }
      storesJson.forEach(item => {
        // for (let i = 0; i < menuStores.length; i++) {
        //     if (item.value == menuStores[i].store_id) {
        //         menuStores[i].is_active = 1;
        //     }
        // }
        let LSMStores = {
          store_id: item.value,
          menu_item_id: itemId
        };
        itemStores.push(LSMStores);
      });
    } else {
      let obj: any;
      if (stores.length > 0) {
        stores.forEach(async element => {
          let storeGroupExist = await this.adminService.checkMenuItemForStores(
            element.value,
            itemId
          );
          if (!storeGroupExist) {
            obj = {
              store_id: element.value,
              menu_item_id: itemId,
              is_active: 1
            };
            menuStores.push(obj);
          }
        });
      }
      await this.adminService.deleteItemStores(
        itemId,
        null,
        null,
        null,
        null,
        null
      );
    }
    if (data.itemSizes) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      // await this.adminService.deleteItemVariants(itemId);
      let variants: any = [];
      let modifiers: any = [];
      let sizes = JSON.parse(data.itemSizes);
      let dbSizes = await this.adminService.findVariantOfMenuItemID(itemId);
      for (let x = 0; x < sizes.length; x++) {
        files.forEach(file => {
          if (file.filename.includes(`_variant_${x}`)) {
            sizes[x].image_url = file.filename;
          }
          if (file.filename.includes(`_thumbnail_${x}`)) {
            sizes[x].thumbnail_url = file.filename;
          }
        });
      }
      for (let x = 0; x < sizes.length; x++) {
        let itemSize: any = {
          menu_item_id: itemId,
          size: sizes[x].size,
          cost: sizes[x].cost,
          mrp: sizes[x].mrp,
          variant_id: sizes[x].variant_id,
          extra_price: sizes[x].extra_price,
          alt_text: sizes[x].alt_text,
          description: sizes[x].description,
          image_url: sizes[x].image_url,
          thumbnail_url: sizes[x].thumbnail_url,
          total_nutrition: sizes[x].nutritional_info,
          serving: sizes[x].serving
        };
        if (sizes[x].pos_code !== "") {
          itemSize.pos_code = sizes[x].pos_code;
        }
        if (sizes[x].erp_id !== "") {
          itemSize.erp_id = sizes[x].erp_id;
        }
        if (sizes[x].aloha_cat_id !== "") {
          itemSize.aloha_cat_id = sizes[x].aloha_cat_id;
        }
        if (sizes[x].id == "") {
          if (sizes[x].image_url == "" && sizes[x].image_url == null) {
            itemSize.image_url = null;
          }
          if (sizes[x].thumbnail_url == "" && sizes[x].thumbnail_url == null) {
            itemSize.thumbnail_url = null;
          }

          itemSize.OrderModePrice = sizes[x].order_modes_price;
          variants.push(itemSize);
        }
        // Add modifier for foodpanda integrations
        let modifier: any = {
          modifier_name: data?.item_name + itemSize?.size,
          erp_id: itemSize.erp_id,
          pos_code: itemSize.pos_code,
          isFoodPanda: 1
        };
        modifiers.push(modifier);

        //To update the existing variants
        for (let ind = 0; ind < dbSizes.length; ind++) {
          if (dbSizes[ind].id === sizes[x].id) {
            await this.adminService.deleteModePriceForVariant(dbSizes[ind].id);
            if (dbSizes[ind].image_url != sizes[x].image_url) {
              if (
                dbSizes[ind].image_url !== "" &&
                fs.existsSync(`Uploads/images/${dbSizes[ind].image_url}`)
              ) {
                fs.unlinkSync(`Uploads/images/${dbSizes[ind].image_url}`);
              }
            }
            if (dbSizes[ind].thumbnail_url != sizes[x].thumbnail_url) {
              if (
                dbSizes[ind].thumbnail_url !== "" &&
                fs.existsSync(`Uploads/images/${dbSizes[ind].thumbnail_url}`)
              ) {
                fs.unlinkSync(`Uploads/images/${dbSizes[ind].thumbnail_url}`);
              }
            }
            await this.adminService.updateItemVariants(sizes[x].id, itemSize);
            await this.adminService.addOrderModePrices(
              sizes[x].order_modes_price
            );
            for (let j = 0; j < stores.length; j++) {
              let storeVarExist = await this.adminService.checkItemVariantsForStores(
                stores[j].value,
                sizes[x].id
              );
              if (!storeVarExist) {
                let storeMenuObj = {
                  store_id: stores[j].value,
                  item_variant_id: sizes[x].id,
                  is_active: 1
                };
                menuStores.push(storeMenuObj);
              }
            }
          }
        }
      }
      // sizes.forEach(async (item) => {
      //     let itemSize: any = {
      //         menu_item_id: itemId,
      //         size: item.size,
      //         cost: item.cost,
      //         mrp: item.mrp,
      //         variant_id: item.variant_id,
      //         extra_price: item.extra_price,
      //         alt_text: item.alt_text,
      //         description: item.description,
      //         image_url: item.image_url,
      //         thumbnail_url: item.thumbnail_url,
      //         total_nutrition: item.nutritional_info
      //     }
      //     if (item.pos_code !== "") {
      //         itemSize.pos_code = item.pos_code;
      //     } if (item.erp_id !== "") {
      //         itemSize.erp_id = item.erp_id;
      //     } if (item.aloha_cat_id !== "") {
      //         itemSize.aloha_cat_id = item.aloha_cat_id;
      //     }
      //     if (item.id == "") {
      //         if (item.image_url == "" && item.image_url == null) {
      //             itemSize.image_url = null
      //         }
      //         if (item.thumbnail_url == "" && item.thumbnail_url == null) {
      //             itemSize.thumbnail_url = null
      //         }

      //         itemSize.OrderModePrice = item.order_modes_price;
      //         variants.push(itemSize);
      //     }
      //     //To update the existing variants
      //     dbSizes.forEach(async (obj) => {
      //         if (obj.id === item.id) {
      //             await this.adminService.deleteModePriceForVariant(obj.id)
      //             if (obj.image_url != item.image_url) {
      //                 if (obj.image_url !== "" && fs.existsSync(`Uploads/images/${obj.image_url}`)) {
      //                     fs.unlinkSync(`Uploads/images/${obj.image_url}`);
      //                 }
      //             }
      //             if (obj.thumbnail_url != item.thumbnail_url) {
      //                 if (obj.thumbnail_url !== "" && fs.existsSync(`Uploads/images/${obj.thumbnail_url}`)) {
      //                     fs.unlinkSync(`Uploads/images/${obj.thumbnail_url}`);
      //                 }
      //             }
      //             await this.adminService.updateItemVariants(item.id, itemSize);
      //             await this.adminService.addOrderModePrices(item.order_modes_price)
      //             for (let j = 0; j < stores.length; j++) {
      //                 console.log("stores[j].value",stores[j].value,item.id)
      //                 let storeVarExist = await this.adminService.checkItemVariantsForStores(stores[j].value, item.id);
      //                 if (!storeVarExist) {
      //                     obj = { store_id: stores[j].value, item_variant_id: item.id, is_active: 1 }
      //                     menuStores.push(obj);
      //                 }
      //             }
      //         }
      //     });
      // });
      //To delete the missing variants
      dbSizes.forEach(async element => {
        let data = sizes.find(obj => {
          if (element.id == obj.id) {
            return obj;
          }
        });
        if (!data) {
          // let choices = await this.adminService.getComboChoicesVariants(element.id);
          // if (choices && choices.length > 0) {
          //     let deleteChoicevariants: any = await this.adminService.deleteComboChoicesVariants(element.id)
          //     if (deleteChoicevariants.affected > 0) {
          //         await this.adminService.deleteModePriceForVariant(element.id)
          //         await this.adminService.deleteStoreMenuItemVar(element.id)
          //         let response: any = await this.adminService.deleteItemVariant(element.id)
          //         if (response.affected > 0) {
          //             if (element.image_url !== "" && fs.existsSync(`Uploads/images/${element.image_url}`)) {
          //                 fs.unlinkSync(`Uploads/images/${element.image_url}`);
          //             }
          //         }
          //     }
          // } else {
          await this.adminService.deleteMenuBlockLogsForVariant(element.id);
          await this.adminService.deleteModePriceForVariant(element.id);
          await this.adminService.deleteStoreMenuItemVar(element.id);
          let response: any = await this.adminService.deleteItemVariant(
            element.id
          );
          if (response.affected > 0) {
            if (
              element.image_url !== "" &&
              fs.existsSync(`Uploads/images/${element.image_url}`)
            ) {
              fs.unlinkSync(`Uploads/images/${element.image_url}`);
            }
            if (
              element.thumbnail_url !== "" &&
              fs.existsSync(`Uploads/images/${element.thumbnail_url}`)
            ) {
              fs.unlinkSync(`Uploads/images/${element.thumbnail_url}`);
            }
          }
          // }
        }
      });
      //To insert the new variants
      try {
        if (variants.length > 0) {
          for (let i = 0; i < variants.length; i++) {
            let ModePrices = variants[i].OrderModePrice;
            let variantSuccess = await this.adminService.additemVariants(
              variants[i]
            );
            let obj: any;
            for (let j = 0; j < stores.length; j++) {
              let storeVarExist = await this.adminService.checkItemVariantsForStores(
                stores[j].value,
                variantSuccess.id
              );
              if (!storeVarExist) {
                obj = {
                  store_id: stores[j].value,
                  item_variant_id: variantSuccess.id,
                  is_active: 1
                };
                menuStores.push(obj);
              }
            }
            for (let i = 0; i < ModePrices.length; i++) {
              ModePrices[i].item_variant_id = variantSuccess.id;
            }
            await this.adminService.addOrderModePrices(ModePrices);
          }
        }
        // check modifiers exists
        if (modifiers.length > 0) {
          let modifiersArr: any = [];
          for (let ind = 0; ind < modifiers.length; ind++) {
            const element = await this.adminService.getModifiersByPosCode(
              modifiers[ind].pos_code
            );
            if (element == undefined || element == null) {
              modifiersArr.push(modifiers[ind]);
            }
          }
          if (modifiersArr.length > 0) {
            await this.adminService.addModifier(modifiersArr);
          }
        }
      } catch (e) {
        console.log("Edit Item Variant Exception", e);
      }
    }
    if (data.item_mode) {
      //if data has comboChoices item
      await this.adminService.deleteItemModes(itemId, null, null, null);
      let Modes = JSON.parse(data.item_mode);
      Modes.forEach(mode => {
        let itemmode = {
          order_mode_id: mode.value,
          menu_item_id: itemId
        };
        itemModes.push(itemmode);
      });
    }
    delete data.item_mode;
    delete data.stores_json;
    delete data.itemSizes;
    if (!data.serving_hours) {
      data.serving_hours = null;
    }

    let is_foodpanda = data.is_foodpanda;

    let success = await this.adminService.editItem(itemId, data);
    if (success.affected > 0) {
      if (is_foodpanda == 0) {
        //delete from combo_timing
        await this.adminService.removeMenuTime(itemId);
      }
      let heroImagesResult: any = await this.adminService.getItemHeroImages(
        itemId
      );
      if (data.hero_item == 0) {
        if (heroImagesResult) {
          let response: any = await this.adminService.deleteItemHeroImages(
            itemId
          );
          if (response.affected > 0) {
            if (
              heroImagesResult.image !== "" &&
              fs.existsSync(`Uploads/images/${heroImagesResult.image}`)
            ) {
              fs.unlinkSync(`Uploads/images/${heroImagesResult.image}`);
            }
            if (
              heroImagesResult.hero_mobile_image !== "" &&
              fs.existsSync(
                `Uploads/images/${heroImagesResult.hero_mobile_image}`
              )
            ) {
              fs.unlinkSync(
                `Uploads/images/${heroImagesResult.hero_mobile_image}`
              );
            }
          }
        }
      }
      if (files.length > 0) {
        // let response: any = await this.adminService.deleteItemHeroImages(itemId);
        files.forEach(async file => {
          if (file.filename.includes("_hero_image")) {
            if (heroImagesResult) {
              await this.adminService.deleteItemHeroImages(itemId);
              if (
                heroImagesResult.image !== "" &&
                fs.existsSync(`Uploads/images/${heroImagesResult.image}`)
              ) {
                fs.unlinkSync(`Uploads/images/${heroImagesResult.image}`);
              }
            }
          } else if (file.filename.includes("_hero_mobile_image")) {
            if (heroImagesResult) {
              await this.adminService.deleteItemHeroImages(itemId);
              if (
                heroImagesResult.hero_mobile_image !== "" &&
                fs.existsSync(
                  `Uploads/images/${heroImagesResult.hero_mobile_image}`
                )
              ) {
                fs.unlinkSync(
                  `Uploads/images/${heroImagesResult.hero_mobile_image}`
                );
              }
            }
          } else {
            await this.adminService.deleteItemImages(itemId);
          }
        });
      }
      let heroImgData: any = [];
      let heroImageobj: any = {
        menu_item_id: null,
        image: "",
        hero_mobile_image: null
      };
      files.forEach(file => {
        if (file.filename.includes("_hero_image")) {
          heroImageobj.menu_item_id = itemId;
          heroImageobj.image = file.filename;
          if (heroImagesResult) {
            heroImageobj.hero_mobile_image = heroImagesResult.hero_mobile_image;
          }
        }
        if (file.filename.includes("_hero_mobile_image")) {
          heroImageobj.menu_item_id = itemId;
          if (isEmpty(heroImageobj.image) && heroImagesResult) {
            heroImageobj.image = heroImagesResult.image;
          }
          heroImageobj.hero_mobile_image = file.filename;
        }
      });
      if (heroImageobj.menu_item_id) {
        heroImgData.push(heroImageobj);
        await this.adminService.addHeroItem(heroImgData);
      }
      await this.adminService.addLSMitems(itemStores);
      await this.adminService.addStoresForMenu(menuStores);
      await this.adminService.addOrderItemMode(itemModes);
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Menu Items",
        activity_name: data.item_name,
        status: "Update Menu Item",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Item Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Item Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/check_item_poscode/:pos_code")
  async checkPosCode(@Param("pos_code") poscode): Promise<any> {
    let data = await this.adminService.checkPOSCode(poscode);
    if (data) {
      throw new HttpException(
        {
          success: false,
          message: `POS code already exist for ${data.menu_item_id.item_name}(${data.size})`
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      return { success: true, successResponse: "POS code not found" };
    }
  }
  @Get("admin/check_item_erpId/:erp_id")
  async checkERPId(@Param("erp_id") erpid): Promise<any> {
    let data = await this.adminService.checkERPId(erpid);
    if (data) {
      throw new HttpException(
        {
          success: false,
          message: `ERP Id already exist for ${data.menu_item_id.item_name}(${data.size})`
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      return { success: true, successResponse: "ERP Id not found" };
    }
  }
  @Post("admin/update_item_snooze/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async updateMenuItemSnooze(
    @Res() res: Response,
    @Req() request: Request,
    @Param("id") menu_item_id,
    @Body() menu_item: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let itemData: any = await this.adminService.getItemById(menu_item_id);
    let data: any = {
      menu_item_id: menu_item_id,
      is_snooze: "0"
    };
    let success: any = null;
    if (menu_item.snoozeReason !== "") {
      data.snooze_start_time = menu_item.snooze_start_time;
      data.snooze_end_time = menu_item.snooze_end_time;
      //Snooze Start time in utc
      // .subtract(5, 'h')
      let snooze_start_utc = moment(data.snooze_start_time).subtract(5, "h");
      console.log(snooze_start_utc);
      let snoozeStarthours = moment(snooze_start_utc)
        .utc(true)
        .hours();
      let snoozeStartminutes = moment(snooze_start_utc)
        .utc(true)
        .minutes();
      console.log(snoozeStarthours);
      console.log(snoozeStartminutes);
      let snoozeStartdate = snooze_start_utc.toISOString().split("T")[0];
      const snoozeStartsplitDate: any = snoozeStartdate.split("-");
      const snoozeStartdayOfMonth = snoozeStartsplitDate[2];
      const snoozeStartmonth = snoozeStartsplitDate[1] - 1; //because cron month index starts from 0
      //Snooze End time in utc
      // .subtract(5, 'h')
      let snooze_end_utc = moment(data.snooze_end_time).subtract(5, "h");
      console.log(snooze_end_utc);
      let snoozeEndhours = moment(snooze_end_utc)
        .utc(true)
        .hours();
      let snoozeEndminutes = moment(snooze_end_utc)
        .utc(true)
        .minutes();
      console.log(snoozeEndhours);
      console.log(snoozeEndminutes);
      let snoozeEnddate = snooze_end_utc.toISOString().split("T")[0];
      const snoozeEndsplitDate: any = snoozeEnddate.split("-");
      const snoozeEnddayOfMonth = snoozeEndsplitDate[2];
      const snoozeEndmonth = snoozeEndsplitDate[1] - 1; //because cron month index starts from 0
      let SnoozeStartCron: any = "";
      let SnoozeEndCron: any = "";
      // Generate a v1 (time-based) id
      const SnoozeStartCronname = uuid
        .v1()
        .substr(0, 20)
        .replace(/-/g, "");
      SnoozeStartCron = `${snoozeStartminutes} ${snoozeStarthours} ${snoozeStartdayOfMonth} ${snoozeStartmonth} *`;
      data.snooze_start_cron_id = SnoozeStartCronname;
      data.snooze_start_cron_job = SnoozeStartCron;
      data.snooze_start_cron_job_date =
        snoozeStartdate +
        " " +
        snoozeStarthours +
        ":" +
        snoozeStartminutes +
        ":00";
      const SnoozeEndCronname = uuid
        .v1()
        .substr(0, 20)
        .replace(/-/g, "");
      SnoozeEndCron = `${snoozeEndminutes} ${snoozeEndhours} ${snoozeEnddayOfMonth} ${snoozeEndmonth} *`;
      data.snooze_end_cron_id = SnoozeEndCronname;
      data.snooze_end_cron_job = SnoozeEndCron;
      data.snooze_end_cron_job_date =
        snoozeEnddate + " " + snoozeEndhours + ":" + snoozeEndminutes + ":00";
      success = await this.adminService.updateItemSnooze(data);
    } else {
      data.snooze_start_time = null;
      data.snooze_end_time = null;
      data.snooze_start_cron_id = null;
      data.snooze_start_cron_job = null;
      data.snooze_start_cron_job_date = null;
      data.snooze_end_cron_id = null;
      data.snooze_end_cron_job = null;
      data.snooze_end_cron_job_date = null;
      success = await this.adminService.updateItemSnooze(data);
    }
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Snooze date not saved"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logDataArray: any = [];
      if (menu_item.snoozeReason !== "") {
        axios
          .post(
            `http://localhost:5014/tasks/snooze_Job`,
            {
              menu_item_id: itemData.menu_item_id,
              SnoozeStartCronname: data.snooze_start_cron_id,
              SnoozeEndCronname: data.snooze_end_cron_id,
              snoozeStartCron: data.snooze_start_cron_job,
              snoozeEndCron: data.snooze_end_cron_job,
              snooze_type: "item"
            },
            {
              headers: { "Content-Type": "application/json" }
            }
          )
          .then(async (resp: any) => {
            console.log("response", resp.data);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: "Snooze Item Cron Created",
                activity_type: "Menu Items",
                activity_name: itemData.item_name,
                status: "Snooze Item Cron Creation Successfully",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: menu_item.snoozeReason,
                activity_type: "Menu Items",
                activity_name: itemData.item_name,
                status: "Snooze Items",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(200).send({
              success: true,
              successResponse: "Update Item Snooze Successfully"
            });
          })
          .catch(async (err: any) => {
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: err.message,
                activity_type: "Menu Items",
                activity_name: itemData.store_name,
                status: "Snooze Item Cron not saved",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: menu_item.snoozeReason,
                activity_type: "Menu Items",
                activity_name: itemData.item_name,
                status: "Snooze Items",
                ip_address: _ip
              }
            );
            console.log("err", err);
            await this.adminService.addLogs(logDataArray);
            res.status(500).send({ success: false, message: err.message });
          });
      } else {
        axios
          .put(
            `http://localhost:5014/tasks/delete_snooze_Job`,
            {
              menu_item_id: itemData.menu_item_id,
              snooze_start_cron_id: itemData.snooze_start_cron_id,
              snooze_end_cron_id: itemData.snooze_end_cron_id,
              is_snooze: itemData.is_snooze,
              snooze_type: "item"
            },
            {
              headers: { "Content-Type": "application/json" }
            }
          )
          .then(async (resp: any) => {
            console.log("response", resp.data);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: "Snooze Item Cron Deleted",
                activity_type: "Menu Items",
                activity_name: itemData.item_name,
                status: "Snooze Item Cron Deleted Successfully",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: menu_item.snoozeReason,
                activity_type: "Menu Items",
                activity_name: itemData.item_name,
                status: "Snooze Items",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(200).send({
              success: true,
              successResponse: "Update Item Snooze Successfully"
            });
          })
          .catch(async (err: any) => {
            console.log("err:", err.message);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: err.message,
                activity_type: "Menu Items",
                activity_name: itemData.item_name,
                status: "Snooze Item Cron Not Deleted",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: menu_item.snoozeReason,
                activity_type: "Menu Items",
                activity_name: itemData.item_name,
                status: "Snooze Items",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(500).send({ success: false, message: err.message });
          });
      }
    }
  }
  @Post("admin/update_store_specific_item_price/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async updateStoreSpecificItemPrice(
    @Res() res: Response,
    @Req() request: Request,
    @Param("id") menu_item_id,
    @Body() menu_item: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    try {
      let itemStores: any = [];
      await this.adminService.deleteSpecificStorePrices(menu_item_id, null);
      if (menu_item.storesJson) {
        let storesJson = JSON.parse(menu_item.storesJson);
        storesJson.forEach(item => {
          let specificStorePrice = {
            store_id: item.store_id,
            menu_item_id: menu_item_id,
            mrp: item.mrp
          };
          itemStores.push(specificStorePrice);
        });
      }
      let success: any = await this.adminService.addSpecificStorePrices(
        itemStores
      );
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: "Data not saved"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        res
          .status(200)
          .send({ success: true, successResponse: "Data saved successfully" });
      }
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/update_store_specific_combo_price/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async updateStoreSpecificComboPrice(
    @Res() res: Response,
    @Req() request: Request,
    @Param("id") combo_id,
    @Body() combo: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    try {
      let itemStores: any = [];
      await this.adminService.deleteSpecificStorePrices(null, combo_id);
      if (combo.storesJson) {
        let storesJson = JSON.parse(combo.storesJson);
        storesJson.forEach(item => {
          let specificStorePrice = {
            store_id: item.store_id,
            combo_id: combo_id,
            mrp: item.mrp
          };
          itemStores.push(specificStorePrice);
        });
      }
      let success: any = await this.adminService.addSpecificStorePrices(
        itemStores
      );
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: "Data not saved"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        res
          .status(200)
          .send({ success: true, successResponse: "Data saved successfully" });
      }
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/fpcombos")
  async FPcombosData(): Promise<any> {
    let data = await this.adminService.FpcombosData();
    return { success: true, successResponse: data };
  }

  @Get("admin/fpitems")
  async FPitemsData(): Promise<any> {
    let data = await this.adminService.FpitemsData();
    return { success: true, successResponse: data };
  }

  async makeScheduleDataFormat(): Promise<any> {
    const daysData = await this.adminService.getDays();
    const groupedDaysData = daysData.reduce((acc, day) => {
      acc[day.day] = acc[day.day] || [];
      acc[day.day].push(day);
      return acc;
    }, {});
    const daysTiming: any = [];

    // Iterate over each group of data
    Object.entries(groupedDaysData).forEach(([day, dayData]: any) => {
      // Extract label from the first item in the group (assuming label is the same for all items in the group)
      const label = dayData[0].label;

      // Extract start and close times for the current day
      const timePeriods = dayData.map(dayItem => ({
        open: dayItem.start_time,
        close: dayItem.close_time,
        day_id: dayItem.day_id
      }));

      // Add the day with its timePeriods to the daysTiming array
      daysTiming.push({
        day: day,
        label: label,
        timePeriods: timePeriods
      });
    });

    let comboTimeData = await this.adminService.getCombosTime();
    let itemTimeData = await this.adminService.getItemsTime();

    daysTiming.forEach(dayTiming => {
      // Iterate over each time period within the dayTiming
      dayTiming.timePeriods.forEach(timePeriod => {
        // Filter comboTimeData for the current day_id
        const dayComboData = comboTimeData.filter(
          combo => combo.day_id === timePeriod.day_id
        );

        // Populate combosJson array for the current time period
        timePeriod.combosJson = dayComboData.map(combo => ({
          value: combo.value,
          label: combo.label_combo
        }));
      });
    });

    daysTiming.forEach(dayTiming => {
      // Iterate over each time period within the dayTiming
      dayTiming.timePeriods.forEach(timePeriod => {
        // Filter comboTimeData for the current day_id
        const dayItemData = itemTimeData.filter(
          item => item.day_id === timePeriod.day_id
        );

        // Populate combosJson array for the current time period
        timePeriod.itemsJson = dayItemData.map(item => ({
          value: item.value,
          label: item.label_menu
        }));
      });
    });
    return daysTiming;
  }

  @Get("admin/scheduleData")
  async getScheduleData(): Promise<any> {
    try {
      let data = await this.makeScheduleDataFormat();
      return { success: true, successResponse: data };
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post("admin/fpschedule")
  async FPScheduleData(
    @Req() request: Request,
    @Body() data: any
  ): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let length = data.daysTiming.length;
    try {
      await this.adminService.dataClean();

      for (let i = 0; i < length; i++) {
        let time_period = data.daysTiming[i].timePeriods;
        for (let j = 0; j < time_period.length; j++) {
          let days = await this.adminService.add_day({
            start_time: time_period[j].open,
            close_time: time_period[j].close,
            day: data.daysTiming[i].day,
            label: data.daysTiming[i].label
          });

          for (let k = 0; k < time_period[j].combosJson.length; k++) {
            let combo = time_period[j].combosJson[k];
            await this.adminService.add_combo_time({
              day_id: days.day_id,
              combo_id: combo.value
            });
          }

          for (let k = 0; k < time_period[j].itemsJson.length; k++) {
            let item = time_period[j].itemsJson[k];
            await this.adminService.add_menu_item_time({
              day_id: days.day_id,
              menu_item_id: item.value
            });
          }
        }
      }
      let activitylogData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Fp Schedule Updated",
        activity_type: "FP Schedule Updated",
        activity_name: `FP Schedule Updation`,
        status: "FP Schedule Updated Succesfully",
        ip_address: null
      };
      await this.adminService.addLogs(activitylogData);

      return { success: true, successResponse: data };
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  //Combos Only Routes
  @Get("admin/combos")
  async combosData(): Promise<any> {
    let data = await this.adminService.combosData();
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        let Modes: any = [];
        let specificPrice: any = [];
        let itemModes = await this.adminService.getItemModesByCombo(
          data[i].combo_id
        );
        let comboChoices = await this.adminService.getComboChoicesByComboId(
          data[i].combo_id
        );
        if (itemModes && itemModes.length > 0) {
          itemModes.forEach(element => {
            Modes.push({
              label: element.order_mode_id.name,
              value: element.order_mode_id.id
            });
          });
          data[i].item_mode = JSON.stringify(Modes);
        }
        let specificPrices = await this.adminService.getSpecificStorePricesByCombo(
          data[i].combo_id
        );
        if (specificPrices && specificPrices.length > 0) {
          specificPrices.forEach(element => {
            specificPrice.push({
              store_id: element.store_id.store_id,
              mrp: element.mrp
            });
          });
          data[i].storesJson = JSON.stringify(specificPrice);
        }
        let combo_choices: any = [];
        for (let j = 0; j < comboChoices.length; j++) {
          if (comboChoices[j].mainComboModId.length == 0) {
            combo_choices.push(comboChoices[j]);
          }
        }
        data[i].combo_choices = combo_choices;
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Combos Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/suggestive_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async suggestiveBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() suggestive: any
  ): Promise<any> {
    let suggestiveId = Number(id);
    let success = await this.adminService.suggestiveBlockUnblock(
      suggestiveId,
      suggestive.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: suggestive.user_info.role,
        user_name:
          suggestive.user_info.first_name +
          " " +
          suggestive.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Suggestive Combos",
        // activity_name: combo.combo_name,
        status: suggestive.is_active == 1 ? "UnBlock Combo" : "Block Combo",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Combo Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Combo Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/suggestive/:id")
  async GetSuggestive(@Param("id") id): Promise<any> {
    let suggestive_id = Number(id);
    let data: any = await this.adminService.getSuggestiveById(suggestive_id);
    if (data.itemStores.length > 0) {
      let Array: any = [];
      data.itemStores.forEach(element => {
        let obj = {
          value: element.store_id.store_id,
          label: element.store_id.store_name
        };
        Array.push(obj);
      });
      data.stores_json = JSON.stringify(Array);
    }
    return { success: true, successResponse: data };
  }

  @Get("admin/combo/:id")
  async GetCombo(@Param("id") id): Promise<any> {
    let combo_id = Number(id);
    let data: any = await this.adminService.getComboById(combo_id);
    let topDeals = await this.adminService.getTopDealCount();
    data.topDealsCount = topDeals.count;
    //stores Json to show the selected stores in CMS Edit COMBO
    if (data.itemStores.length > 0) {
      let Array: any = [];
      data.itemStores.forEach(element => {
        let obj = {
          value: element.store_id.store_id,
          label: element.store_id.store_name
        };
        Array.push(obj);
      });
      data.stores_json = JSON.stringify(Array);
    }
    let Modes: any = [];
    let itemModes = await this.adminService.getItemModesByCombo(combo_id);
    if (itemModes && itemModes.length > 0) {
      itemModes.forEach(element => {
        Modes.push({
          label: element.order_mode_id.name,
          value: element.order_mode_id.id
        });
      });
      data.item_mode = JSON.stringify(Modes);
    }
    //ComboChoices Json to show the selected Choices in CMS Edit COMBO
    if (data.mainComboId.length > 0) {
      let Array: any = [];
      for (let index = 0; index < data.mainComboId.length; index++) {
        // let menuItems: any = await this.adminService.getComboChoiceMenuItems(data.mainComboId[index].group_id.group_id);
        // for (let index = 0; index < menuItems.length; index++) {
        //     let variant: any = await this.adminService.getItemsVariations(menuItems[index].value);
        //     menuItems[index].item_size = JSON.stringify(variant.variants)
        // }
        let obj: any = {
          group_name: data.mainComboId[index].group_name,
          priority: data.mainComboId[index].priority,
          size_pos_code: data.mainComboId[index].size_pos_code,
          recipe: data.mainComboId[index].recipe,
          is_half_n_half:
            data.mainComboId[index].is_half_n_half == 1 ? true : false,
          is_full_pizza:
            data.mainComboId[index].is_full_pizza == 1 ? true : false,
          variant_id: data.mainComboId[index].variant_id,
          mod_groups: [],
          crusts: [],
          flavours: [],
          topings: [],
          condiments: []
        };
        for (
          let j = 0;
          j < data.mainComboId[index].mainComboModId.length;
          j++
        ) {
          let objData = {
            mod_group_id:
              data.mainComboId[index].mainComboModId[j].mod_group_id
                .mod_group_id,
            value:
              data.mainComboId[index].mainComboModId[j].mod_group_id
                .mod_group_id,
            label:
              data.mainComboId[index].mainComboModId[j].mod_group_id
                .mod_group_name +
              (data.mainComboId[index].mainComboModId[j].mod_group_id
                .mod_group_name_hint
                ? " - " +
                  data.mainComboId[index].mainComboModId[j].mod_group_id
                    .mod_group_name_hint
                : ""),
            is_crust:
              data.mainComboId[index].mainComboModId[j].mod_group_id.is_crust,
            is_flavour:
              data.mainComboId[index].mainComboModId[j].mod_group_id.is_flavour,
            is_toping:
              data.mainComboId[index].mainComboModId[j].mod_group_id.is_toping,
            is_condiment:
              data.mainComboId[index].mainComboModId[j].mod_group_id
                .is_condiment,
            is_addon:
              data.mainComboId[index].mainComboModId[j].mod_group_id.is_addon
          };
          if (
            data.mainComboId[index].mainComboModId[j].mod_group_id.is_crust == 1
          ) {
            obj.crusts.push(objData);
          } else if (
            data.mainComboId[index].mainComboModId[j].mod_group_id.is_flavour ==
            1
          ) {
            obj.flavours.push(objData);
          } else if (
            data.mainComboId[index].mainComboModId[j].mod_group_id.is_toping ==
            1
          ) {
            obj.topings.push(objData);
          } else if (
            data.mainComboId[index].mainComboModId[j].mod_group_id
              .is_condiment == 1
          ) {
            obj.condiments.push(objData);
          } else if (
            data.mainComboId[index].mainComboModId[j].mod_group_id.is_addon == 1
          ) {
            obj.mod_groups.push(objData);
          }
        }
        Array.push(obj);
      }
      // for (let i = 0; i < Array.length; i++) {
      //     //To add the ItemSize array in comboChoice Array from the menuItems Array to show the list in CMS
      //     for (let j = 0; j < Array[i].choicesItemOptions.length; j++) {
      //         if (Array[i].menu_item_id == Array[i].choicesItemOptions[j].value) {
      //             //because Array[i].choicesItemOptions[j].item_size is for first time sizes
      //             //in CMS and FIrst time sizes can't parse in front end
      //             Array[i].itemsize = JSON.parse(Array[i].choicesItemOptions[j].item_size);
      //         }
      //     }
      // }
      data.comboChoices = JSON.stringify(Array);
    }
    let orderModePrice: any = [];
    let orderModeData = await this.adminService.getOrderModesComboPrice(id);
    orderModeData.forEach(orderModeId => {
      orderModePrice.push({
        combo_id: id,
        order_mode_id: orderModeId.order_mode_id.id,
        mrp: orderModeId.mrp.toString(),
        label: orderModeId.order_mode_id.name,
        value: orderModeId.order_mode_id.id
      });
    });
    data.order_modes_price = orderModePrice;
    delete data.mainComboId;
    return { success: true, successResponse: data };
  }
  ///Get Menu Items by groupId
  @Get("admin/combo_choice_items/:group_id")
  async getComboChoiceMenuItems(@Param("group_id") id): Promise<any> {
    let group_id = Number(id);
    let data: any = await this.adminService.getComboChoiceMenuItems(group_id);
    for (let index = 0; index < data.length; index++) {
      let variant: any = await this.adminService.getItemsVariations(
        data[index].value
      );
      data[index].item_size = JSON.stringify(variant.variants);
    }
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Items Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_combo/:id")
  @UseInterceptors(
    FilesInterceptor("files", 20, {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editCombo(
    @Req() request: Request,
    @UploadedFiles() files,
    @Param("id") id,
    @Body() combo: Combo
  ): Promise<any> {
    let combo_id = Number(id);
    let data: any = combo;
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let userData = JSON.parse(data.user_info);
    delete data.user_info;
    let itemStores: any = [];
    let menuStores: any = [];
    let comboChoicesMod: any = [];
    let modePrices: any = [];
    let itemModes: any = [];
    if (data.combo_start_time == "null" && data.combo_close_time == "null") {
      data.combo_start_time = null;
      data.combo_close_time = null;
    }
    if (!data.serving_hours) {
      data.serving_hours = null;
    }
    if (data.erp_id == "null") {
      data.erp_id = null;
    }
    if (data.pos_code == "null") {
      data.pos_code = null;
    }
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    let modifiers: any = await this.adminService.getAllPublishModifiers();
    let filterModifiers: any = [];
    if (data.is_lsm == 1) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteItemStores(
        null,
        combo_id,
        null,
        null,
        null,
        null
      );
      let obj: any;
      let storesJson = JSON.parse(data.stores_json);
      if (stores.length > 0) {
        for (let i = 0; i < stores.length; i++) {
          let storeGroupExist = await this.adminService.checkComboForStores(
            stores[i].value,
            combo_id
          );
          if (!storeGroupExist) {
            obj = {
              store_id: stores[i].value,
              combo_id: combo_id,
              is_active: 1
            };
            menuStores.push(obj);
          }
        }
      }
      storesJson.forEach(item => {
        // for (let i = 0; i < menuStores.length; i++) {
        //     if (item.value == menuStores[i].store_id) {
        //         menuStores[i].is_active = 1;
        //     }
        // }
        let LSMStores = {
          store_id: item.value,
          combo_id: combo_id
        };
        itemStores.push(LSMStores);
      });
    } else if (data.is_lsm == 2) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteItemStores(
        null,
        combo_id,
        null,
        null,
        null,
        null
      );
      let obj: any;
      let storesJson = JSON.parse(data.stores_json);
      if (stores.length > 0) {
        for (let i = 0; i < stores.length; i++) {
          let storeGroupExist = await this.adminService.checkComboForStores(
            stores[i].value,
            combo_id
          );
          if (!storeGroupExist) {
            obj = {
              store_id: stores[i].value,
              combo_id: combo_id,
              is_active: 1
            };
            menuStores.push(obj);
          }
        }
      }
      storesJson.forEach(item => {
        // for (let i = 0; i < menuStores.length; i++) {
        //     if (item.value == menuStores[i].store_id) {
        //         menuStores[i].is_active = 1;
        //     }
        // }
        let LSMStores = {
          store_id: item.value,
          combo_id: combo_id
        };
        itemStores.push(LSMStores);
      });
    } else {
      let obj: any;
      if (stores.length > 0) {
        stores.forEach(async element => {
          let storeGroupExist = await this.adminService.checkComboForStores(
            element.value,
            combo_id
          );
          if (!storeGroupExist) {
            obj = { store_id: element.value, combo_id: combo_id, is_active: 1 };
            menuStores.push(obj);
          }
        });
      }
      await this.adminService.deleteItemStores(
        null,
        combo_id,
        null,
        null,
        null,
        null
      );
    }
    delete data.stores_json;
    if (data.combo_choices) {
      //if data has comboChoices item
      await this.adminService.deleteComboChoicesModiifersGroups(combo_id);
      await this.adminService.deleteComboChoices(combo_id);
      let choices = JSON.parse(data.combo_choices);
      for (let i = 0; i < choices.length; i++) {
        let mod_groupsJson = choices[i].mod_groups ? choices[i].mod_groups : [];
        let crusts_Json = choices[i].crusts ? choices[i].crusts : [];
        let flavours_Json = choices[i].flavours ? choices[i].flavours : [];
        let topings_Json = choices[i].topings ? choices[i].topings : [];
        let condiments_Json = choices[i].condiments
          ? choices[i].condiments
          : [];
        let itemchoice: any = {
          group_name: choices[i].group_name,
          combo_id: combo_id,
          priority: choices[i].priority,
          size_pos_code: choices[i].size_pos_code,
          recipe: choices[i].recipe
        };
        if (choices[i].is_full_pizza) {
          itemchoice.variant_id = choices[i].variant_id;
          itemchoice.is_full_pizza = 1;
          itemchoice.is_half_n_half = 0;
        } else if (choices[i].is_half_n_half) {
          itemchoice.variant_id = choices[i].variant_id;
          itemchoice.is_full_pizza = 0;
          itemchoice.is_half_n_half = 1;
        }
        let successData = await this.adminService.addComboChoices(itemchoice);
        if (successData) {
          for (let j = 0; j < mod_groupsJson.length; j++) {
            let itemModChoice: any = {
              combo_id: combo_id,
              combo_choice_id: successData.id,
              mod_group_id: mod_groupsJson[j].value
            };
            comboChoicesMod.push(itemModChoice);
          }
          for (let j = 0; j < crusts_Json.length; j++) {
            let itemModChoice: any = {
              combo_id: combo_id,
              combo_choice_id: successData.id,
              mod_group_id: crusts_Json[j].value
            };
            comboChoicesMod.push(itemModChoice);
          }
          for (let j = 0; j < flavours_Json.length; j++) {
            let itemModChoice: any = {
              combo_id: combo_id,
              combo_choice_id: successData.id,
              mod_group_id: flavours_Json[j].value
            };
            comboChoicesMod.push(itemModChoice);
            if (
              successData.is_full_pizza == 1 ||
              successData.is_half_n_half == 1
            ) {
              for (let k = 0; k < modifiers.length; k++) {
                let modGroupsJsondata: any = JSON.parse(
                  modifiers[k].mod_groups_json
                );
                if (
                  modGroupsJsondata.some(
                    el => el.value == flavours_Json[j].value
                  )
                ) {
                  const modObj = await this.adminService.getModifiersByPosCode(
                    choices[i].size_pos_code + "-" + modifiers[k]?.pos_code
                  );
                  if (modObj == undefined || modObj == null) {
                    let modifier: any = {
                      modifier_name:
                        modifiers[k]?.modifier_name + choices[i].size_pos_code,
                      erp_id:
                        choices[i].size_pos_code + "-" + modifiers[k]?.pos_code,
                      pos_code:
                        choices[i].size_pos_code + "-" + modifiers[k]?.pos_code,
                      isFoodPanda: 1
                    };
                    filterModifiers.push(modifier);
                  }
                }
              }
            }
          }
          for (let j = 0; j < topings_Json.length; j++) {
            let itemModChoice: any = {
              combo_id: combo_id,
              combo_choice_id: successData.id,
              mod_group_id: topings_Json[j].value
            };
            comboChoicesMod.push(itemModChoice);
          }
          for (let j = 0; j < condiments_Json.length; j++) {
            let itemModChoice: any = {
              combo_id: combo_id,
              combo_choice_id: successData.id,
              mod_group_id: condiments_Json[j].value
            };
            comboChoicesMod.push(itemModChoice);
          }
          if (filterModifiers.length > 0) {
            await this.adminService.addModifier(filterModifiers);
          }
          await this.adminService.addComboChoicesModifiers(comboChoicesMod);
        }
      }
    }
    delete data.combo_choices;
    if (data.item_mode) {
      //if data has comboChoices item
      await this.adminService.deleteItemModes(null, combo_id, null, null);
      let Modes = JSON.parse(data.item_mode);
      Modes.forEach(mode => {
        let itemmode = {
          order_mode_id: mode.value,
          combo_id: combo_id
        };
        itemModes.push(itemmode);
      });
    }
    delete data.item_mode;
    if (data.comboModePrice) {
      //if data has comboChoices item
      await this.adminService.deleteModePriceForCombo(combo_id);
      let ModePrices = JSON.parse(data.comboModePrice);
      ModePrices.forEach(item => {
        let modePrice = {
          order_mode_id: item.order_mode_id,
          combo_id: combo_id,
          mrp: item.mrp
        };
        modePrices.push(modePrice);
      });
    }
    delete data.comboModePrice;
    let is_foodpanda = data.is_foodpanda;
    let success = await this.adminService.editCombo(combo_id, data);
    if (success.affected > 0) {
      if (is_foodpanda == 0) {
        //delete from combo_timing
        await this.adminService.removeComboTime(combo_id);
      }
      let data: any = await this.adminService.getComboById(combo_id);
      if (files.length > 0) {
        // let response: any = await this.adminService.deleteComboHeroImages(combo_id);
        let response: any = await this.adminService.deleteComboImages(combo_id);
        if (response.affected > 0) {
          data.images.forEach(element => {
            if (
              element.image_url !== "" &&
              fs.existsSync(`Uploads/images/${element.image_url}`)
            ) {
              fs.unlinkSync(`Uploads/images/${element.image_url}`);
            }
          });
        }
        // if (response.affected > 0) {
        //     data.images.forEach(element => {
        //         if (element.thumbnail_url !== "" && fs.existsSync(`Uploads/images/${element.thumbnail_url}`)) {
        //             fs.unlinkSync(`Uploads/images/${element.thumbnail_url}`);
        //         }
        //     });
        // }
      }
      let item_img: any = [];
      // let comboImageobj: any = {
      //     combo_id: null,
      //     image_url: "",
      //     image_size: null,
      //     thumbnail_url:""
      // };
      // files.forEach(file => {
      //     if (file.filename.includes('_combo_')) {
      //         comboImageobj.combo_id = combo_id;
      //         comboImageobj.image_url = file.filename;
      //         comboImageobj.image_size= file.size;

      //     }
      //     if (file.filename.includes('_thumbnail_')) {
      //         comboImageobj.combo_id = combo_id;
      //         comboImageobj.thumbnail_url = file.filename;
      //         comboImageobj.image_size= file.size;
      //     }
      //     if (comboImageobj.combo_id ) {
      //         item_img.push(comboImageobj)
      //     }
      // });
      files.forEach(file => {
        let fileReponse = {
          combo_id: combo_id,
          image_url: file.filename,
          image_size: file.size
        };
        if (
          !file.filename.includes("_hero_image") &&
          !file.filename.includes("_hero_mobile_image")
        ) {
          item_img.push(fileReponse);
        }
      });
      await this.adminService.addLSMitems(itemStores);
      await this.adminService.addStoresForMenu(menuStores);
      await this.adminService.addOrderModePrices(modePrices);
      await this.adminService.addOrderItemMode(itemModes);
      await this.adminService.addItemImage(item_img);
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Combos",
        activity_name: data.combo_name,
        status: "Update Combo",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Combo Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Combo Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/update_combo_snooze/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async updateComboSnooze(
    @Res() res: Response,
    @Req() request: Request,
    @Param("id") combo_id,
    @Body() combo: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let comboData: any = await this.adminService.getComboById(combo_id);
    let data: any = {
      combo_id: combo_id,
      is_snooze: "0"
    };
    let success: any = null;
    if (combo.snoozeReason !== "") {
      data.snooze_start_time = combo.snooze_start_time;
      data.snooze_end_time = combo.snooze_end_time;
      //Snooze Start time in utc
      // .subtract(5, 'h')
      let snooze_start_utc = moment(data.snooze_start_time).subtract(5, "h");
      console.log(snooze_start_utc);
      let snoozeStarthours = moment(snooze_start_utc)
        .utc(true)
        .hours();
      let snoozeStartminutes = moment(snooze_start_utc)
        .utc(true)
        .minutes();
      console.log(snoozeStarthours);
      console.log(snoozeStartminutes);
      let snoozeStartdate = snooze_start_utc.toISOString().split("T")[0];
      const snoozeStartsplitDate: any = snoozeStartdate.split("-");
      const snoozeStartdayOfMonth = snoozeStartsplitDate[2];
      const snoozeStartmonth = snoozeStartsplitDate[1] - 1; //because cron month index starts from 0
      //Snooze End time in utc
      // .subtract(5, 'h')
      let snooze_end_utc = moment(data.snooze_end_time).subtract(5, "h");
      console.log(snooze_end_utc);
      let snoozeEndhours = moment(snooze_end_utc)
        .utc(true)
        .hours();
      let snoozeEndminutes = moment(snooze_end_utc)
        .utc(true)
        .minutes();
      console.log(snoozeEndhours);
      console.log(snoozeEndminutes);
      let snoozeEnddate = snooze_end_utc.toISOString().split("T")[0];
      const snoozeEndsplitDate: any = snoozeEnddate.split("-");
      const snoozeEnddayOfMonth = snoozeEndsplitDate[2];
      const snoozeEndmonth = snoozeEndsplitDate[1] - 1; //because cron month index starts from 0
      let SnoozeStartCron: any = "";
      let SnoozeEndCron: any = "";
      // Generate a v1 (time-based) id
      const SnoozeStartCronname = uuid
        .v1()
        .substr(0, 20)
        .replace(/-/g, "");
      SnoozeStartCron = `${snoozeStartminutes} ${snoozeStarthours} ${snoozeStartdayOfMonth} ${snoozeStartmonth} *`;
      data.snooze_start_cron_id = SnoozeStartCronname;
      data.snooze_start_cron_job = SnoozeStartCron;
      data.snooze_start_cron_job_date =
        snoozeStartdate +
        " " +
        snoozeStarthours +
        ":" +
        snoozeStartminutes +
        ":00";
      const SnoozeEndCronname = uuid
        .v1()
        .substr(0, 20)
        .replace(/-/g, "");
      SnoozeEndCron = `${snoozeEndminutes} ${snoozeEndhours} ${snoozeEnddayOfMonth} ${snoozeEndmonth} *`;
      data.snooze_end_cron_id = SnoozeEndCronname;
      data.snooze_end_cron_job = SnoozeEndCron;
      data.snooze_end_cron_job_date =
        snoozeEnddate + " " + snoozeEndhours + ":" + snoozeEndminutes + ":00";
      success = await this.adminService.updateComboSnooze(data);
    } else {
      data.snooze_start_time = null;
      data.snooze_end_time = null;
      data.snooze_start_cron_id = null;
      data.snooze_start_cron_job = null;
      data.snooze_start_cron_job_date = null;
      data.snooze_end_cron_id = null;
      data.snooze_end_cron_job = null;
      data.snooze_end_cron_job_date = null;
      success = await this.adminService.updateComboSnooze(data);
    }
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Snooze date not saved"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logDataArray: any = [];
      if (combo.snoozeReason !== "") {
        axios
          .post(
            `http://localhost:5014/tasks/snooze_Job`,
            {
              combo_id: comboData.combo_id,
              SnoozeStartCronname: data.snooze_start_cron_id,
              SnoozeEndCronname: data.snooze_end_cron_id,
              snoozeStartCron: data.snooze_start_cron_job,
              snoozeEndCron: data.snooze_end_cron_job,
              snooze_type: "combo"
            },
            {
              headers: { "Content-Type": "application/json" }
            }
          )
          .then(async (resp: any) => {
            console.log("response", resp.data);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: "Snooze Combo Cron Created",
                activity_type: "Combos",
                activity_name: comboData.combo_name,
                status: "Snooze Combo Cron Creation Successfully",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: combo.snoozeReason,
                activity_type: "Combos",
                activity_name: comboData.combo_name,
                status: "Snooze Combos",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(200).send({
              success: true,
              successResponse: "Update Combo Snooze Successfully"
            });
          })
          .catch(async (err: any) => {
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: err.message,
                activity_type: "Combos",
                activity_name: comboData.combo_name,
                status: "Snooze Combo Cron not saved",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: combo.snoozeReason,
                activity_type: "Combos",
                activity_name: comboData.combo_name,
                status: "Snooze Combos",
                ip_address: _ip
              }
            );
            console.log("err", err);
            await this.adminService.addLogs(logDataArray);
            res.status(500).send({ success: false, message: err.message });
          });
      } else {
        axios
          .put(
            `http://localhost:5014/tasks/delete_snooze_Job`,
            {
              combo_id: comboData.combo_id,
              snooze_start_cron_id: comboData.snooze_start_cron_id,
              snooze_end_cron_id: comboData.snooze_end_cron_id,
              is_snooze: comboData.is_snooze,
              snooze_type: "combo"
            },
            {
              headers: { "Content-Type": "application/json" }
            }
          )
          .then(async (resp: any) => {
            console.log("response", resp.data);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: "Snooze Combo Cron Deleted",
                activity_type: "Combos",
                activity_name: comboData.combo_name,
                status: "Snooze Combo Cron Deleted Successfully",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: combo.snoozeReason,
                activity_type: "Combos",
                activity_name: comboData.combo_name,
                status: "Snooze Combos",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(200).send({
              success: true,
              successResponse: "Update Combo Snooze Successfully"
            });
          })
          .catch(async (err: any) => {
            console.log("err:", err.message);
            logDataArray.push(
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: err.message,
                activity_type: "Combos",
                activity_name: comboData.combo_name,
                status: "Snooze Combo Cron Not Deleted",
                ip_address: _ip
              },
              {
                role: jwtData.role,
                user_name: jwtData.first_name + " " + jwtData.last_name,
                reason: combo.snoozeReason,
                activity_type: "Combos",
                activity_name: comboData.combo_name,
                status: "Snooze Combos",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(500).send({ success: false, message: err.message });
          });
      }
    }
  }
  @Put("admin/combo_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async comboBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() combo: any
  ): Promise<any> {
    let comboId = Number(id);
    let success = await this.adminService.comboBlockUnblock(
      comboId,
      combo.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: combo.user_info.role,
        user_name: combo.user_info.first_name + " " + combo.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Combos",
        activity_name: combo.combo_name,
        status: combo.is_active == 1 ? "UnBlock Combo" : "Block Combo",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Combo Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Combo Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/add_combo")
  @UseInterceptors(
    FilesInterceptor("files", 20, {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addCombo(
    @Req() request: Request,
    @UploadedFiles() files,
    @Body() combo: Combo
  ): Promise<any> {
    if (files.length > 0) {
      let menuStores: any = [];
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let token: any = request.headers.authorization?.split("Bearer ")[1];
      let jwtData: any = jwtDecode(token);
      let data: any = combo;

      if (!data.serving_hours) {
        data.serving_hours = null;
      }
      let modePrices: any = [];
      let itemModes: any = [];
      let Modes: any = data.item_mode;
      delete data.item_mode;
      let success = await this.adminService.addCombo(data);
      let stores = await this.adminService.storesListForMenu(
        jwtData.user_group_id
      );
      let modifiers: any = await this.adminService.getAllPublishModifiers();
      let filterModifiers: any = [];
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: "Combo Not Inserted"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        let item_img: any = [];
        let itemStores: any = [];
        let comboChoicesMod: any = [];
        files.forEach(file => {
          let fileReponse = {
            combo_id: success.combo_id,
            image_url: file.filename,
            image_size: file.size
          };
          item_img.push(fileReponse);
        });
        // let comboImageobj: any = {
        //     combo_id: null,
        //     image_url: "",
        //     image_size: null,
        //     thumbnail_url:""
        // };
        // files.forEach(file => {
        //     if (file.filename.includes('_combo_')) {
        //         comboImageobj.combo_id = success.combo_id;
        //         comboImageobj.image_url = file.filename;
        //         comboImageobj.image_size= file.size;

        //     }
        //     if (file.filename.includes('_thumbnail_')) {
        //         comboImageobj.combo_id = success.combo_id;
        //         comboImageobj.thumbnail_url = file.filename;
        //         comboImageobj.image_size= file.size;
        //     }
        //     if (comboImageobj.combo_id) {
        //         item_img.push(comboImageobj)
        //     }
        // });
        if (data.is_lsm == 1) {
          //if item is lsm item so then stores will insert in ItemStoreRelationTable
          let obj: any;
          let storesJson = JSON.parse(data.stores_json);
          if (stores.length > 0) {
            for (let i = 0; i < stores.length; i++) {
              let storeGroupExist = await this.adminService.checkComboForStores(
                stores[i].value,
                success.combo_id
              );
              if (!storeGroupExist) {
                obj = {
                  store_id: stores[i].value,
                  combo_id: success.combo_id,
                  is_active: 1
                };
                menuStores.push(obj);
              }
            }
          }
          storesJson.forEach(item => {
            // for (let i = 0; i < menuStores.length; i++) {
            //     if (item.value == menuStores[i].store_id) {
            //         menuStores[i].is_active = 1;
            //     }
            // }
            let LSMStores = {
              store_id: item.value,
              combo_id: success.combo_id
            };
            itemStores.push(LSMStores);
          });
          await this.adminService.addLSMitems(itemStores);
        } else if (data.is_lsm == 2) {
          let obj: any;
          let storesJson = JSON.parse(data.stores_json);
          if (stores.length > 0) {
            for (let i = 0; i < stores.length; i++) {
              let storeGroupExist = await this.adminService.checkComboForStores(
                stores[i].value,
                success.combo_id
              );
              if (!storeGroupExist) {
                obj = {
                  store_id: stores[i].value,
                  combo_id: success.combo_id,
                  is_active: 1
                };
                menuStores.push(obj);
              }
            }
          }
          storesJson.forEach(item => {
            // for (let i = 0; i < menuStores.length; i++) {
            //     if (item.value == menuStores[i].store_id) {
            //         menuStores[i].is_active = 1;
            //     }
            // }
            let LSMStores = {
              store_id: item.value,
              combo_id: success.combo_id
            };
            itemStores.push(LSMStores);
          });
          await this.adminService.addLSMitems(itemStores);
        } else {
          let obj: any;
          for (let i = 0; i < stores.length; i++) {
            let storeGroupExist = await this.adminService.checkComboForStores(
              stores[i].value,
              success.combo_id
            );
            if (!storeGroupExist) {
              obj = {
                store_id: stores[i].value,
                combo_id: success.combo_id,
                is_active: 1
              };
              menuStores.push(obj);
            }
          }
        }
        if (data.combo_choices) {
          //if item is lsm item so then stores will insert in ItemStoreRelationTable
          let choices = JSON.parse(data.combo_choices);
          for (let i = 0; i < choices.length; i++) {
            let mod_groupsJson = choices[i].mod_groups
              ? choices[i].mod_groups
              : [];
            let crusts_Json = choices[i].crusts ? choices[i].crusts : [];
            let flavours_Json = choices[i].flavours ? choices[i].flavours : [];
            let topings_Json = choices[i].topings ? choices[i].topings : [];
            let itemchoice: any = {
              group_name: choices[i].group_name,
              combo_id: success.combo_id,
              priority: choices[i].priority,
              size_pos_code: choices[i].size_pos_code,
              recipe: choices[i].recipe
            };
            if (choices[i].is_full_pizza) {
              itemchoice.variant_id = choices[i].variant_id;
              itemchoice.is_full_pizza = 1;
              itemchoice.is_half_n_half = 0;
            } else if (choices[i].is_half_n_half) {
              itemchoice.variant_id = choices[i].variant_id;
              itemchoice.is_full_pizza = 0;
              itemchoice.is_half_n_half = 1;
            }
            let successData = await this.adminService.addComboChoices(
              itemchoice
            );
            if (successData) {
              for (let j = 0; j < mod_groupsJson.length; j++) {
                let itemModChoice: any = {
                  combo_id: success.combo_id,
                  combo_choice_id: successData.id,
                  mod_group_id: mod_groupsJson[j].value
                };
                comboChoicesMod.push(itemModChoice);
              }
              for (let j = 0; j < crusts_Json.length; j++) {
                let itemModChoice: any = {
                  combo_id: success.combo_id,
                  combo_choice_id: successData.id,
                  mod_group_id: crusts_Json[j].value
                };
                comboChoicesMod.push(itemModChoice);
              }
              for (let j = 0; j < flavours_Json.length; j++) {
                let itemModChoice: any = {
                  combo_id: success.combo_id,
                  combo_choice_id: successData.id,
                  mod_group_id: flavours_Json[j].value
                };
                comboChoicesMod.push(itemModChoice);
                if (
                  successData.is_full_pizza == 1 ||
                  successData.is_half_n_half == 1
                ) {
                  for (let k = 0; k < modifiers.length; k++) {
                    let modGroupsJsondata: any = JSON.parse(
                      modifiers[k].mod_groups_json
                    );
                    if (
                      modGroupsJsondata.some(
                        el => el.value == flavours_Json[j].value
                      )
                    ) {
                      const modObj = await this.adminService.getModifiersByPosCode(
                        choices[i].size_pos_code + "-" + modifiers[k]?.pos_code
                      );
                      if (modObj == undefined || modObj == null) {
                        let modifier: any = {
                          modifier_name:
                            modifiers[k]?.modifier_name +
                            choices[i].size_pos_code,
                          erp_id:
                            choices[i].size_pos_code +
                            "-" +
                            modifiers[k]?.pos_code,
                          pos_code:
                            choices[i].size_pos_code +
                            "-" +
                            modifiers[k]?.pos_code,
                          isFoodPanda: 1
                        };
                        filterModifiers.push(modifier);
                      }
                    }
                  }
                }
              }
              for (let j = 0; j < topings_Json.length; j++) {
                let itemModChoice: any = {
                  combo_id: success.combo_id,
                  combo_choice_id: successData.id,
                  mod_group_id: topings_Json[j].value
                };
                comboChoicesMod.push(itemModChoice);
              }
              if (filterModifiers.length > 0) {
                await this.adminService.addModifier(filterModifiers);
              }
              await this.adminService.addComboChoicesModifiers(comboChoicesMod);
            }
          }
        }
        if (data.comboModePrice) {
          //if data has comboChoices item
          let ModePrices = JSON.parse(data.comboModePrice);
          ModePrices.forEach(item => {
            let modePrice = {
              order_mode_id: item.order_mode_id,
              combo_id: success.combo_id,
              mrp: item.mrp
            };
            modePrices.push(modePrice);
          });
        }
        if (Modes) {
          //if data has comboChoices item
          let parseModes = JSON.parse(Modes);
          parseModes.forEach(mode => {
            let itemmode = {
              order_mode_id: mode.value,
              combo_id: success.combo_id
            };
            itemModes.push(itemmode);
          });
        }
        await this.adminService.addStoresForMenu(menuStores);
        await this.adminService.addOrderModePrices(modePrices);
        await this.adminService.addOrderItemMode(itemModes);
        await this.adminService.addItemImage(item_img);
        let logData: any = {
          role: jwtData.role,
          user_name: jwtData.first_name + " " + jwtData.last_name,
          reason: "New Combo",
          activity_type: "Combos",
          activity_name: data.combo_name,
          status: "Add Combo",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return {
          success: true,
          successResponse: "Combo Inserted Successfully"
        };
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "No Image Selected"
        },
        HttpStatus.NOT_ACCEPTABLE
      );
    }
  }
  // Attach SuggestiveCombos
  @Get("admin/getSuggCombos")
  async getSuggCombos(): Promise<any> {
    let CombosData = await this.adminService.getSuggCombos();
    return { success: true, successResponse: CombosData };
  }
  @Post("admin/attach_suggestive_deals")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async attach_suggestive_deals(
    @Req() request: Request,
    @Body() body: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data: any = [];
    let itemStores: any = [];
    let suggestiveCombos = JSON.parse(body.selectedSuggComboJson);
    let comboExistForStore = await this.adminService.checkSuggestiveProductsForComboandStore(
      body.store_id,
      body.combo_id
    );
    if (comboExistForStore) {
      let success: any = await this.adminService.deleteSuggestiveChildProd(
        comboExistForStore.id
      );
      if (success.affected > 0) {
        await this.adminService.deleteSuggestiveProd(comboExistForStore.id);
      }
    }
    let success = await this.adminService.addSuggestiveDeals(body);
    if (success) {
      suggestiveCombos.forEach(obj => {
        let combo: any = {
          sugg_base_product_id: success.id,
          suggestive_combo_id: obj.value
        };
        data.push(combo);
      });
      await this.adminService.addSuggestiveChildDeals(data);
      if (data.is_lsm == 1 || data.is_lsm == 2) {
        //if item is lsm item so then stores will insert in ItemStoreRelationTable
        let storesJson = JSON.parse(data.stores_json);
        storesJson.forEach(item => {
          let LSMStores = {
            store_id: item.value,
            sugg_base_prod_id: success.id
          };
          itemStores.push(LSMStores);
        });
        await this.adminService.addLSMitems(itemStores);
      }
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Attach Suggestive Deals",
        activity_type: "Suggestive Deals",
        activity_name: `Combo (${body.combo_id})`,
        status: "Attach Suggestive Deals",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Data saved successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Data not saved"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Put("admin/edit_suggestive_deals/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async edit_suggestive_deals(
    @Req() request: Request,
    @Param("id") id,
    @Body() body: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data: any = [];
    let itemStores: any = [];
    let suggestive_id = Number(id);

    let suggestiveCombos = JSON.parse(body.selectedSuggComboJson);
    console.log(body, "Bodyyyyy======>");

    await this.adminService.deleteSuggestiveChildProd(suggestive_id);

    if (body.is_lsm == 1) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteItemStores(
        null,
        null,
        null,
        null,
        null,
        suggestive_id
      );
      let storesJson = JSON.parse(body.stores_json);
      storesJson.forEach(item => {
        let LSMStores = {
          store_id: item.value,
          sugg_base_prod_id: suggestive_id
        };
        itemStores.push(LSMStores);
      });
    } else if (body.is_lsm == 2) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteItemStores(
        null,
        null,
        null,
        null,
        null,
        suggestive_id
      );
      let storesJson = JSON.parse(body.stores_json);
      storesJson.forEach(item => {
        let LSMStores = {
          store_id: item.value,
          sugg_base_prod_id: suggestive_id
        };
        itemStores.push(LSMStores);
      });
    } else {
      await this.adminService.deleteItemStores(
        null,
        null,
        null,
        null,
        null,
        suggestive_id
      );
    }
    delete body.stores_json;
    delete body.selectedSuggComboJson;

    let success = await this.adminService.editSuggestive(suggestive_id, body);

    console.log(success, "Succeesssss===========================>");

    if (success) {
      suggestiveCombos.forEach((obj: any) => {
        let combo: any = {
          sugg_base_product_id: success.id,
          suggestive_combo_id: obj.value
        };
        data.push(combo);
      });
      await this.adminService.addSuggestiveChildDeals(data);
      await this.adminService.addLSMitems(itemStores);

      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Edit Suggestive Deals",
        activity_type: "Suggestive Deals",
        activity_name: `Combo (${body.combo_id})`,
        status: "Edit Suggestive Deals",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Data update successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Data not updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/get_suggestive_deals")
  async getSuggDeals(): Promise<any> {
    let data = await this.adminService.getSuggestiveCombos();
    if (data.length > 0) {
      data.forEach((obj: any) => {
        obj.combo_name = obj.combo_id.combo_name;
        obj.suggestive_child_products.forEach((childObj: any) => {
          childObj.combo_id = childObj.suggestive_combo_id.combo_id;
          childObj.combo_name = childObj.suggestive_combo_id.combo_name;
        });
      });
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Suggestive Combos Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Modifiers Routes Only
  @Get("admin/mod_groups")
  async modGroupData(): Promise<any> {
    let data = await this.adminService.modGroupData();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Mod Groups Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/mod_group/:id")
  async GetModGroup(@Param("id") id): Promise<any> {
    let mod_group_id = Number(id);
    let data = await this.adminService.GetModGroupById(mod_group_id);
    return { success: true, successResponse: data };
  }
  @Put("admin/edit_mod_group/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editModGroup(
    @Req() request: Request,
    @Param("id") id,
    @Body() modGroup: any
  ): Promise<any> {
    let mod_group_id = Number(id);
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = modGroup.user_info;
    delete modGroup.user_info;
    let success = await this.adminService.editModGroup(mod_group_id, modGroup);
    if (success.affected > 0) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Mod Groups",
        activity_name: modGroup.mod_group_name,
        status: "Update Mod Group",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "ModifierGroup Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "ModifierGroup Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/add_mod_group")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addModGroup(@Req() request: Request, @Body() group: any): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = group.user_info;
    delete group.user_info;
    let success = await this.adminService.addModGroup(group);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Mod Groups Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "New Mod Group",
        activity_type: "Mod Groups",
        activity_name: group.mod_group_name,
        status: "Add Mod Group",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Mod Groups Inserted Successfully"
      };
    }
  }
  @Put("admin/mod_group_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async ModGroupsBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() modGroup: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let modGroupId = Number(id);
    let success = await this.adminService.modGroupsBlockUnblock(
      modGroupId,
      modGroup.is_active
    );
    if (success.affected > 0) {
      let logData: any = {
        role: modGroup.user_info.role,
        user_name: modGroup.user_info.first_name + modGroup.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Mod Groups",
        activity_name: modGroup.mod_group_name,
        status:
          modGroup.is_active == 1 ? "UnBlock Mod Group" : "Block Mod Group",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Brand Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Brand Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/modifiers/:mod_group_id")
  async GetModifiersByModGroupId(@Param("mod_group_id") id): Promise<any> {
    let mod_group_id = Number(id);
    let data = await this.adminService.GetModifiersByModGroupId(mod_group_id);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Modifiers Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/modifiers")
  async modifierData(): Promise<any> {
    let data = await this.adminService.modifierData();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Modifier Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/modifier_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async ModifierBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() mod: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let modId = Number(id);
    let success = await this.adminService.modifierBlockUnblock(
      modId,
      mod.is_active
    );
    if (success.affected > 0) {
      let logData: any = {
        role: mod.user_info.role,
        user_name: mod.user_info.first_name + mod.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Modifiers Options",
        activity_name: mod.modifier_name,
        status: mod.is_active == 1 ? "UnBlock Modifier" : "Block Modifier",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Brand Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Modifiers Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/fpModifiers")
  async fpModifierData(): Promise<any> {
    let data = await this.adminService.fpModifierData();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Modifier Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/modifier/:id")
  async GetModifier(@Param("id") id): Promise<any> {
    let modifier_id = Number(id);
    let data: any = await this.adminService.GetModifierById(modifier_id);
    let variants: any = await this.adminService.getModVariantByModId(id);
    if (variants && variants.length > 0) {
      let Array: any = [];
      variants.forEach(element => {
        let obj = { value: element.variant_id, label: element.variant_name };
        Array.push(obj);
      });
      data.variants = JSON.stringify(Array);
    }
    return { success: true, successResponse: data };
  }
  @Put("admin/edit_modifier/:id")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editModifier(
    @Req() request: Request,
    @UploadedFile() file,
    @Param("id") id,
    @Body() mod: ModifierOption
  ): Promise<any> {
    let modifier_id = Number(id);
    var _ip: any;
    let menuStores: any = [];
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let data: any = mod;
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    if (data.erp_id == "null") {
      data.erp_id = null;
    }
    if (data.pos_code == "null") {
      data.pos_code = null;
    }
    let variants: any;
    if (data.for_specific_variations == 1) {
      variants = JSON.parse(data.variants);
      delete data.variants;
    }
    let success = await this.adminService.editModifier(modifier_id, data);
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    let obj: any;
    if (stores.length > 0) {
      for (let i = 0; i < stores.length; i++) {
        let storeModExist = await this.adminService.checkModifierForStores(
          stores[i].value,
          modifier_id
        );
        if (!storeModExist) {
          obj = {
            store_id: stores[i].value,
            modifier_id: modifier_id,
            is_active: 1
          };
          menuStores.push(obj);
        }
      }
    }
    if (success.affected > 0) {
      await this.adminService.addStoresForMenu(menuStores);
      if (file) {
        await this.adminService.deleteModifierImages(modifier_id);
        let fileReponse = {
          modifier_id: modifier_id,
          image_url: file.filename,
          image_size: file.size
        };
        await this.adminService.addItemImage(fileReponse);
      }
      await this.adminService.deleteVarRel(modifier_id, null);
      if (data.for_specific_variations == "1") {
        let mod_var_data: any = [];
        variants.forEach(element => {
          let obj = {
            modifier_id: modifier_id,
            // group_id: element.group_id,
            // menu_item_id: element.menu_item_id,
            variant_id: element.value
          };
          mod_var_data.push(obj);
        });
        await this.adminService.addVariatnsRelations(mod_var_data);
      }
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Edit Info",
        activity_type: "Modifiers",
        activity_name: data.modifier_name,
        status: "Update Modifier",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Modifier Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Modifier Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/add_modifier")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addModifier(
    @Req() request: Request,
    @UploadedFile() file,
    @Body() modifier: ModifierOption
  ): Promise<any> {
    // if (files.length > 0) {
    let menuStores: any = [];
    let data: any = modifier;
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let success = await this.adminService.addModifier(data);
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Modifiers Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let fileReponse = {
        modifier_id: success.modifier_id,
        image_url: file.filename,
        image_size: file.size
      };
      await this.adminService.addItemImage(fileReponse);
      if (data.for_specific_variations == "1") {
        let mod_var_data: any = [];
        let variants: any = JSON.parse(data.variants);
        variants.forEach(element => {
          let obj = {
            modifier_id: success.modifier_id,
            // group_id: element.group_id,
            // menu_item_id: element.menu_item_id,
            variant_id: element.value
          };
          mod_var_data.push(obj);
        });
        await this.adminService.addVariatnsRelations(mod_var_data);
      }
      let obj: any;
      if (stores.length > 0) {
        for (let i = 0; i < stores.length; i++) {
          let storeModExist = await this.adminService.checkModifierForStores(
            stores[i].value,
            success.modifier_id
          );
          if (!storeModExist) {
            obj = {
              store_id: stores[i].value,
              modifier_id: success.modifier_id,
              is_active: 1
            };
            menuStores.push(obj);
          }
        }
        await this.adminService.addStoresForMenu(menuStores);
      }
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "New Modifier",
        activity_type: "Modifiers",
        activity_name: data.modifier_name,
        status: "Add Modifier",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Modifier Inserted Successfully"
      };
    }
    // } else {
    //     throw new HttpException({
    //         success: false,
    //         message: 'No Image Selected',
    //     }, HttpStatus.NOT_ACCEPTABLE);
    // }
  }
  @Get("admin/activeModGroups")
  async GetModGroups(): Promise<any> {
    let data = await this.adminService.GetModGroups();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "ModifierGroups Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/del_modifier/:id")
  async deleteModifier(
    @Req() request: Request,
    @Param("id") id,
    @Body() mod: any
  ): Promise<any> {
    let modifier_id = Number(id);
    try {
      await this.adminService.deleteStoreMenuModifier(modifier_id);
      await this.adminService.deleteVarRel(modifier_id, null);
      let success = await this.adminService.deleteModifier(modifier_id);
      if (success) {
        var _ip: any;
        _ip =
          request.headers["x-forwarded-for"] ||
          request.connection.remoteAddress;
        if (_ip.substr(0, 7) == "::ffff:") {
          _ip = _ip.substr(7);
        }
        let logData: any = {
          role: mod.user_info.role,
          user_name: mod.user_info.first_name + " " + mod.user_info.last_name,
          reason: "Delete",
          activity_type: "Modifiers",
          activity_name: mod.modifier_name,
          status: "Delete Modifier",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return {
          success: true,
          successResponse: "Modifier Deleted Successfully"
        };
      } else {
        throw new HttpException(
          {
            success: false,
            message: "Modifier Not Deleted"
          },
          HttpStatus.FORBIDDEN
        );
      }
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Banners routes
  @Get("admin/banners")
  async bannersData(): Promise<any> {
    let data = await this.adminService.bannerData();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Banners Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/banner/:id")
  async GetBanner(@Param("id") id): Promise<any> {
    let banner_id = Number(id);
    let data = await this.adminService.getBannerById(banner_id);
    return { success: true, successResponse: data };
  }
  @Post("admin/add_banner")
  @UseInterceptors(
    FilesInterceptor("files", 20, {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addBanner(
    @Req() request: Request,
    @UploadedFiles() files,
    @Body() banner: Banners
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let itemStores: any = [];
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    if (files.length > 0) {
      let data: any = banner;
      files.forEach(file => {
        if (file.filename.includes("_desktop_image")) {
          data.desktop_image = file.filename;
        }
        if (file.filename.includes("_mobile_image")) {
          data.mobile_image = file.filename;
        }
      });
      let success = await this.adminService.addBanner(data);
      if (data.is_lsm == 1) {
        //if item is lsm item so then stores will insert in ItemStoreRelationTable
        let storesJson = JSON.parse(data.stores_json);
        if (stores.length > 0) {
          storesJson.forEach(item => {
            let LSMStores = {
              store_id: item.value,
              banner_id: success.id
            };
            itemStores.push(LSMStores);
          });
          await this.adminService.addLSMitems(itemStores);
        }
      } else if (data.is_lsm == 2) {
        //if item is lsm item so then stores will insert in ItemStoreRelationTable
        let storesJson = JSON.parse(data.stores_json);
        if (stores.length > 0) {
          storesJson.forEach(item => {
            let LSMStores = {
              store_id: item.value,
              banner_id: success.id
            };
            itemStores.push(LSMStores);
          });
          await this.adminService.addLSMitems(itemStores);
        }
      }
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: "Banner Not Inserted"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        let logData: any = {
          role: jwtData.role,
          user_name: jwtData.first_name + " " + jwtData.last_name,
          reason: "New Banner",
          activity_type: "Banners",
          activity_name: success.id,
          status: "Add Banners",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return {
          success: true,
          successResponse: "Banner Inserted Successfully"
        };
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "No Image Selected"
        },
        HttpStatus.NOT_ACCEPTABLE
      );
    }
  }
  @Put("admin/banner_block_unblock/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async bannerBlockUnblock(
    @Req() request: Request,
    @Param("id") id,
    @Body() banner: any
  ): Promise<any> {
    let bannerId = Number(id);
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let success = await this.adminService.bannerBlockUnblock(
      bannerId,
      banner.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Block/UnBlock",
        activity_type: "Banners",
        activity_name: bannerId,
        status: banner.is_active == 1 ? "UnBlock Banner" : "Block Banner",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Banner Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Banner Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_banner/:id")
  @UseInterceptors(
    FilesInterceptor("files", 20, {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editBanner(
    @Req() request: Request,
    @UploadedFiles() files,
    @Param("id") id,
    @Body() banner: Banners
  ): Promise<any> {
    let banner_id = Number(id);
    let data: any = banner;
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let bannerData = await this.adminService.getBannerById(banner_id);
    let itemStores: any = [];
    let stores = await this.adminService.storesListForMenu(
      jwtData.user_group_id
    );
    if (files.length > 0) {
      files.forEach(async file => {
        if (file.filename.includes("_desktop_image")) {
          if (
            bannerData.desktop_image !== "" &&
            fs.existsSync(`Uploads/images/${bannerData.desktop_image}`)
          ) {
            fs.unlinkSync(`Uploads/images/${bannerData.desktop_image}`);
          }
        } else if (file.filename.includes("_mobile_image")) {
          if (
            bannerData.mobile_image !== "" &&
            fs.existsSync(`Uploads/images/${bannerData.mobile_image}`)
          ) {
            fs.unlinkSync(`Uploads/images/${bannerData.mobile_image}`);
          }
        }
      });
    }
    files.forEach(file => {
      if (file.filename.includes("_desktop_image")) {
        data.desktop_image = file.filename;
      }
      if (file.filename.includes("_mobile_image")) {
        data.mobile_image = file.filename;
      }
    });
    if (!data.serving_hours) {
      data.serving_hours = null;
    }
    if (!data.sync_type) {
      data.sync_type = null;
      data.sync_type_id = null;
      data.sync_type_label = null;
    }
    console.log("edit banner data", data);
    if (data.is_lsm == 1) {
      await this.adminService.deleteItemStores(
        null,
        null,
        null,
        null,
        banner_id,
        null
      ); //if item is lsm item so then stores will insert in ItemStoreRelationTable
      let storesJson = JSON.parse(data.stores_json);
      if (stores.length > 0) {
        storesJson.forEach(item => {
          let LSMStores = {
            store_id: item.value,
            banner_id: banner_id
          };
          itemStores.push(LSMStores);
        });
        await this.adminService.addLSMitems(itemStores);
      }
    } else if (data.is_lsm == 2) {
      await this.adminService.deleteItemStores(
        null,
        null,
        null,
        null,
        banner_id,
        null
      ); //if item is lsm item so then stores will insert in ItemStoreRelationTable
      let storesJson = JSON.parse(data.stores_json);
      if (stores.length > 0) {
        storesJson.forEach(item => {
          let LSMStores = {
            store_id: item.value,
            banner_id: banner_id
          };
          itemStores.push(LSMStores);
        });
        await this.adminService.addLSMitems(itemStores);
      }
    } else {
      await this.adminService.deleteItemStores(
        null,
        null,
        null,
        null,
        banner_id,
        null
      );
    }
    delete data.stores_json;
    let success = await this.adminService.editBanner(banner_id, data);
    if (success.affected > 0) {
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Edit Info",
        activity_type: "Banners",
        activity_name: banner_id,
        status: "Update Banner",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Banner Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Banner Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/getItemsForBanners")
  async getItemsForBanners(): Promise<any> {
    let data = await this.adminService.getItemsForBanners();
    let CombosData = await this.adminService.getCombosForBanners();
    let result: any = [];
    for (let i = 0; i < data.length + CombosData.length; i++) {
      if (data[i] != null) {
        result.push(data[i]);
      }
      if (CombosData[i] != null) {
        result.push(CombosData[i]);
      }
    }
    return { success: true, successResponse: result };
  }
  //Coupons only routes
  @Get("admin/coupons")
  async CouponData(): Promise<any> {
    let data = await this.adminService.CouponData();
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].storeCouponId && data[i].storeCouponId.length > 0) {
          let Stores: any = [];
          data[i].storeCouponId.forEach(element => {
            Stores.push({
              label: element.store_id.store_name,
              value: element.store_id.store_id
            });
          });
          data[i].stores_json = JSON.stringify(Stores);
          delete data[i].storeCouponId;
        }
        if (data[i].menuCouponId && data[i].menuCouponId.length > 0) {
          let Menus: any = [];
          data[i].menuCouponId.forEach(element => {
            Menus.push({
              label: element.menu_id.menu_name,
              value: element.menu_id.menu_id
            });
          });
          data[i].multiMenuJson = JSON.stringify(Menus);
          delete data[i].menuCouponId;
        }
        if (data[i].groupCouponId && data[i].groupCouponId.length > 0) {
          let Groups: any = [];
          data[i].groupCouponId.forEach(element => {
            Groups.push({
              label:
                element.group_id.group_name +
                " (" +
                element.group_id.menu_id.menu_name +
                ")",
              value: element.group_id.group_id
            });
          });
          data[i].multiGroupJson = JSON.stringify(Groups);
          delete data[i].groupCouponId;
        }
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Coupons Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/coupon/:id")
  async GetCouponById(@Param("id") id): Promise<any> {
    let coupon_id = Number(id);
    let data = await this.adminService.GetCouponById(coupon_id);
    if (data) {
      if (data.customerCouponId && data.customerCouponId.length > 0) {
        let Customers: any = [];
        data.customerCouponId.forEach(element => {
          if (element.assigned == 1) {
            Customers.push({
              label: element?.customer_id?.login_name,
              value: element?.customer_id?.customer_id
            });
          }
        });
        data.customers_json = JSON.stringify(Customers);
        delete data.customerCouponId;
      }
      if (data.storeCouponId && data.storeCouponId.length > 0) {
        let Stores: any = [];
        data.storeCouponId.forEach(element => {
          Stores.push({
            label: element.store_id.store_name,
            value: element.store_id.store_id
          });
        });
        data.stores_json = JSON.stringify(Stores);
        delete data.storeCouponId;
      }
      if (data.menuCouponId && data.menuCouponId.length > 0) {
        let Menus: any = [];
        data.menuCouponId.forEach(element => {
          Menus.push({
            label: element.menu_id.menu_name,
            value: element.menu_id.menu_id
          });
        });
        data.multiMenuJson = JSON.stringify(Menus);
        delete data.menuCouponId;
      }
      if (data.groupCouponId && data.groupCouponId.length > 0) {
        let Groups: any = [];
        data.groupCouponId.forEach(element => {
          Groups.push({
            label:
              element.group_id.group_name +
              " (" +
              element.group_id.menu_id.menu_name +
              ")",
            value: element.group_id.group_id
          });
        });
        data.multiGroupJson = JSON.stringify(Groups);
        delete data.groupCouponId;
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Coupon Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_coupon/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editCoupon(
    @Headers() headers: any,
    @Param("id") id,
    @Body() coupon: any
  ): Promise<any> {
    const token: any = headers["authorization"]?.split("Bearer ")[1];
    const jwtData: any = jwtDecode(token);
    const storeData = coupon.stores_json;
    const customersData = coupon.customers_json;
    const multiMenuJson = coupon.multiMenuJson;
    const multiGroupJson = coupon.multiGroupJson;
    delete coupon.stores_json;
    delete coupon.customers_json;
    delete coupon.multiMenuJson;
    delete coupon.multiGroupJson;
    let coupon_id = Number(id);
    if (coupon.free_delivery == 1) {
      coupon.type = null;
      coupon.type_id = 0;
    }
    let success = await this.adminService.editCoupon(coupon_id, coupon);
    if (!success || success.affected == 0) {
      throw new HttpException(
        {
          success: false,
          message: "Coupon Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
    await this.adminService.removeCouponStore(coupon_id);
    await this.adminService.removeCouponMenu(coupon_id);
    await this.adminService.removeCouponGroup(coupon_id);
    await this.adminService.updateAssignedCouponCustomers(coupon_id, 0);
    let couponStore: any = [];
    let couponCustomers: any = [];
    let couponMenu: any = [];
    let couponGroup: any = [];
    if (storeData) {
      let Stores = JSON.parse(storeData);
      let obj;
      for (let i = 0; i < Stores.length; i++) {
        obj = { store_id: Stores[i].value, coupon_id: coupon_id };
        couponStore.push(obj);
      }
      await this.adminService.addStoresForCoupons(couponStore);
    }
    if (customersData) {
      let Customers = JSON.parse(customersData);
      let obj;
      for (let i = 0; i < Customers.length; i++) {
        let findCustomer = await this.adminService.GetCouponCustomerBycouponId(
          coupon_id,
          Customers[i].value
        );
        if (findCustomer) {
          await this.adminService.updateAssignedCouponCustomers(
            coupon_id,
            1,
            Customers[i].value
          );
        } else {
          obj = {
            customer_id: Customers[i].value,
            coupon_id: coupon_id,
            assigned: 1,
            coupon_count: 0
          };
          couponCustomers.push(obj);
        }
      }
      await this.adminService.addCustomersForCoupons(couponCustomers);
    }
    if (multiMenuJson) {
      let Menus = JSON.parse(multiMenuJson);
      let obj;
      for (let i = 0; i < Menus.length; i++) {
        obj = { menu_id: Menus[i].value, coupon_id: coupon_id };
        couponMenu.push(obj);
      }
      await this.adminService.addMenusForCoupons(couponMenu);
    }
    if (multiGroupJson) {
      let Groups = JSON.parse(multiGroupJson);
      let obj;
      for (let i = 0; i < Groups.length; i++) {
        obj = { group_id: Groups[i].value, coupon_id: coupon_id };
        couponGroup.push(obj);
      }
      await this.adminService.addGroupsForCoupons(couponGroup);
    }
    let logData: any = {
      role: jwtData.role,
      user_name: jwtData.first_name + " " + jwtData.last_name,
      reason: "Edit Info",
      activity_type: "Coupons",
      activity_name: `code: ${coupon.coupon_code}`,
      status: "Update Coupon"
    };
    await this.adminService.addLogs(logData);
    return { success: true, successResponse: "Coupon Updated Successfully" };
  }
  @Post("admin/add_coupon")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addCoupon(@Headers() headers: any, @Body() coupon: any): Promise<any> {
    const token: any = headers["authorization"]?.split("Bearer ")[1];
    const jwtData: any = jwtDecode(token);
    coupon.total_usage = coupon.limit;
    const success = await this.adminService.addCoupon(coupon);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Coupon Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    }
    let couponStore: any = [];
    let couponCustomers: any = [];
    let couponMenu: any = [];
    let couponGroup: any = [];
    if (coupon.stores_json) {
      let Stores = JSON.parse(coupon.stores_json);
      let obj;
      for (let i = 0; i < Stores.length; i++) {
        obj = { store_id: Stores[i].value, coupon_id: success.coupon_id };
        couponStore.push(obj);
      }
      await this.adminService.addStoresForCoupons(couponStore);
    }
    if (coupon.customers_json) {
      let Customers = JSON.parse(coupon.customers_json);
      let obj;
      for (let i = 0; i < Customers.length; i++) {
        obj = {
          customer_id: Customers[i].value,
          coupon_id: success.coupon_id,
          assigned: 1,
          coupon_count: 0
        };
        couponCustomers.push(obj);
      }
      await this.adminService.addCustomersForCoupons(couponCustomers);
    }
    if (coupon.multiMenuJson) {
      let Menus = JSON.parse(coupon.multiMenuJson);
      let obj;
      for (let i = 0; i < Menus.length; i++) {
        obj = { menu_id: Menus[i].value, coupon_id: success.coupon_id };
        couponMenu.push(obj);
      }
      await this.adminService.addMenusForCoupons(couponMenu);
    }
    if (coupon.multiGroupJson) {
      let Groups = JSON.parse(coupon.multiGroupJson);
      let obj;
      for (let i = 0; i < Groups.length; i++) {
        obj = { group_id: Groups[i].value, coupon_id: success.coupon_id };
        couponGroup.push(obj);
      }
      await this.adminService.addGroupsForCoupons(couponGroup);
    }
    let logData: any = {
      role: jwtData.role,
      user_name: jwtData.first_name + " " + jwtData.last_name,
      reason: "New Coupon",
      activity_type: "Coupons",
      activity_name: `code: ${coupon.coupon_code}`,
      status: "Add Coupon"
    };
    await this.adminService.addLogs(logData);
    return { success: true, successResponse: "Coupon Inserted Successfully" };
  }
  @Post("admin/del_coupon/:id")
  async deleteCoupon(
    @Req() request: Request,
    @Param("id") id,
    @Body() coupon: any
  ): Promise<any> {
    let coupon_id = Number(id);
    await this.adminService.deleteEmpCoupon(coupon_id);
    let success = await this.adminService.deleteCoupon(coupon_id);
    if (success) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: coupon.user_info.role,
        user_name: coupon.user_info.first_name + coupon.user_info.last_name,
        reason: "Delete",
        activity_type: "Coupons",
        activity_name: `code: ${coupon.coupon_code}`,
        status: "Delete Coupon",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Coupon Deleted Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Coupon Not Deleted"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/coupon_active_inactive/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async couponActiveInactive(
    @Req() request: Request,
    @Param("id") id,
    @Body() coupon: any
  ): Promise<any> {
    let couponId = Number(id);
    let success = await this.adminService.couponActiveInactive(
      couponId,
      coupon.is_archive
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: coupon.user_info.role,
        user_name: coupon.user_info.first_name + coupon.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Coupons",
        activity_name: `code: ${coupon.coupon_code}`,
        status: coupon.is_archive == 1 ? "Block Coupon" : "UnBlock Coupon",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Coupon Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Coupon Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/coupons-upload")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads",
        filename: editFileName
      }),
      fileFilter: excelFileFilter
    })
  )
  async CouponsUpload(
    @Headers() header,
    @UploadedFile() file: any,
    @Res() res: Response,
    @Body() body: any
  ) {
    let token: any = header["authorization"]?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    const tempFilePath = `Uploads/${file.filename}`;
    const readableStream = fs.createReadStream(tempFilePath, {
      highWaterMark: 1024 * 1024
    }); // 1MB chunks
    const parser = readableStream.pipe(csvParser());
    parser.on("data", async row => {
      if (Object.keys(row).length > 0) {
        try {
          let data = await this.adminService.GetCouponByCouponCode(
            row.CustomerVoucherCode
          );
          // console.log("step1", row.CustomerVoucherCode)
          if (!data) {
            // console.log("step2", row.CustomerVoucherCode)
            // Validate and transform data before saving
            const coupon = new Coupon();
            coupon.coupon_code = row.CustomerVoucherCode;
            coupon.coupon_name = body.coupon_name || "";
            coupon.pos_code = body.pos_code || "";
            coupon.coupon_description = body.coupon_description || "";
            coupon.discount_type = body.discount_type || "";
            coupon.coupon_value = Number(body.coupon_value) || 0;
            coupon.items_json = ""; // Assuming this is handled separately

            coupon.is_archive = Number(body.is_archive) || 0;
            coupon.start_date = new Date(body.start_date);
            coupon.expire_date = new Date(body.expire_date);
            coupon.total_usage = Number(body.total_usage) || 0;
            coupon.mode = body.mode || "";
            coupon.channel = body.channel || "";
            coupon.type = body.type || "";
            coupon.type_id = Number(body.type_id) || 0;
            coupon.percent = Number(body.percent) || 0;
            coupon.limit = Number(body.limit) || 0;
            coupon.free_delivery = Number(body.free_delivery) || 0;
            coupon.for_customer = Number(body.for_customer) || 0;
            coupon.min_amount = Number(body.min_amount) || 0;
            coupon.min_orders = Number(body.min_orders) || 0;
            coupon.total_usage_customer =
              Number(body.total_usage_customer) || 0;
            coupon.is_emp = Number(body.is_nps) || 0;
            coupon.is_partner = Number(body.is_partner) || 0;
            coupon.is_vip = Number(body.is_vip) || 0;
            coupon.is_nps = Number(body.is_nps) || 0;
            coupon.usage_duration = body.usage_duration;
            let success = await this.adminService.addCoupon(coupon);
            // console.log("step3", row.CustomerVoucherCode, success)
            if (success) {
              // console.log("step4", row.CustomerVoucherCode)
              let couponStore: any = [];
              let couponCustomers: any = [];
              let couponMenu: any = [];
              let couponGroup: any = [];
              if (body.stores_json) {
                let Stores = JSON.parse(body.stores_json);
                let obj;
                for (let i = 0; i < Stores.length; i++) {
                  obj = {
                    store_id: Stores[i].value,
                    coupon_id: success.coupon_id
                  };
                  couponStore.push(obj);
                }
                await this.adminService.addStoresForCoupons(couponStore);
              }
              if (body.customers_json) {
                let Customers = JSON.parse(body.customers_json);
                let obj;
                for (let i = 0; i < Customers.length; i++) {
                  obj = {
                    customer_id: Customers[i].value,
                    coupon_id: success.coupon_id,
                    assigned: 1,
                    coupon_count: 0
                  };
                  couponCustomers.push(obj);
                }
                await this.adminService.addCustomersForCoupons(couponCustomers);
              }
              if (body.multiMenuJson) {
                let Menus = JSON.parse(body.multiMenuJson);
                let obj;
                for (let i = 0; i < Menus.length; i++) {
                  obj = {
                    menu_id: Menus[i].value,
                    coupon_id: success.coupon_id
                  };
                  couponMenu.push(obj);
                }
                await this.adminService.addMenusForCoupons(couponMenu);
              }
              if (body.multiGroupJson) {
                let Groups = JSON.parse(body.multiGroupJson);
                let obj;
                for (let i = 0; i < Groups.length; i++) {
                  obj = {
                    group_id: Groups[i].value,
                    coupon_id: success.coupon_id
                  };
                  couponGroup.push(obj);
                }
                await this.adminService.addGroupsForCoupons(couponGroup);
              }
            }
          }
        } catch (error) {
          console.log("error", error.message);
        }
      }
    });
    parser.on("end", async () => {
      console.log("Data stream complete");
      fs.unlinkSync(tempFilePath); // Clean up temporary file
      let obj = {
        email: jwtData.email_address
      };
      sendDataUploadNotification(obj);
    });
    parser.on("error", error => {
      console.log("Data error", error);
      fs.unlinkSync(tempFilePath); // Clean up temporary file
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        success: false,
        message: `Error processing file ${error}`
      });
    });
    return res.status(HttpStatus.OK).json({
      statusCode: HttpStatus.OK,
      success: true,
      message: "Data successfully uploading"
    });
  }
  @Post("admin/coupons-upload-workers")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads",
        filename: editFileName
      }),
      fileFilter: excelFileFilter
    })
  )
  async CouponsUploadWorks(@Res() res: Response): Promise<any> {
    const result = await this.workerService.executeTaskInWorker({
      limit: 20000000000
    });
    res.status(HttpStatus.OK).json({ result: result });
  }
  @Get("admin/non-blocking")
  async nonBlocking(@Res() res: Response): Promise<any> {
    res.status(HttpStatus.OK).send("This page is non blocking");
  }

  @Get("admin/blocking")
  async blocing(@Res() res: Response): Promise<any> {
    const result = await this.workerService.executeTaskInWorker({
      limit: 20000000000
    });
    res.status(HttpStatus.OK).json({ result: result });
  }

  @Get("admin/active_emp_coupons")
  async activeCoupons(): Promise<any> {
    let data = await this.adminService.activeEmpCoupons();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Coupons Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  // coupen pagination only routes
  @Get("admin/paginated-coupons/:page")
  async findPaginationCoupon(@Param("page") page: any): Promise<any> {
    let numOfRows = await this.adminService.countCouponDataFetch();
    const perPage = couponsPerPage;
    const skip: any = perPage * page;
    let data = await this.adminService.CouponDataForPaginations(skip, perPage);
    if (data.length > 0) {
      return {
        success: true,
        successResponse: data,
        currentPage: page,
        pageCount: Math.ceil(numOfRows / perPage),
        numOfRows: numOfRows
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Coupons Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Post("admin/search_coupons/:page")
  async getCouponsByCouponCode(
    @Param("page") page: any,
    @Body() searchBody: ICouponSearch
  ): Promise<any> {
    const { search_type, search_value } = searchBody;
    const totalSearchCoupons = await this.adminService.countSearchCouponsDataFetch(search_type, search_value);
    const perPage = couponsPerPage;
    const skip: any = perPage * page;
    const data = await this.adminService.searchCouponsByCode(search_type, search_value, skip, perPage);
    const totalCoupons = totalSearchCoupons.length;
    if (data.length == 0) {
      throw new HttpException(
        {
          success: false,
          message: "Coupons not found"
        },
        HttpStatus.FORBIDDEN
      );
    } 

    return {
      success: true,
      successResponse: data,
      currentPage: page,
      pageCount: Math.ceil(totalCoupons / perPage),
      numOfRows: totalCoupons
    };

  }

  //Discounts only routes
  @Get("admin/discounts")
  async DiscountData(): Promise<any> {
    let data = await this.adminService.DiscountData();
    if (data.length > 0) {
      data.forEach(function(obj) {
        var ExpireDate = obj.expire_date.toLocaleString().split(",");
        obj.expire_date = ExpireDate[0];
      });
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discounts Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/discount/:id")
  async GetDiscountById(@Param("id") id): Promise<any> {
    let discount_id = Number(id);
    let data = await this.adminService.GetDiscountById(discount_id);
    if (data) {
      if (data.storeDiscountId && data.storeDiscountId.length > 0) {
        let Stores: any = [];
        data.storeDiscountId.forEach(element => {
          Stores.push({
            label: element.store_id.store_name,
            value: element.store_id.store_id
          });
        });
        data.stores_json = JSON.stringify(Stores);
        delete data.storeDiscountId;
      }
      if (data.discount_variant_id && data.discount_variant_id.length > 0) {
        let Array: any = [];
        data.discount_variant_id.forEach(element => {
          let obj = {
            value: element.variant_id.id,
            label: element.variant_id.name
          };
          Array.push(obj);
        });
        data.variants = Array;
        delete data.discount_variant_id;
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_discount/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editDiscount(
    @Req() request: Request,
    @Param("id") id,
    @Body() discount: any
  ): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let storeData = discount.stores_json;
    delete discount.stores_json;
    let discount_id = Number(id);
    let variants: any;
    if (discount.for_specific_variations == 1) {
      variants = JSON.parse(discount.variants);
      delete discount.variants;
    }
    let success = await this.adminService.editDiscount(discount_id, discount);
    if (success.affected === 0) {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
    await this.adminService.deleteVarRel(null, discount_id);
    if (discount.for_specific_variations == 1) {
      let discount_var_data: any = [];
      variants.forEach(element => {
        let obj = {
          discount_id: discount_id,
          variant_id: element.value
        };
        discount_var_data.push(obj);
      });
      await this.adminService.addVariatnsRelations(discount_var_data);
    }
    let discountStores: any = [];
    if (discount.is_lsm == 1) {
      await this.adminService.removeDiscountStore(discount_id);
      let Stores = JSON.parse(storeData);
      let obj;
      for (let i = 0; i < Stores.length; i++) {
        obj = { store_id: Stores[i].value, discount_id: discount_id };
        discountStores.push(obj);
      }
      await this.adminService.addStoresForDiscounts(discountStores);
    } else if (discount.is_lsm == 2) {
      await this.adminService.removeDiscountStore(discount_id);
      let Stores = JSON.parse(storeData);
      let obj;
      for (let i = 0; i < Stores.length; i++) {
        obj = { store_id: Stores[i].value, discount_id: discount_id };
        discountStores.push(obj);
      }
      await this.adminService.addStoresForDiscounts(discountStores);
    } else {
      await this.adminService.removeDiscountStore(discount_id);
    }
    let logData: any = {
      role: jwtData.role,
      user_name: jwtData.first_name + " " + jwtData.last_name,
      reason: "Edit Info",
      activity_type: "Discounts",
      activity_name: id,
      status: "Update Discount"
    };
    await this.adminService.addLogs(logData);
    return { success: true, successResponse: "Discount Updated Successfully" };
  }
  @Patch("admin/bulk_coupon_update")
  async bulkUpdateCouponByPulseCode(@Res() res: Response, @Body(new badRequestValidationPipe()) data: bulkCouponUpdate) {
    const { actionType, actionParam, massUpdate } = data;
    const result = await this.adminService.bulkCouponUpdateByPOSCode(actionType, actionParam, massUpdate);
    if (result.affected == 0) {
      return res
        .status(HttpStatus.BAD_REQUEST)
        .send({ success: false, message: "Coupons not updated" });
    }
    return res
      .status(HttpStatus.OK)
      .send({ success: true, successResponse: "Coupons updated successfully" });
  }
  @Post("admin/add_discount")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addDiscount(
    @Req() request: Request,
    @Body() discount: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let success = await this.adminService.addDiscount(discount);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    }
    if (discount.for_specific_variations == "1") {
      let discount_var_data: any = [];
      let variants: any = JSON.parse(discount.variants);
      variants.forEach(element => {
        let obj = {
          discount_id: success.discount_id,
          variant_id: element.value
        };
        discount_var_data.push(obj);
      });
      await this.adminService.addVariatnsRelations(discount_var_data);
    }
    let discountStores: any = [];
    if (discount.is_lsm == 1) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      let Stores = JSON.parse(discount.stores_json);
      let obj;
      for (let i = 0; i < Stores.length; i++) {
        obj = { store_id: Stores[i].value, discount_id: success.discount_id };
        discountStores.push(obj);
      }
      await this.adminService.addStoresForDiscounts(discountStores);
    } else if (discount.is_lsm == 2) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      let Stores = JSON.parse(discount.stores_json);
      let obj;
      for (let i = 0; i < Stores.length; i++) {
        obj = { store_id: Stores[i].value, discount_id: success.discount_id };
        discountStores.push(obj);
      }
      await this.adminService.addStoresForDiscounts(discountStores);
    }
    let logData: any = {
      role: jwtData.role,
      user_name: jwtData.first_name + " " + jwtData.last_name,
      reason: "New Discount",
      activity_type: "Discounts",
      activity_name: success.discount_id,
      status: "Add Discount",
      ip_address: _ip
    };
    await this.adminService.addLogs(logData);
    return { success: true, successResponse: "Discount Inserted Successfully" };
  }
  @Post("admin/add_fp_discount")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addfpDiscount(
    @Req() request: Request,
    @Body() discount: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let success = await this.adminService.addFPDiscount(discount);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "New FP Discount",
        activity_type: "FP Discounts",
        activity_name: success.id,
        status: "Add fp Discount",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Discount Inserted Successfully"
      };
    }
  }
  @Get("admin/fp_discounts")
  async FPDiscountData(): Promise<any> {
    let data = await this.adminService.FPDiscountData();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discounts Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/fp_discount/:id")
  async GetFPDiscountById(@Param("id") id): Promise<any> {
    let discount_id = Number(id);
    let data = await this.adminService.GetFPDiscountById(discount_id);
    if (data) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_fp_discount/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editFPDiscount(
    @Req() request: Request,
    @Param("id") id,
    @Body() discount: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    delete discount.stores_json;
    let discount_id = Number(id);
    let success = await this.adminService.editfpDiscount(discount_id, discount);
    if (success.affected > 0) {
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Edit Info",
        activity_type: "FP Discounts",
        activity_name: id,
        status: "Update FP Discount",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Discount Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/fp_discount_active_inactive/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async fpdiscountActiveInactive(
    @Req() request: Request,
    @Param("id") id,
    @Body() discount: any
  ): Promise<any> {
    let discountId = Number(id);
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let success = await this.adminService.fpdiscountActiveInactive(
      discountId,
      discount.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + jwtData.last_name,
        reason: "Block/UnBlock",
        activity_type: "FP Discounts",
        activity_name: id,
        status: discount.is_active == 1 ? "UnBlock Discount" : "Block Discount",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Discount Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Delete("admin/del_discount/:id")
  async deleteDiscount(@Req() request: Request, @Param("id") id): Promise<any> {
    let discount_id = Number(id);
    let success = await this.adminService.deleteDiscount(discount_id);
    if (success) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let token: any = request.headers.authorization?.split("Bearer ")[1];
      let jwtData: any = jwtDecode(token);
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + jwtData.last_name,
        reason: "Delete",
        activity_type: "Discounts",
        activity_name: id,
        status: "Delete Discount",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Discount Deleted Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Deleted"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/discount_active_inactive/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async discountActiveInactive(
    @Req() request: Request,
    @Param("id") id,
    @Body() discount: any
  ): Promise<any> {
    let discountId = Number(id);
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let success = await this.adminService.discountActiveInactive(
      discountId,
      discount.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + jwtData.last_name,
        reason: "Block/UnBlock",
        activity_type: "Discounts",
        activity_name: id,
        status: discount.is_active == 1 ? "UnBlock Discount" : "Block Discount",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Discount Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Promos only routes
  @Get("admin/promos")
  async PromoData(): Promise<any> {
    let data = await this.adminService.PromoData();
    if (data.length > 0) {
      data.forEach(function(obj) {
        var ExpireDate = obj.expire_date.toLocaleString().split(",");
        obj.expire_date = ExpireDate[0];
      });
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Promos Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/promo/:id")
  async GetPromoById(@Param("id") id): Promise<any> {
    let promo_id = Number(id);
    let data = await this.adminService.GetPromoById(promo_id);
    if (data) {
      let ExpireDate = data.expire_date.toLocaleString().split(",");
      let Date = ExpireDate[0].split("/");
      data.expire_date =
        Date[2] +
        "-" +
        ("0" + Date[0]).slice(-2) +
        "-" +
        ("0" + Date[1]).slice(-2); //format(2020-02-12)
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Promo Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_promo/:id")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editPromo(
    @Req() request: Request,
    @Param("id") id,
    @UploadedFile() file,
    @Body() promo: Promo
  ): Promise<any> {
    let promo_id = Number(id);
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let data: any = promo;
    let userData = JSON.parse(data.user_info);
    delete data.user_info;
    if (file) {
      data["promo_image"] = file.filename;
    }
    let success = await this.adminService.editPromo(promo_id, data);
    if (success.affected > 0) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Promos",
        activity_name: data.promo_name,
        status: "Update Promo",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Promo Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Promo Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/add_promo")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: diskStorage({
        destination: "./Uploads/images",
        // destination:'/home/<USER>/dominos/Uploads/images',
        filename: editFileName
      }),
      fileFilter: imageFileFilter
    })
  )
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addPromo(
    @Req() request: Request,
    @UploadedFile() file,
    @Body() promo: Promo
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let data: any = promo;
    let userData = JSON.parse(data.user_info);
    delete data.user_info;
    if (file) {
      data["promo_image"] = file.filename;
      let success = await this.adminService.addPromo(data);
      if (!success) {
        throw new HttpException(
          {
            success: false,
            message: "Promo Not Inserted"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        let logData: any = {
          role: userData.role,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "New Promo",
          activity_type: "Promos",
          activity_name: data.promo_name,
          status: "Add Promo",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        return {
          success: true,
          successResponse: "Promo Inserted Successfully"
        };
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "No Image Selected"
        },
        HttpStatus.NOT_ACCEPTABLE
      );
    }
  }
  @Post("admin/del_promo/:id")
  async deletePromo(
    @Req() request: Request,
    @Param("id") id,
    @Body() promo: any
  ): Promise<any> {
    let promo_id = Number(id);
    let success = await this.adminService.deletePromo(promo_id);
    if (success) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: promo.user_info.role,
        user_name: promo.user_info.first_name + " " + promo.user_info.last_name,
        reason: "Delete",
        activity_type: "Promos",
        activity_name: promo.promo_name,
        status: "Delete Promo",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Promo Deleted Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Promo Not Deleted"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/promo_active_inactive/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async promoActiveInactive(
    @Req() request: Request,
    @Param("id") id,
    @Body() promo: any
  ): Promise<any> {
    let promoId = Number(id);
    let success = await this.adminService.promoActiveInactive(
      promoId,
      promo.is_archive
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: promo.user_info.role,
        user_name: promo.user_info.first_name + " " + promo.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "Promos",
        activity_name: promo.promo_name,
        status: promo.is_archive == 1 ? "Block Promo" : "UnBlock Promo",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Promo Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Promo Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Push Notifications only routes
  @Get("admin/notifications")
  async PushNotificationsData(): Promise<any> {
    let data = await this.adminService.PushNotificationsData();
    if (data.length > 0) {
      data.forEach(function(obj) {
        var ExpireDate = obj.expire_date.toLocaleString().split(",");
        let data = JSON.parse(obj.notification_data);
        obj.title = data.title;
        obj.description = data.body;
        obj.expire_date = ExpireDate[0];
      });
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Notifications Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/notification/:id")
  async GetNotificationById(@Param("id") id): Promise<any> {
    let notification_id = Number(id);
    let data = await this.adminService.GetNotificationById(notification_id);
    if (data) {
      let ExpireDate = data.expire_date.toLocaleString().split(",");
      let Date = ExpireDate[0].split("/");
      data.expire_date =
        Date[2] +
        "-" +
        ("0" + Date[0]).slice(-2) +
        "-" +
        ("0" + Date[1]).slice(-2); //format(2020-02-12)
      let body = JSON.parse(data.notification_data);
      data.title = body.title;
      data.description = body.body;
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/edit_notification/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editNotification(
    @Res() res: Response,
    @Req() request: Request,
    @Param("id") id,
    @Body() notification: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = notification.user_info;
    delete notification.user_info;
    let notification_id = Number(id);
    let FCMArray: any = [];
    let getPrevData = await this.adminService.GetNotificationById(id);
    let prevPn_cron_job_id = null;
    if (getPrevData) {
      prevPn_cron_job_id = getPrevData.pn_cron_job_id;
    }
    let notificationData: any = {
      title: notification.title,
      body: notification.body
    };
    let body: any = {
      // method: 'promos',
      notification_data: JSON.stringify(notificationData),
      expire_date: notification.expire_date,
      is_active: 1
    };
    if (notification.is_schedule == 1) {
      body.is_schedule = notification.is_schedule;
      body.pn_start_time = notification.pn_start_time;
      //Snooze Start time in utc
      // .subtract(5, 'h')
      let pn_start_utc = moment(body.pn_start_time).subtract(5, "h");
      console.log(pn_start_utc);
      let pnStarthours = moment(pn_start_utc)
        .utc(true)
        .hours();
      let pnStartminutes = moment(pn_start_utc)
        .utc(true)
        .minutes();
      console.log(pnStarthours);
      console.log(pnStartminutes);
      let pnStartdate = pn_start_utc.toISOString().split("T")[0];
      const pnStartsplitDate: any = pnStartdate.split("-");
      const pnStartdayOfMonth = pnStartsplitDate[2];
      const pnStartmonth = pnStartsplitDate[1] - 1; //because cron month index starts from 0
      let pnStartCron: any = "";
      // Generate a v1 (time-based) id
      const pnStartCronname = uuid
        .v1()
        .substr(0, 20)
        .replace(/-/g, "");
      pnStartCron = `${pnStartminutes} ${pnStarthours} ${pnStartdayOfMonth} ${pnStartmonth} *`;
      body.pn_cron_job_id = pnStartCronname;
      body.pn_cron_job = pnStartCron;
      body.pn_cron_job_date =
        pnStartdate + " " + pnStarthours + ":" + pnStartminutes + ":00";
    } else {
      body.is_schedule = 0;
      body.pn_start_time = null;
      body.pn_cron_job_id = null;
      body.pn_cron_job = null;
      body.pn_cron_job_date = null;
    }
    let success = await this.adminService.editNotification(
      notification_id,
      body
    );
    if (success.affected > 0) {
      let logDataArray: any = [];
      if (notification.is_schedule == 1) {
        axios
          .post(
            `http://localhost:5014/tasks/push_notification_scheduling`,
            {
              push_notification_id: notification_id,
              pnStartCronname: body.pn_cron_job_id,
              pnStartCron: body.pn_cron_job,
              notification_data: JSON.stringify(notificationData),
              prevPn_cron_job_id: prevPn_cron_job_id
            },
            {
              headers: { "Content-Type": "application/json" }
            }
          )
          .then(async (resp: any) => {
            console.log("response", resp.data);
            logDataArray.push(
              {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: "Notification Cron Created",
                activity_type: "PushNotifications",
                activity_name: notification.title,
                status: "Notification Cron Creation Successfully",
                ip_address: _ip
              },
              {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: "Edit Info",
                activity_type: "PushNotifications",
                activity_name: notification.title,
                status: "Update Notification",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(200).send({
              success: true,
              successResponse: "Update Store Snooze Successfully"
            });
          })
          .catch(async (err: any) => {
            logDataArray.push(
              {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: err.message,
                activity_type: "PushNotifications",
                activity_name: notification.title,
                status: "Notification Cron not saved",
                ip_address: _ip
              },
              {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: "Edit Info",
                activity_type: "PushNotifications",
                activity_name: notification.title,
                status: "Update Notification",
                ip_address: _ip
              }
            );
            console.log("err", err);
            await this.adminService.addLogs(logDataArray);
            res.status(500).send({ success: false, message: err.message });
          });
      } else {
        let logData: any = {
          role: userData.role,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "Edit Info",
          activity_type: "PushNotifications",
          activity_name: notification.title,
          status: "Update Notification",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        let customerCount: any = await this.adminService.getTotalCustomers();
        if (customerCount > 0) {
          for (let index = 0; index < customerCount; index = index + 10000) {
            let customers: any = await this.adminService.getAllActiveFireBaseCustomers(
              index
            );
            customers.forEach((element: any) => {
              if (element.device_token) {
                FCMArray.push(element.device_token);
              }
              if (element.ios_device_token) {
                FCMArray.push(element.ios_device_token);
              }
            });
          }
          await sendNotification(FCMArray, notificationData);
        }
        res.status(200).send({
          success: true,
          successResponse: "Notification Updated Successfully"
        });
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Notification Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/add_notification")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addNotification(
    @Res() res: Response,
    @Req() request: Request,
    @Body() notification: any
  ): Promise<any> {
    let FCMArray: any = [];
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = notification.user_info;
    delete notification.user_info;
    let notificationData: any = {
      title: notification.title,
      body: notification.body
    };
    let body: any = {
      // method: 'promos',
      notification_data: JSON.stringify(notificationData),
      expire_date: notification.expire_date,
      is_active: 1
    };
    if (notification.is_schedule == 1) {
      body.is_schedule = notification.is_schedule;
      body.pn_start_time = notification.pn_start_time;
      //Snooze Start time in utc
      // .subtract(5, 'h')
      let pn_start_utc = moment(body.pn_start_time).subtract(5, "h");
      console.log(pn_start_utc);
      let pnStarthours = moment(pn_start_utc)
        .utc(true)
        .hours();
      let pnStartminutes = moment(pn_start_utc)
        .utc(true)
        .minutes();
      console.log(pnStarthours);
      console.log(pnStartminutes);
      let pnStartdate = pn_start_utc.toISOString().split("T")[0];
      const pnStartsplitDate: any = pnStartdate.split("-");
      const pnStartdayOfMonth = pnStartsplitDate[2];
      const pnStartmonth = pnStartsplitDate[1] - 1; //because cron month index starts from 0
      let pnStartCron: any = "";
      // Generate a v1 (time-based) id
      const pnStartCronname = uuid
        .v1()
        .substr(0, 20)
        .replace(/-/g, "");
      pnStartCron = `${pnStartminutes} ${pnStarthours} ${pnStartdayOfMonth} ${pnStartmonth} *`;
      body.pn_cron_job_id = pnStartCronname;
      body.pn_cron_job = pnStartCron;
      body.pn_cron_job_date =
        pnStartdate + " " + pnStarthours + ":" + pnStartminutes + ":00";
    }
    let success = await this.adminService.addNotification(body);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Notification Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logDataArray: any = [];
      if (notification.is_schedule == 1) {
        axios
          .post(
            `http://localhost:5014/tasks/push_notification_scheduling`,
            {
              push_notification_id: success.id,
              pnStartCronname: body.pn_cron_job_id,
              pnStartCron: body.pn_cron_job,
              notification_data: JSON.stringify(notificationData)
            },
            {
              headers: { "Content-Type": "application/json" }
            }
          )
          .then(async (resp: any) => {
            console.log("response", resp.data);
            logDataArray.push(
              {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: "Notification Cron Created",
                activity_type: "PushNotifications",
                activity_name: notification.title,
                status: "Notification Cron Creation Successfully",
                ip_address: _ip
              },
              {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: "New PushNotification",
                activity_type: "PushNotifications",
                activity_name: notification.title,
                status: "Add Notification",
                ip_address: _ip
              }
            );
            await this.adminService.addLogs(logDataArray);
            res.status(200).send({
              success: true,
              successResponse: "Update Store Snooze Successfully"
            });
          })
          .catch(async (err: any) => {
            logDataArray.push(
              {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: err.message,
                activity_type: "PushNotifications",
                activity_name: notification.title,
                status: "Notification Cron not saved",
                ip_address: _ip
              },
              {
                role: userData.role,
                user_name: userData.first_name + " " + userData.last_name,
                reason: "New PushNotification",
                activity_type: "PushNotifications",
                activity_name: notification.title,
                status: "Add Notification",
                ip_address: _ip
              }
            );
            console.log("err", err);
            await this.adminService.addLogs(logDataArray);
            res.status(500).send({ success: false, message: err.message });
          });
      } else {
        let logData: any = {
          role: userData.role,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "New PushNotification",
          activity_type: "PushNotifications",
          activity_name: notification.title,
          status: "Add Notification",
          ip_address: _ip
        };
        await this.adminService.addLogs(logData);
        let customerCount: any = await this.adminService.getTotalCustomers();
        if (customerCount > 0) {
          for (let index = 0; index < customerCount; index = index + 10000) {
            let customers: any = await this.adminService.getAllActiveFireBaseCustomers(
              index
            );
            customers.forEach((element: any) => {
              if (element.device_token) {
                FCMArray.push(element.device_token);
              }
              if (element.ios_device_token) {
                FCMArray.push(element.ios_device_token);
              }
            });
          }
          await sendNotification(FCMArray, notificationData);
        }
        res.status(200).send({
          success: true,
          successResponse: "Notification Inserted Successfully"
        });
      }
    }
  }
  @Post("admin/del_notification/:id")
  async deleteNotification(
    @Req() request: Request,
    @Param("id") id,
    @Body() notification: any
  ): Promise<any> {
    let notification_id = Number(id);
    let success = await this.adminService.deleteNotification(notification_id);
    if (success) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: notification.user_info.role,
        user_name:
          notification.user_info.first_name + notification.user_info.last_name,
        reason: "Delete",
        activity_type: "PushNotificaitons",
        activity_name: notification.title,
        status: "Delete Notification",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Notification Deleted Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Notification Not Deleted"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/notification_active_inactive/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async notificationActiveInactive(
    @Req() request: Request,
    @Param("id") id,
    @Body() notification: any
  ): Promise<any> {
    let discountId = Number(id);
    let success = await this.adminService.notificationActiveInactive(
      discountId,
      notification.is_active
    );
    if (success.affected > 0) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: notification.user_info.role,
        user_name:
          notification.user_info.first_name + notification.user_info.last_name,
        reason: "Block/UnBlock",
        activity_type: "PushNotifications",
        activity_name: notification.title,
        status:
          notification.is_active == 1
            ? "UnBlock Notification"
            : "Block Notification",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Notification Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Notification Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Settings Only Routes
  @Post("admin/save_discount")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async saveDiscount(@Body() data: Discounts): Promise<any> {
    let success = await this.adminService.saveDiscount(data);
    if (success) {
      return { success: true, successResponse: "Discount Saved Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Discount Not Saved"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Post("admin/save_delivery_modes")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async saveDeliveryModes(@Body() data: any): Promise<any> {
    console.log(data);
    let success = await this.adminService.saveDeliveryModes(data);
    if (success) {
      return {
        success: true,
        successResponse: "Delivery Fee Saved Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Delivery Mode Not Saved"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Post("admin/save_deliveryfee")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async saveDelivery(@Req() request: Request, @Body() data: any): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = data.user_info;
    delete data.user_info;
    let success = await this.adminService.saveDelivery(data);
    if (success) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Update",
        activity_type: "Delivery Fee",
        activity_name: `state (${data.state_name})`,
        status: "Update Delivery Fee",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Delivery Fee Saved Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Delivery Fee Not Saved"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/delivery_timings")
  async getDeliveryTimings(): Promise<any> {
    let data = await this.adminService.getDeliveryTiming();
    if (data) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Delivery Timings Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/save_delivery_time")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async saveDeliveryTiming(
    @Req() request: Request,
    @Body() data: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = data.user_info;
    delete data.user_info;
    console.log(data);
    let currentTime = await this.adminService.getDeliveryTiming();
    let success: any;
    if (currentTime) {
      success = await this.adminService.saveDeliveryTiming(data);
    } else {
      success = await this.adminService.addDeliveryTiming(data);
    }
    if (success) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Update",
        activity_type: "Delivery Time",
        activity_name: "Setting",
        status: "Update Delivery Time",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Delivery Timing Saved Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Delivery Timing Not Saved"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/save_posServiceFee")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async savePOServiceFee(
    @Req() request: Request,
    @Body() data: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = data.user_info;
    delete data.user_info;
    let success = await this.adminService.savePOSFEE(data);
    if (success) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Update",
        activity_type: "POS Service Fee",
        activity_name: `state (${data.state_name})`,
        status: "Update POS Service Fee",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "POS Service Fee Saved Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "POS Service Fee Not Saved"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  // Roles ONLY ROUTES

  @Get("admin/roles")
  async rolesData(): Promise<any> {
    let data = await this.adminService.rolesData();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Roles Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/add_role")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addRole(@Req() request: Request, @Body() role: any): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = role.user_info;
    delete role.user_info;
    let success = await this.adminService.addRole(role);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Role Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "New Role",
        activity_type: "Roles",
        activity_name: role.role_name,
        status: "Add Role",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Role Inserted Successfully" };
    }
  }
  @Put("admin/edit_role/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editRole(
    @Req() request: Request,
    @Param("id") id,
    @Body() role: any
  ): Promise<any> {
    var _ip: any;
    _ip =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    if (_ip.substr(0, 7) == "::ffff:") {
      _ip = _ip.substr(7);
    }
    let userData = role.user_info;
    delete role.user_info;
    let role_id = Number(id);
    let success = await this.adminService.editRole(role_id, role);
    if (success.affected > 0) {
      let logData: any = {
        role: userData.role,
        user_name: userData.first_name + " " + userData.last_name,
        reason: "Edit Info",
        activity_type: "Roles",
        activity_name: role.role_name,
        status: "Update Role",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Role Updated Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Role Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/role/:id")
  async GetRole(@Param("id") id): Promise<any> {
    let role_id = Number(id);
    let data = await this.adminService.getRoleById(role_id);
    return { success: true, successResponse: data };
  }
  @Post("admin/del_role/:id")
  async deleteRole(
    @Req() request: Request,
    @Param("id") id,
    @Body() role: any
  ): Promise<any> {
    let role_id = Number(id);
    let success = await this.adminService.delRole(role_id);
    if (success) {
      var _ip: any;
      _ip =
        request.headers["x-forwarded-for"] || request.connection.remoteAddress;
      if (_ip.substr(0, 7) == "::ffff:") {
        _ip = _ip.substr(7);
      }
      let logData: any = {
        role: role.user_info.role,
        user_name: role.user_info.first_name + " " + role.user_info.last_name,
        reason: "Delete",
        activity_type: "Roles",
        activity_name: role.role_name,
        status: "Delete Role",
        ip_address: _ip
      };
      await this.adminService.addLogs(logData);
      return { success: true, successResponse: "Role Deleted Successfully" };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Role Not Deleted"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Feedback applications
  @Get("admin/feedbacks")
  async getAllFeedback(@Req() request: Request): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    let data = await this.adminService.getAllFeedback(store_ids);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Feedbacks Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  //Franchise applications
  @Get("admin/franchise")
  async getAllFranchiseApp(): Promise<any> {
    let data = await this.adminService.getAllFranchiseApp();
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Franchise Applications Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/add_logs")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async addLogs(
    @Req() request: Request,
    @Body() activity: ActivityLogs
  ): Promise<any> {
    var _ip: any =
      request.headers["x-forwarded-for"] || request.connection.remoteAddress;
    // This will filter the unideciilion ipv6 to only 7 substring
    if (_ip.substr(0, 7) == "::ffff:") {
      activity.ip_address = _ip.substr(7);
    }
    let success = await this.adminService.addLogs(activity);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Activity Not Inserted"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      return {
        success: true,
        successResponse: "Activity Inserted Successfully"
      };
    }
  }
  //Predefined things
  @Get("admin/sales_channel")
  async channelData(): Promise<any> {
    let data = await this.adminService.saleChannelData();
    return { success: true, successResponse: data };
  }
  @Get("admin/variants")
  async variantData(): Promise<any> {
    let data = await this.adminService.variantsData();
    return { success: true, successResponse: data };
  }
  @Get("admin/discounts")
  async discountlData(): Promise<any> {
    let data = await this.adminService.discountlData();
    return { success: true, successResponse: data };
  }
  @Get("admin/store_types")
  async storeTypesData(): Promise<any> {
    let data = await this.adminService.storeTypeData();
    return { success: true, successResponse: data };
  }
  @Get("admin/countries")
  async countryData(): Promise<any> {
    let data = await this.adminService.countryData();
    return { success: true, successResponse: data };
  }
  @Get("admin/storelist/:country/:state")
  async getStoresListByCountryandState(
    @Param("country") country,
    @Param("state") state
  ): Promise<any> {
    let data = await this.adminService.getStoresListByCountryandState(
      country,
      state
    );
    return { success: true, successResponse: data };
  }
  @Get("admin/deliveryfee/:country/:state/:store")
  async getDeliveryFeeByStateandStore(
    @Param("country") country,
    @Param("state") state,
    @Param("store") store
  ): Promise<any> {
    let data = await this.adminService.getDeliveryFeeByStateandStore(
      country,
      state,
      store
    );
    return { success: true, successResponse: data };
  }
  @Get("admin/combo_options/:id")
  async comboOptionData(@Param("id") id): Promise<any> {
    let menu_id = Number(id);
    let data = await this.adminService.comboOptionData(menu_id);
    for (let index = 0; index < data.length; index++) {
      let variant: any = await this.adminService.getItemsVariations(
        data[index].id
      );
      data[index].item_size = JSON.stringify(variant.variants);
    }
    function groupBy(objectArray, property) {
      return objectArray.reduce((acc, obj) => {
        const key = obj[property];
        if (!acc[key]) {
          acc[key] = [];
        }
        // Add object to list for given key's value
        acc[key].push(obj);
        return acc;
      }, {});
    }
    const comboOptions = groupBy(data, "group_name");
    return { success: true, successResponse: comboOptions };
  }
  @Get("admin/groups_options")
  async groupsOptionData(): Promise<any> {
    let data = await this.adminService.groupOptionsData();
    return { success: true, successResponse: data };
  }
  @Get("admin/sub_groups_options")
  async subgroupsOptionData(): Promise<any> {
    let data = await this.adminService.subgroupOptionsData();
    return { success: true, successResponse: data };
  }
  @Get("admin/getAllActiveItems")
  async getAllActiveItems(): Promise<any> {
    let data = await this.adminService.getAllActiveItems();
    let ComboData = await this.adminService.getAllActiveCombo();
    let result: any = [];
    for (let i = 0; i < data.length + ComboData.length; i++) {
      if (data[i] != null) {
        data[i].value = `item_${data[i].value}`;
        result.push(data[i]);
      }
      if (ComboData[i] != null) {
        ComboData[i].value = `combo_${ComboData[i].value}`;
        result.push(ComboData[i]);
      }
    }
    return { success: true, successResponse: result };
  }
  @Get("admin/getAllbusinessTypes")
  async getAllbusinessTypes(): Promise<any> {
    let data = await this.adminService.getAllbusinessTypes();
    return { success: true, successResponse: data };
  }

  @Get("admin/getBackupStores/:id")
  async getStoreBackupByStores(@Param("id") id): Promise<any> {
    let data = await this.adminService.getBackupStoreByStoreId(id);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Brands Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/cities")
  async citiesData(): Promise<any> {
    let data = await this.adminService.getCities();
    if (data && data.length > 0) {
      data.forEach((obj: any) => {
        obj.country = obj.country_id && obj.country_id.country_name;
      });
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "City not found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/save_city")
  async addCity(@Req() request: Request, @Body() city: any): Promise<any> {
    console.log(city);
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let userData: any = await this.adminService.getUserById(jwtData.user_id);
    let cityData: any = await this.adminService.cityDataByName(city.name);
    if (cityData) {
      throw new HttpException(
        {
          success: false,
          message: "City Name already exist"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let data: any = await this.adminService.saveCity(city);
      if (!data) {
        throw new HttpException(
          {
            success: false,
            message: "City Not Added"
          },
          HttpStatus.FORBIDDEN
        );
      } else {
        let logData: any = {
          role: userData.role_name,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "New City",
          activity_type: "Cities",
          activity_name: data.name,
          status: "Add City",
          ip_address: ""
        };
        await this.adminService.addLogs(logData);
        return { success: true, successResponse: "City Added Successfully" };
      }
    }
  }
  @Put("admin/edit_city/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async editCity(
    @Req() request: Request,
    @Param("id") id,
    @Body() city: any
  ): Promise<any> {
    let city_id = Number(id);
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let userData: any = await this.adminService.getUserById(jwtData.user_id);
    let data: any = await this.adminService.cityDataByName(city.name);
    if (data) {
      throw new HttpException(
        {
          success: false,
          message: "City Name already exist"
        },
        HttpStatus.FORBIDDEN
      );
    } else {
      let success = await this.adminService.editCity(city_id, city);
      if (success) {
        let logData: any = {
          role: userData.role_name,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "Edit Info",
          activity_type: "Cities",
          activity_name: city.name,
          status: "Update City",
          ip_address: ""
        };
        await this.adminService.addLogs(logData);
        return { success: true, successResponse: "City Updated Successfully" };
      } else {
        throw new HttpException(
          {
            success: false,
            message: "City Not Updated"
          },
          HttpStatus.FORBIDDEN
        );
      }
    }
  }

  @Get("admin/get_city/:id")
  async cityData(@Param("id") id: number): Promise<any> {
    let data: any = await this.adminService.cityData(id);
    if (data) {
      data.country = data.country_id && data.country_id.country_id;
      data.country_name = data.country_id && data.country_id.country_name;
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "City not found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Delete("admin/del_city/:id")
  async deleteCity(
    @Req() request: Request,
    @Param("id") id,
    @Body() group: any
  ): Promise<any> {
    let city_id = Number(id);
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let userData: any = await this.adminService.getUserById(jwtData.user_id);
    try {
      let deleteCity = await this.adminService.delCity(city_id);
      if (deleteCity) {
        let logData: any = {
          role: userData.role_name,
          user_name: userData.first_name + " " + userData.last_name,
          reason: "Delete",
          activity_type: "Cities",
          activity_name: group.name,
          status: "Delete City",
          ip_address: ""
        };
        await this.adminService.addLogs(logData);
        return { success: true, successResponse: "City Deleted Successfully" };
      } else {
        await this.adminService.activeInActiveCity(city_id);
        return { success: true, successResponse: "City Deleted Successfully" };
      }
    } catch (e) {
      await this.adminService.activeInActiveCity(city_id);
      return { success: true, successResponse: "City Deleted Successfully" };
    }
  }
  //Extra Api remove after one time
  @Get("admin/add_default_groups")
  async addDefaultGroups(): Promise<any> {
    try {
      let data = await this.adminService.stores();
      let allAccess_Groups = ["Simplex Group", "Dominos Group"];
      for (let i = 0; i < data.length; i++) {
        let userDefaultGroup = {
          name: data[i].store_name,
          description:
            "Default group is created automatically when store is created",
          default_group: 1,
          is_active: 1
        };
        let defaultGroup = await this.adminService.addDefaultGroup(
          userDefaultGroup
        );
        if (defaultGroup) {
          let groupStore = {
            user_group_id: defaultGroup.id,
            store_id: data[i].store_id,
            is_active: 1
          };
          await this.adminService.addGroupStore(groupStore);
        }
        if (data.length - 1 == i) {
          for (let i = 0; i < allAccess_Groups.length; i++) {
            let allAccessGroups = {
              name: allAccess_Groups[i],
              description:
                "Group is created automatically and all stores are assigned to this group",
              all_access: 1,
              is_active: 1
            };
            let allAccessResponse = await this.adminService.addDefaultGroup(
              allAccessGroups
            );
            if (allAccessResponse) {
              let allStores: any = [];
              for (let j = 0; j < data.length; j++) {
                let groupStore = {
                  user_group_id: allAccessResponse.id,
                  store_id: data[j].store_id,
                  is_active: 1
                };
                allStores.push(groupStore);
              }
              await this.adminService.addGroupStore(allStores);
            }
          }
        }
      }
      return {
        success: true,
        successResponse: "Default Groups saved successfully"
      };
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/order_modes")
  async GetOrderModes(): Promise<any> {
    let orderModeData = await this.adminService.getOrderModes();
    return { success: true, successResponse: orderModeData };
  }

  @Get("admin/order_modes_settings")
  async GetOrderModesForfbr(): Promise<any> {
    try {
      let orderModeData = await this.adminService.getOrderModesForFbrSettings();
      for (let i = 0; i < orderModeData.length; i++) {
        let modeStores: any = [];
        let modeStorss = orderModeData[i].modeStoreId;
        if (modeStorss && modeStorss.length > 0) {
          modeStorss.forEach(element => {
            modeStores.push({
              label: element.store_id.store_name,
              value: element.store_id.store_id
            });
          });
          orderModeData[i].stores_json = JSON.stringify(modeStores);
        }
      }
      return { success: true, successResponse: orderModeData };
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Put("admin/edit_order_mode/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async EditOrderMode(
    @Req() request: Request,
    @Param("id") id,
    @Body() body: any
  ): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data = body;
    let modeId = Number(id);
    let modeStores: any = [];
    if (data.all_store == 1) {
      //if item is lsm item so then stores will insert in ItemStoreRelationTable
      await this.adminService.deleteModeStores(modeId);
      let storesJson = JSON.parse(data.stores_json);
      storesJson.forEach(item => {
        let ModeStores = {
          store_id: item.value,
          order_mode_id: modeId
        };
        modeStores.push(ModeStores);
      });
    } else {
      await this.adminService.deleteModeStores(modeId);
    }
    delete data.stores_json;
    let success = await this.adminService.editOrderMode(modeId, data);
    if (success.affected > 0) {
      await this.adminService.addStoresForOrderMode(modeStores);
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + " " + jwtData.last_name,
        reason: "Edit Info",
        activity_type: "Order Modes",
        activity_name: data.name,
        status: "Update Order Modes",
        ip_address: null
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Order Mode Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Order Mode Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Put("admin/mode_enableDisable_fbr/:id")
  @UsePipes(
    new ValidationPipe({
      skipMissingProperties: true,
      errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE
    })
  )
  async ModEnableDisableFbr(
    @Req() request: Request,
    @Param("id") id,
    @Body() body: any
  ): Promise<any> {
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let modeId = Number(id);
    let success = await this.adminService.modEnableDisableFbr(
      modeId,
      body.fbr_check
    );
    if (success.affected > 0) {
      let logData: any = {
        role: jwtData.role,
        user_name: jwtData.first_name + jwtData.last_name,
        reason: "Enable/Disable",
        activity_type: "Mod Groups",
        activity_name: body.order_mode_name,
        status:
          body.fbr_check == 1 ? "Enable Order Mode" : "Disable Order Mode",
        ip_address: null
      };
      await this.adminService.addLogs(logData);
      return {
        success: true,
        successResponse: "Order Mode Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Order Mod Not Updated"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Get("admin/mode/:id")
  async GetModeDetail(@Param("id") id): Promise<any> {
    let order_mode_id = Number(id);
    let data = await this.adminService.getOrderModeByID(order_mode_id);
    if (data.modeStoreId.length > 0) {
      let Array: any = [];
      data.modeStoreId.forEach(element => {
        let obj = {
          value: element.store_id.store_id,
          label: element.store_id.store_name
        };
        Array.push(obj);
      });
      data.stores_json = JSON.stringify(Array);
    }
    return { success: true, successResponse: data };
  }

  @Get("admin/updatePosCodeComboChoices")
  async updatePosCodeComboChoices(): Promise<any> {
    let choices = await this.adminService.getAllComboChoices();
    // return choices;
    if (choices.length > 0) {
      for (let i = 0; i < choices.length; i++) {
        const element = choices[i];
        element.combo_pos_code = element.combo_id && element.combo_id.pos_code;
        element.menu_variant_pos_code = element.size && element.size.pos_code;
        element.combo_is_publish =
          element.combo_id && element.combo_id.is_publish;
        element.combo_is_active =
          element.combo_id && element.combo_id.is_active;
        await this.adminService.updateComboChoicesPosCode(element.id, element);
      }

      return {
        length: choices.length,
        success: true,
        successResponse: choices
      };
    } else {
      return {
        length: choices.length,
        success: false,
        successResponse: choices
      };
    }
  }
  @Post("admin/home_counter_pos")
  async homeCounterPOS(
    @Req() request: Request,
    @Body() interval: any
  ): Promise<any> {
    //console.log("respoense: ", response);
    let token: any = request.headers.authorization?.split("Bearer ")[1];
    let jwtData: any = jwtDecode(token);
    let data: any;
    let store_ids = await this.adminService.getStores(jwtData.user_group_id);
    if (interval.days == 0) {
      data = await this.adminService.aggorderDataForhomeCounter(
        interval.days,
        store_ids
      );
    } else {
      data = await this.adminService.aggorderDataForhomeCounterWithDatetime(
        interval,
        store_ids
      );
    }
    if (data.length > 0) {
      let deliverySales = 0;
      let pickupSales = 0;

      for (let i = 0; i < data.length; i++) {
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].delivery_status == "Delivery"
        ) {
          deliverySales = deliverySales + data[i].order_grossprice;
        }
        if (
          (data[i].order_status_code == 5 || data[i].order_status_code == 6) &&
          data[i].delivery_status == "Pickup"
        ) {
          pickupSales = pickupSales + data[i].order_grossprice;
        }
      }
      let obj = [
        {
          deliverySales: deliverySales,
          pickupSales: pickupSales
        }
      ];
      return { success: true, successResponse: obj };
    } else {
      let obj = [
        {
          deliverySales: 0,
          pickupSales: 0
        }
      ];
      return { success: true, successResponse: obj };
    }
  }

  @Post("admin/save_make_a_combo")
  async saveMakeACombo(@Body() data: any): Promise<any> {
    let make_a_combo = {
      name: data.name,
      menu_id: data.menu_id,
      group_id: data.group_id,
      // menu_item_id:data.menu_item_id,
      type: data.type,
      is_active: 1
    };
    if (data.options && data.options.length > 0) {
      let success: any = await this.adminService.saveMakeACombo(make_a_combo);
      for (let i = 0; i < data.options.length; i++) {
        let option = data.options[i];
        let make_a_combo_options = {
          make_a_combo_id: success.id,
          group_id: option.group_id,
          menu_item_id: option.menu_item_id,
          variant_id: option.variant_id
        };
        await this.adminService.saveMakeAComboChoices(make_a_combo_options);
      }
      if (success) {
        return {
          success: true,
          successResponse: "Make A Combo Saved Successfully"
        };
      } else {
        throw new HttpException(
          {
            success: false,
            message: "Make A Combo Not Saved"
          },
          HttpStatus.FORBIDDEN
        );
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Make A Combo Options Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/make_a_combos_list")
  async getMakeACombosList() {
    let data: any = await this.adminService.makeAComboList();
    if (data && data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Data Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/make_a_combo/:id")
  async getMakeACombo(@Param("id") id) {
    let data: any = await this.adminService.getMakeACombo(id);
    if (data) {
      let options: any = await this.adminService.getMakeComboChoicesByMakeComboId(
        id
      );
      if (options && options.length > 0) {
        for (let i = 0; i < options.length; i++) {
          let itemByGroup: any = await this.adminService.GetItemsByGroup(
            options[i].group_id
          );
          let variantsByItem: any = await this.adminService.GetVariantsByItem(
            options[i].menu_item_id
          );
          Object.assign(options[i], { savedItem: itemByGroup });
          Object.assign(options[i], { savedVariant: variantsByItem });
        }
        Object.assign(data, { options: options });
      }
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Data Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Put("admin/block_unblock_make_a_combo/:id")
  async blockUnblockMakeACombo(@Param("id") id, @Body() data) {
    let value = data.is_active == 1 ? 0 : 1;
    let dataa: any = await this.adminService.blockUnblockMakeACombo(id, value);
    if (dataa) {
      return {
        success: true,
        successResponse: "Make A Combo Updated Successfully"
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Data Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Put("admin/edit-make-a-combo/:id")
  async editMakeACombo(@Param("id") id, @Body() body) {
    let data = {
      name: body.name,
      menu_id: body.menu_id,
      group_id: body.group_id,
      menu_item_id: body.menu_item_id,
      type: body.type
    };
    let removeJson: any = body.removeJson;
    let success: any = await this.adminService.updateMakeACombo(id, data);
    if (body.options && body.options.length > 0) {
      let savedOptions: any = await this.adminService.getMakeComboChoicesByMakeComboId(
        id
      );
      // await this.adminService.deleteMakeComboChoicesByMakeComboId(id)
      body.options.forEach(async (option: any) => {
        console.log("id", option.id);
        savedOptions.forEach(async (savedOption: any) => {
          if (savedOption.id == option.id) {
            let make_a_combo_options = {
              make_a_combo_id: id,
              group_id: option.group_id,
              menu_item_id: option.menu_item_id,
              variant_id: option.variant_id,
              is_active: 1
            };
            await this.adminService.updateComboChoices(
              savedOption.id,
              make_a_combo_options
            );
          }
        });
      });

      body.options.forEach(async (option: any) => {
        console.log("id2", option.id);
        // savedOptions.forEach(async (savedOption:any) => {
        if (option.id == "") {
          let make_a_combo_options = {
            make_a_combo_id: id,
            group_id: option.group_id,
            menu_item_id: option.menu_item_id,
            variant_id: option.variant_id
          };
          await this.adminService.saveMakeAComboChoices(make_a_combo_options);
        }
        // })
      });

      if (removeJson && removeJson.length > 0) {
        removeJson.map(async (removeOption: any) => {
          if (removeOption.id != "") {
            await this.adminService.updateMakeComboChoicesInactive(
              removeOption.id
            );
          }
        });
      }

      // savedOptions.forEach(async element => {
      //     let data =  body.options.find((obj:any) => {
      //         if (element.id == obj.id) {
      //             return obj
      //         };
      //     })
      //     if (!data) {
      //         let make_a_combo_options = {
      //             make_a_combo_id:id,
      //             group_id:element.group_id,
      //             menu_item_id:element.menu_item_id,
      //             variant_id:element.variant_id
      //          }
      //          await this.adminService.saveMakeAComboChoices(make_a_combo_options)
      //     }
      // })
      // for(let i = 0; i < body.options.length; i++) {
      //     let option = body.options[i]
      //     let make_a_combo_options = {
      //         make_a_combo_id:id,
      //         group_id:option.group_id,
      //         menu_item_id:option.menu_item_id,
      //         variant_id:option.variant_id
      //     }
      //     console.log("options",make_a_combo_options)
      //     await this.adminService.saveMakeAComboChoices(make_a_combo_options)
      // }
      if (success) {
        return {
          success: true,
          successResponse: "Make A Combo Saved Successfully"
        };
      } else {
        throw new HttpException(
          {
            success: false,
            message: "Make A Combo Not Saved"
          },
          HttpStatus.FORBIDDEN
        );
      }
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Make A Combo Options Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/menu_items/:groupid")
  async getItemsByGroupId(@Param("groupid") groupid): Promise<any> {
    let group_id = Number(groupid);
    let data = await this.adminService.GetItemsByGroup(group_id);
    if (data.length > 0) {
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Items Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/menu_item_variants/:itemid")
  async getItemVariantsByGroupId(@Param("itemid") itemid): Promise<any> {
    let item_id = Number(itemid);
    let data = await this.adminService.GetVariantsByItem(item_id);
    if (data.length > 0) {
      data.map((info: any) => {
        Object.assign(info, {
          value: info.id,
          label: `${info.size} - ${info.item_name}`
        });
      });
      return { success: true, successResponse: data };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Item Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  @Post("admin/ping")
  async pingIp(@Res() res: Response, @Body() data: any): Promise<any> {
    // const replyFromLocale = "Reply from";// command for local machine
    const bytesFromLocale = "bytes from"; // command for linux machine like putty server
    // const promises: any = [];
    // Refrence Video
    // https://www.youtube.com/watch?v=fjmlRrf0MDY
    // timout reference link
    // https://mattsumme.rs/2015/nodejs-child-process-timeouts/
    // promises.push(new Promise(async (resolve, _reject) => {
    //     await exec(`ping -n 1 -w 1000 ${data.ip_address}`, {
    //         timeout: 1000
    //     }, (_err, stdout, _stderr) => {
    //         // successfull response: Reply from **************: bytes=32 time=10ms TTL=128
    //         // unsuccessfull:        Reply from ***********: Destination host unreachable
    //         // host unreachable:     Ping request could not find host
    //         let status = "Fail";
    //         let loadTime = 0;
    //         let output = stdout.toString();
    //         let replyFromIndex = output.indexOf(replyFromLocale);
    //         if (replyFromIndex > 0 && output.substring(replyFromIndex).toUpperCase().indexOf("BYTES") > 0) {
    //             status = "Success";
    //             loadTime = output.substring(replyFromIndex).toUpperCase().indexOf("TIME")
    //         }
    //         resolve(new Date().toISOString() + "/" + status + "/" + loadTime + "/" + data.ip_address)
    //     })
    // }))

    // Promise.all(promises).then((results: any) => {
    //     let data = results[0].split("/");
    //     res.status(200).send({ 'Status': data[1], "Time": data[2], "Address": data[3], })
    // })
    // ping -n 1 ${data.ip_address}   command for local machine
    // ping -c 1 ${data.ip_address}   command for linux machine like putty server
    await exec(
      `ping -c 1 ${data.ip_address}`,
      {
        timeout: 1000
      },
      (_err, stdout, _stderr) => {
        // successfull response: Reply from **************: bytes=32 time=10ms TTL=128
        // unsuccessfull:        Reply from ***********: Destination host unreachable
        // host unreachable:     Ping request could not find host
        console.log("_err", _err);
        console.log("stdout", stdout);
        console.log("_stderr", _stderr);
        let status = "Fail";
        let loadTime = 0;
        let output = stdout.toString();
        // let replyFromIndex = output.indexOf(replyFromLocale);  // command for local machine
        let bytesFromIndex = output.indexOf(bytesFromLocale); // command for linux machine like putty server
        if (bytesFromIndex > 0) {
          status = "Success";
          let loadtimeIndex = output
            .substring(bytesFromIndex)
            .toUpperCase()
            .indexOf("TIME=");
          loadTime = output
            .substring(bytesFromIndex)
            .substring(loadtimeIndex + 5, 58); // command for linux machine like putty server
        }
        res
          .status(200)
          .send({ Status: status, Time: loadTime, Address: data.ip_address });
      }
    );
  }

  async AggAuthTOken() {
    let data = qs.stringify({
      grant_type: "client_credentials",
      password: process.env.FP_PASSWORD,
      username: process.env.FP_USERNAME
    });
    const res = await axios({
      method: "post",
      maxBodyLength: Infinity,
      url: "https://integration-middleware.as.restaurant-partners.com/v2/login",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      data: data
    });
    if (res.data) {
      return res.data.access_token;
    } else {
      console.log("Err while fetching token", res);
    }
  }

  @Post("admin/fpmenulogs")
  async fpmenulogs(@Res() res: Response, @Body() data: any): Promise<any> {
    let aggAuthToken = await this.AggAuthTOken();

    const { fp_branch_code } = data;
    console.log("data: ", data);

    axios
      .get(
        `https://integration-middleware.as.restaurant-partners.com/v2/chains/${process.env.FP_CHAIN_CODE}/vendors/${fp_branch_code}/menu-import-logs`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${aggAuthToken}`
          }
        }
      )
      .then(async (response: any) => {
        if (response.data) {
          res.status(200).send({
            success: true,
            data: response.data[fp_branch_code],
            successResponse: `Data Ping Successfully`
          });
        }
      })
      .catch(async (err: any) => {
        res.status(403).send({
          success: false,
          message: `${JSON.stringify(err.response.data.message)}`
        });
      });
  }

  @Get("admin/updateAllModifiersFromVariants")
  async updateAllModifiersFromVariants(): Promise<any> {
    let data: any = await this.adminService.getAllVariantsWithMenuItem();
    let modifiersArr: any = [];
    if (data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        const element = data[i];
        const modObj = await this.adminService.getModifiersByPosCode(
          element.pos_code
        );
        if (modObj == undefined || modObj == null) {
          let modifier: any = {
            modifier_name: element?.menu_item_id?.item_name + element?.size,
            erp_id: element?.erp_id,
            pos_code: element?.pos_code,
            isFoodPanda: 1
          };
          modifiersArr.push(modifier);
        }
      }
      if (modifiersArr.length > 0) {
        await this.adminService.addModifier(modifiersArr);
      }
      console.log("modifiersArr.length", modifiersArr.length);
      console.log("data.length", data.length);

      return {
        success: true,
        successResponse: modifiersArr,
        variantsData: data
      };
    } else {
      throw new HttpException(
        {
          success: false,
          message: "Modifier Not Found"
        },
        HttpStatus.FORBIDDEN
      );
    }
  }

  @Get("admin/force_update_option")
  async IOSUpdateOPTION(@Res() res: Response) {
    let data = await this.adminService.getBrandById(1);
    res.status(HttpStatus.OK).send({
      statusCode: HttpStatus.OK,
      force_update: data.is_app_live ? true : false,
      ios_force_update: data.is_ios_live ? true : false,
      is_google_login: data.is_google_login ? true : false,
      is_fb_login: data.is_fb_login ? true : false,
      is_apple_login: data.is_apple_login ? true : false,
      is_delete_button: data.is_delete_button ? true : false
    });
  }

  @Post("admin/save_mov")
  async saveMov(
    @Headers() headers,
    @Body() data: { mov: string; sales_channel_id: number },
    @Res() res: Response
  ): Promise<any> {
    const token: string = headers.authorization?.split("Bearer ")[1] || "";
    const jwtData: {
      role: string;
      first_name: string;
      last_name: string;
    } = jwtDecode(token);
    const success = await this.adminService.saveMov(data);
    if (!success) {
      res
        .status(HttpStatus.BAD_REQUEST)
        .send({ success: false, message: "MOV Not Saved" });
    }
    await this.loggingService.saveLogs(
      jwtData,
      "Update MOV",
      "MOV",
      `Amount : ${data.mov}`
    );
    res
      .status(HttpStatus.CREATED)
      .send({ success: true, successResponse: "MOV Saved Successfully" });
  }

  @Post("admin/update-global-conf")
  async updateGlobalConfiguration(
    @Headers() headers,
    @Body() data: any
  ): Promise<any> {
    const token: any = headers.authorization?.split("Bearer ")[1];
    const jwtData: any = jwtDecode(token);

    const success = await this.adminService.editBrand(1, data);
    if (!success) {
      throw new HttpException(
        {
          success: false,
          message: "Configuration not updated"
        },
        HttpStatus.NOT_FOUND
      );
    }
    const logData: any = {
      role: jwtData.role,
      user_name: jwtData.first_name + " " + jwtData.last_name,
      reason: "Update",
      activity_type: "Global Configuration",
      activity_name:
        data.is_app_live >= 0
          ? `Android Force Update: ${data.is_app_live}`
          : data.is_ios_live >= 0
          ? `IOS Force Update: ${data.is_ios_live}`
          : data.is_google_login >= 0
          ? `Google Login Button: ${data.is_google_login}`
          : data.is_fb_login >= 0
          ? `FB Login Button: ${data.is_fb_login}`
          : data.is_apple_login >= 0
          ? `Apple Login Button: ${data.is_apple_login}`
          : data.is_delete_button >= 0
          ? `Delete Account Button: ${data.is_delete_button}`
          : data.is_cash_disable >= 0
          ? `Cash Payment Method: ${data.is_cash_disable}`
          : data.is_card_disable >= 0
          ? `Card Payment Method: ${data.is_card_disable}`
          : data.is_jazz_disable >= 0
          ? `Jazz Payment Method: ${data.is_jazz_disable}`
          : data.is_dds_map_address >= 0
          ? `DDS Maps Address: ${data.is_dds_map_address}` ? data.is_google_maps_enable >= 0
          : `Map provider manara enable: ${data.is_google_maps_enable}`
          : "",
      status: "Update Global Configuration"
    };
    await this.adminService.addLogs(logData);
    return {
      success: true,
      successResponse: `${
        data.is_app_live >= 0
          ? "Android Force Update"
          : data.is_ios_live >= 0
          ? "IOS Force Update"
          : data.is_google_login >= 0
          ? `Google Login Button`
          : data.is_fb_login >= 0
          ? `FB Login Button`
          : data.is_apple_login >= 0
          ? `Apple Login Button`
          : data.is_delete_button >= 0
          ? `Delete Account Button`
          : data.is_cash_disable >= 0
          ? `Cash Payment Method`
          : data.is_card_disable >= 0
          ? `Card Payment Method`
          : data.is_jazz_disable >= 0
          ? `Jazz Payment Method`
          : data.is_dds_map_address >= 0
          ? `DDS Maps Address`
          : ""
      } Updated Successfully`
    };
  }
  @Post("sync_menu_with_store")
  async syncAggregatorMenuWithStore(@Body() data): Promise<any> {
    try {
      const { store_id, brand_id } = data;
      // this object's value's change
      const storeMenu: any = {};
      const mainMenu: any = {};
      const menuID: any = await this.adminService.getMenuById(brand_id);

      if (menuID) {
        let groups = await this.adminService.getStoreGroups(store_id);
        let subgroups = await this.adminService.getStoreSubGroups(store_id);
        let items = await this.adminService.getStoreItems(store_id);
        //let modGroup    =   await this.adminService.getStoreModGroups(store_id)
        let modifier = await this.adminService.getStoreModifiers(store_id);
        let combo = await this.adminService.getStoreCombos(store_id);
        storeMenu.menu_groups = groups;
        storeMenu.menu_items = items;
        //storeMenu.menu_modGroup    =   modGroup;
        storeMenu.menu_modifier = modifier;
        storeMenu.menu_subgroups = subgroups;
        storeMenu.menu_combo = combo;

        let mainMenu_groups = await this.adminService.getAllGroups();
        let mainMenu_items = await this.adminService.getAllItems();
        //let mainMenu_modGroup          = await this.adminService.getAllModGroups()
        let mainMenu_modifier = await this.adminService.getAllModifiers();
        let mainMenu_subGroups = await this.adminService.getAllSubGroups();
        let mainMenu_combos = await this.adminService.getAllCombos();

        mainMenu.mainMenu_groups = mainMenu_groups;
        mainMenu.mainMenu_items = mainMenu_items;
        //mainMenu.mainMenu_modGroup    = mainMenu_modGroup;
        mainMenu.mainMenu_modifier = mainMenu_modifier;
        mainMenu.mainMenu_subGroups = mainMenu_subGroups;
        mainMenu.mainMenu_combos = mainMenu_combos;
        let newData = await this.comparing(storeMenu, mainMenu, store_id);
        let message = "";
        if (
          newData.groups.length == 0 &&
          newData.items.length == 0 &&
          newData.modgroups.length == 0 &&
          newData.modifiers.length == 0 &&
          newData.combos.length == 0 &&
          newData.subgroups.length == 0
        ) {
          message = "Store Menu already up to date";
        } else {
          message = `Store Menu updated with new data: \n ${newData.groups.length} new groups, \n ${newData.items.length} new items,\n ${newData.modgroups.length} new modgroups, \n ${newData.modifiers.length} new modifiers,\n ${newData.subgroups.length} new subgroups, \n ${newData.combos.length} new combos  `;
        }

        return { success: true, successResponse: message };
      } else {
        throw new HttpException(
          {
            success: false,
            message: "Menu Not Found"
          },
          HttpStatus.FORBIDDEN
        );
      }
    } catch (e) {
      throw new HttpException(
        {
          success: false,
          message: e.message
        },
        HttpStatus.FORBIDDEN
      );
    }
  }
  async comparing(store_menu, main_menu, store_id) {
    let result = {
      groups: [] as any,
      items: [] as any,
      modgroups: [] as any,
      modifiers: [] as any,
      combos: [] as any,
      subgroups: [] as any
    };
    for (let i = 0; i < main_menu.mainMenu_groups.length; i++) {
      let main_menu_Group: any = main_menu.mainMenu_groups[i];
      let isGroupPresent = store_menu.menu_groups.some(
        item => item.group_id === main_menu_Group.group_id
      );
      if (!isGroupPresent) {
        let store_group: any = {};
        store_group.group_id = main_menu_Group.group_id;
        store_group.store_id = store_id;
        store_group.is_active = 1;
        result.groups.push(store_group);
      }
    }

    if (result.groups.length > 0) {
      await this.adminService.addStoresForMenu(result.groups);
    }
    for (let i = 0; i < main_menu.mainMenu_items.length; i++) {
      let main_menu_Item: any = main_menu.mainMenu_items[i];
      let isMenuItemPresent = store_menu.menu_items.some(
        item => item.menu_item_id === main_menu_Item.menu_item_id
      );
      if (!isMenuItemPresent) {
        let store_item: any = {};
        store_item.menu_item_id = main_menu_Item.menu_item_id;
        store_item.store_id = store_id;
        store_item.is_active = 1;
        result.items.push(store_item);
      }
    }
    if (result.items.length > 0) {
      await this.adminService.addStoresForMenu(result.items);
    }
    // this can be needed in the future, and is not present in the prev commits
    /*for(let i = 0; i < aggregator.aggregator_modGroup.length; i++) {
    
            let aggregatorModGroup: any = aggregator.aggregator_modGroup[i];
            let isModGroupPresent = store_menu.menu_modGroup.some((item => item.modifier_group_id === aggregatorModGroup.mod_group_id && item.aggregator_id == agg_id));
            if (!isModGroupPresent) {
                let store_ModGroup : any =  {}
                store_ModGroup.modifier_group_id = aggregatorModGroup.mod_group_id
                store_ModGroup.aggregator_id = agg_id
                store_ModGroup.store_id = store_id
                store_ModGroup.is_active = 1
                result.modgroups.push(store_ModGroup);
               
            }
        }
        await this.adminService.addStoresForMenu(result.modgroups)
        */
    for (let i = 0; i < main_menu.mainMenu_modifier.length; i++) {
      let main_menu_Modifier: any = main_menu.mainMenu_modifier[i];
      let isModifierPresent = store_menu.menu_modifier.some(
        item => item.modifier_id === main_menu_Modifier.modifier_id
      );
      if (!isModifierPresent) {
        let store_Modifier: any = {};
        store_Modifier.modifier_id = main_menu_Modifier.modifier_id;
        store_Modifier.store_id = store_id;
        store_Modifier.is_active = 1;
        result.modifiers.push(store_Modifier);
      }
    }
    if (result.modifiers.length > 0) {
      await this.adminService.addStoresForMenu(result.modifiers);
    }
    for (let i = 0; i < main_menu.mainMenu_combos.length; i++) {
      let main_menu_combo: any = main_menu.mainMenu_combos[i];
      let isComboPresent = store_menu.menu_combo.some(
        item => item.combo_id === main_menu_combo.combo_id
      );
      if (!isComboPresent) {
        let store_Combo: any = {};
        store_Combo.combo_id = main_menu_combo.combo_id;
        store_Combo.store_id = store_id;
        store_Combo.is_active = 1;
        result.combos.push(store_Combo);
      }
    }
    if (result.combos.length > 0) {
      await this.adminService.addStoresForMenu(result.combos);
    }
    for (let i = 0; i < main_menu.mainMenu_subGroups.length; i++) {
      let main_menu_SubGroup: any = main_menu.mainMenu_subGroups[i];
      let isSubGroupPresent = store_menu.menu_subgroups.some(
        item => item.subgroup_id === main_menu_SubGroup.id
      );
      if (!isSubGroupPresent) {
        let store_subGroup: any = {};
        store_subGroup.subgroup_id = main_menu_SubGroup.id;
        store_subGroup.store_id = store_id;
        store_subGroup.is_active = 1;
        result.subgroups.push(store_subGroup);
      }
    }
    if (result.subgroups.length > 0) {
      await this.adminService.addStoresForMenu(result.subgroups);
    }
    return result;
  }
}
