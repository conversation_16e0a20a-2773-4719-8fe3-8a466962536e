// worker-task.js (or .ts if using TypeScript)
const { workerData, parentPort } = require("worker_threads");

// Example of a CPU-intensive task
function performHeavyComputation(limit) {
  // Simulate heavy computation
  let counter = 0;
  for (let i = 0; i < limit; i++) {
    counter++;
  }
  return counter;
}

console.log("workerData", workerData);
const result = performHeavyComputation(workerData.limit);
parentPort.postMessage(result);
