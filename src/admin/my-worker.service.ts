import { Injectable } from "@nestjs/common";
import { Worker } from "worker_threads";

@Injectable()
export class WorkerService {
  async executeTaskInWorker(data: any): Promise<number> {
    return new Promise((resolve, reject) => {
      const worker = new Worker("./src/admin/worker-task.ts", {
        workerData: data
      });

      // Listen for messages from the worker
      worker.on("message", result => resolve(result));

      // Handle errors in the worker
      worker.on("error", error => reject(error));

      // Handle worker exit
      worker.on("exit", code => {
        if (code !== 0) {
          reject(new Error(`Worker stopped with exit code ${code}`));
        }
      });
    });
  }
}
